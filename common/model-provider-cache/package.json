{"name": "@mlworkspace/model-provider-cache", "version": "0.1.0", "private": true, "license": "UNLICENSED", "sideEffects": false, "type": "module", "files": ["dist"], "scripts": {"build": "yarn clean && cross-env NODE_OPTIONS=--max_old_space_size=16384 vite build", "build-cache": "node ./generate-cache", "build-cache-int": "node ./generate-cache --int", "build-cache-int:ai-studio": "node ./generate-cache --ai-studio --int", "build-cache:ai-studio": "node ./generate-cache --ai-studio", "build-split": "yarn build", "clean": "rimraf tsconfig.tsbuildinfo dist", "clean-cache": "node ./cleancache.js", "generate": "jest --passWithNoTests --no-coverage", "lint": "concurrently \"yarn:lint:*\"", "lint:es": "cross-env-shell NODE_OPTIONS=--max_old_space_size=32768 eslint src --ext .ts,.tsx,.js,.jsx --quiet", "lint:style": "stylelint \"src/**/*.scss\" --allow-empty-input", "lintfix": "concurrently \"yarn:lint:es --cache --cache-strategy content --fix\" \"yarn:lint:style --cache --fix\"", "quicktest": "jest --passWithNoTests", "test": "node ./generate-cache --force", "test-debug": "jest --passWithNoTests", "test-int:ai-studio": "node ./generate-cache --force --ai-studio --int", "test:ai-studio": "node ./generate-cache --force --ai-studio"}, "babel": {"presets": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@fluentui/jest-serializer-merge-styles": "8.0.46", "@fluentui/react-icons": "^2.0.261", "@mlworkspace/code-common": "^0.1.0", "@mlworkspace/model-provider-cache-types": "^0.1.0", "@testing-library/jest-dom": "5.17.0", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "8.0.1", "@testing-library/user-event": "14.6.1", "react-test-renderer": "17.0.2"}, "peerDependencies": {"@fluentui/react-icons": "^2.0.261", "@mlworkspace/code-common": "^0.1.0", "@mlworkspace/model-provider-cache-types": "^0.1.0", "bowser": "^2.9.0", "he": "^1.2.0", "history": "^4.7.2", "lodash-es": "4.17.21", "moment": "^2.29.4", "pako": "~1.0.5", "polly-js": "^1.6.3", "prop-types": "15.8.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-query": "3.39.2", "react-router-dom": "^5.3.4", "rxjs": "6.5.2", "string-hash": "1.1.3", "tslib": "2.8.1", "uuid": "11.0.4"}}
﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\3\s\common\finetune-core\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="ru-RU" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";FinetuneWizardHyperparameterSettings.ChoiceGroupOptions.Value.Custom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Custom]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Настраиваемое]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.ChoiceGroupOptions.Value.Default" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Default]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[По умолчанию]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.ChoiceGroupOptions.Value.Random" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Random]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Случайный выбор]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.BatchSize.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Размер пакета]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.BatchSize.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch size (1-32)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Размер пакета (1-32)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.BatchSize.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The batch size to use for training. When set to default, batch_size is calculated as 0.2% of examples in training set and the max is 32.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Размер пакета, используемый для обучения. Если настроено значение по умолчанию, batch_size вычисляется как 0,2 % от примеров в обучающем наборе, а максимальное значение — 32.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The batch size to use for training. When set to default, batch_size is calculated as 0.2% of examples in training set and the max is 256.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Beta.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Beta]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Бета-версия]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Beta.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Beta (0.1-2.0)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Бета-версия (0.1-2.0)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Beta.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The beta parameter for Direct Preference Optimization(DPO) method. A higher beta value will increase the weight of the KL penalty between the policy and reference model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Параметр бета для метода прямой оптимизации предпочтений (DPO). Более высокое значение бета увеличит вес штрафа KL между политикой и эталонной моделью.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationBetas.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification betas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Бета-коэффициенты классификации]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationBetas.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If this is provided, we calculate F-beta scores at the specified beta values. The F-beta score is a generalization of F-1 score. This is only used for binary classification. With a beta of 1 (i.e. the F-1 score), precision and recall are given the same weight. A larger beta score puts more weight on recall and less on precision. A smaller beta score puts more weight on precision and less on recall. The value specified should be a comma separated list of doubles.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Если это предусмотрено, мы рассчитываем оценки F-бета при указанных значениях бета. Показатель F-бета представляет собой обобщение показателя F-1. Используется только для двоичной классификации. При значении бета-версии 1 (т.е. оценке F-1) точность и полнота имеют одинаковый вес. Более высокая оценка бета-версии больше влияет на полноту и меньше на точность. Меньшая оценка бета-версии больше влияет на точность и меньше — на полноту. Указанное значение должно представлять собой список чисел двойной точности, разделенных запятыми.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationNClasses.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification n classes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Классы n классификации]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationNClasses.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of classes in a classification task. This parameter is required for multiclass classification.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Число классов в задаче классификации. Этот параметр необходим для классификации с несколькими классами.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationPositiveClass.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification positive class]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Положительный класс классификации]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationPositiveClass.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The positive class in binary classification. This parameter is needed to generate precision, recall, and F1 metrics when doing binary classification.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Положительный класс в двоичной классификации. Этот параметр необходим для генерации метрик точности, полноты и F1 при выполнении двоичной классификации.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeClassificationMetrics.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute classification metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Метрики классификации вычислений]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeClassificationMetrics.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If set, we calculate classification-specific metrics such as accuracy and F-1 score using the validation set at the end of every epoch. In order to compute classification metrics, you must provide a validation_file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Если значение задано, мы рассчитываем метрики, специфичные для классификации, такие как точность и оценка F-1, используя набор данных для проверки в конце каждой эпохи. Для вычисления метрик классификации необходимо предоставить файл_проверки.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeMultiplier.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 0.5-3.0 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[В большинстве случаев рекомендуется диапазон от 0,5 до 3,0.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeMultiplier.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute multiplier]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Множитель вычислений]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeMultiplier.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute multiplier (0.5-3.0)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Множитель вычислений (0,5-3,0)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeMultiplier.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multiplier on amount of compute use for exploring search space during training.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Множитель объема используемых вычислительных ресурсов для исследования пространства поиска во время обучения.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalInterval.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 1-25 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[В большинстве случаев рекомендуется диапазон от 1 до 25.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalInterval.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation interval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Интервал оценки]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Eval interval]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalInterval.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation interval (1-25)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Интервал оценки (1-25)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalInterval.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of training steps between evaluations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Количество шагов обучения между оценками.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalSamples.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 1-10 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[В большинстве случаев рекомендуется диапазон от 1 до 10.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalSamples.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Samples for evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Примеры для оценки]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Eval samples]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalSamples.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Samples for evaluation (1-10)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Примеры для оценки (1–10)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalSamples.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of samples to use during evaluation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Количество примеров, используемых во время оценки.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.CreateText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Создать]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.ErrorTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error while generating]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ошибка при создании]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.FreeBeta" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Free beta]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Бесплатная бета-версия]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.GenerateText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Генерировать]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.GraderPlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Describe how you want the response to be graded, and we'll generate a grader.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Опишите, как следует оценивать ответ, и мы создадим оценщик.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.ResponsePlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Describe how you want the model to respond, and we'll generate a JSON schema.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Опишите, как модели следует отвечать, и мы создадим схему JSON.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Grader.EmptyError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grader schema is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Требуется схема оценщика]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Grader.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grader schema]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Схема оценщика]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Grader.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grader schema]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Схема оценщика]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Grader.ValidatingSchema" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating schema...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Проверка схемы...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.LearningRateMultiplier.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 0.0001-10.0 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[В большинстве случаев рекомендуется диапазон от 0,0001 до 10.0.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.LearningRateMultiplier.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learning rate multiplier]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Множитель скорости обучения]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.LearningRateMultiplier.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learning rate multiplier (0.0-10.0)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Множитель скорости обучения (0,0–10,0)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.LearningRateMultiplier.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The fine-tuning learning rate is the original learning rate used for pre-training multiplied by this multiplier. We recommend experimenting with values between 0.5 and 2. Empirically, we've found that larger learning rates often perform better with larger batch sizes. Must be between 0.0 and 10.0.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Скорость обучения с тонкой настройкой — это исходная скорость обучения, используемая для предварительного обучения, умноженная на этот множитель. Рекомендуется экспериментировать со значениями от 0,5 до 2. Опытным путем обнаружено, что более высокая скорость обучения часто работает лучше при больших размерах пакетов. Значение должно быть в диапазоне от 0,0 до 10,0.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The fine-tuning learning rate is the original learning rate used for pre-training multiplied by this multiplier. We recommend experimenting with values between 0.5 and 2. Empirically, we've found that larger learning rates often perform better with larger batch sizes. Must be between 0.0 and 5.0.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.NumberOfEpochs.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 1-10 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[В большинстве случаев рекомендуется диапазон от 1 до 10.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.NumberOfEpochs.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of epochs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Количество эпох]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.NumberOfEpochs.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of epochs (1-10)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Количество эпох (1–10)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Number of epochs (1-5)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.NumberOfEpochs.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of training epochs. An epoch refers to one full cycle through the data set. If set to default, number of epochs will be determined dynamically based on the input data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Количество эпох обучения. Эпоха указывает на один полный цикл в рамках набора данных. Если настроено значение по умолчанию, количество эпох будет определяться динамически на основе входных данных.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.PromptLossWeight.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt loss weight]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Вес потери запроса]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.PromptLossWeight.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The prompt loss weight to use for training]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Быстрая потеря веса для обучения]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ReasoningEffort.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning effort]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Работа с аргументами]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ReasoningEffort.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning effort]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Работа с аргументами]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ResponseFormat.EmptyError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Response format schema is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Требуется схема формата ответа]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ResponseFormat.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Response format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Формат отклика]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ResponseFormat.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The response format can be added as null if there is no valid schema.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Формат ответа можно указать как null, если нет действительной схемы.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Seed.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Seed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Начальное значение]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Seed.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Seed (0-2147483647)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Начальное значение (0-2147483647)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Seed.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The seed controls the reproducibility of the job. Passing in the same seed and job parameters should produce the same results, but may differ in rare cases. If a seed is not specified, one will be generated for you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Начальное значение управляет возможностью воспроизведения задания. Передача одинакового начального значения и параметров задания должна приводить к одинаковым результатам, но в редких случаях результаты могут отличаться. Если начальное значение не указано, оно будет создано для вас.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.AoaiFilesError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to retrieve files:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Сбой извлечения файлов:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.arcScienceMcqSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grade-school multiple-choice science questions requiring advanced reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Вопросы с несколькими вариантами ответа по естественным наукам для начальной школы, требующие углубленного мышления]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.arcScienceMcqSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[arc_science_mcq_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[arc_science_mcq_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.arcScienceMcqSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scientific reasoning and QA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Научные рассуждения и вопросы с ответами]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.chatTrainSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grade school math problems requiring multi-step arithmetic reasoning and calculation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Математические задачи для начальной школы, требующие многошагового арифметического мышления и вычислений]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.chatTrainSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[textGSM8K_train_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[textGSM8K_train_sample]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[chat_train_sample]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.chatTrainSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mathematical reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Математическое рассуждение]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Mathematical reasoning safety training]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.cuadLegalContractsSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Labeled commercial contracts with 41 important legal clause types]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Помеченные коммерческие контракты с 41 важным типом юридических положений]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.cuadLegalContractsSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[cuad_legal_contracts_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[cuad_legal_contracts_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.cuadLegalContractsSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Legal agent for contract analysis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Юридический агент для анализа контрактов]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Legal contract clause identification]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.helpText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get started with a pre-prepared sample dataset to help you understand how to format data for fine-tuning and test end-to-end workloads. These datasets are intended as examples; they are not intended to produce production quality models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Начните работу с предварительно подготовленным образцом набора данных, который поможет вам понять, как форматировать данные для точной настройки и тестирования сквозных рабочих нагрузок. Эти наборы данных приведены в качестве примеров; они не предназначены для создания качественных моделей производства.]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Each dataset contains professionally curated examples designed for optimal fine-tuning results, supporting a wide range of model capabilities and advanced safety alignment.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.medMcqaHealthcareSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multiple-choice medical exam questions covering diverse healthcare topics and subjects]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Вопросы с несколькими вариантами ответа для экзамена по медицине, охватывающие различные темы здравоохранения]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.medMcqaHealthcareSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[medMCQA_healthcare_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[medMCQA_healthcare_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.medMcqaHealthcareSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assistive medical agent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Вспомогательный медицинский агент]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Medical education and assessment]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.openOrcaDPOSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Imitation learning data with step-by-step explanations from GPT-4 and GPT-3.5]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные по имитационному обучению с пошаговыми объяснениями от GPT-4 и GPT-3.5]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.openOrcaDPOSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[open_orca_dpo_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[open_orca_dpo_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.openOrcaDPOSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Imitative reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Имитационное рассуждение]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.stockToolCallingSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Current and previous stock market queries data, covering price retrieval using tool calls]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные текущих и предыдущих запросов для фондового рынка, включая получение цен с помощью вызовов инструментов]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.stockToolCallingSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[stock_tool_calling_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[stock_tool_calling_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.stockToolCallingSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Agent for stock market monitoring]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Агент для мониторинга фондового рынка]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Curated Sample Datasets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Курированные примеры наборов данных]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.vizWizVisualQnaSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visual questions from blind users with images and spoken queries]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Визуальные вопросы от незрячих пользователей с изображениями и голосовыми запросами]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.vizWizVisualQnaSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[vizWiz_visual_qna_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[vizWiz_visual_qna_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.vizWizVisualQnaSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assistive vision agent for the visually impaired]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Вспомогательный агент зрения для людей с нарушением зрения]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Assistive technology for blind users]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CustomFileListMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The following file types are supported: {acceptedFileExtensions}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Поддерживаются следующие типы файлов: {acceptedFileExtensions}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.ReviewDataDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Preview of top 3 rows from your dataset (Total: {totalRowsContent} rows)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Предварительный просмотр трех верхних строк из набора данных (всего строк: {totalRowsContent})]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.ReviewDataInfo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the preview of the data you have selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Это предварительный просмотр выбранных данных]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.TestDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Test data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Тестовые данные]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.TrainingDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Training data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Обучающие данные]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.ValidationDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные проверки]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.DatasetRequirements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What are the data requirements?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Каковы требования к данным?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose a file to upload from your local drive. File must be under 200 MB.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Выберите файл для отправки с локального диска. Размер файла должен быть меньше 200 МБ.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Error" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ошибка]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.IncorrectColumnMappingMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your data must include columns with the following labels: {columnLabels}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные должны включать столбцы со следующими метками: {columnLabels}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.IncorrectFileTypeError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File is not of a supported type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Тип файла не поддерживается]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.IncorrectFileTypeEvaluating" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File is not of a supported type. Evaluation jobs support JSON Lines files only.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Тип файла не поддерживается. Задания оценки поддерживают только файлы JSON Lines.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.IncorrectFileTypeFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File is not of a supported type. Fine-tuning jobs support csv, tsv, and JSON Lines formats.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Тип файла не поддерживается. Задания тонкой настройки поддерживают форматы CSV, TSV и JSON Lines.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.TrainingDataMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Training data is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Требуются обучающие данные]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileImportError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File import error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ошибка при импорте файла]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileImportSuccessful" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File import successful]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Импорт файла выполнен]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileList" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The supported file types are csv, tsv, and JSON Lines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Поддерживаемые типы файлов: CSV, TSV и JSON Lines.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileLocation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Files will be uploaded to the default datastore and made available in your workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Файлы будут отправлены в стандартное хранилище данных и станут доступны в вашей рабочей области.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileUploadError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File upload error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ошибка при отправке файла]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.AOAILearnMoreMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about data preparation for fine-tuning OpenAI models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Узнайте больше о подготовке данных для тонкой настройки моделей OpenAI.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.CnnDailyMail" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CNN daily mail]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CNN DAILY MAIL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Conll2003Dataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CoNLL2003 dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Набор данных CoNLL2003]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.CustomFileListMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The following file types are supported: {acceptedFileExtensions}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Поддерживаются следующие типы файлов: {acceptedFileExtensions}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.DatasetRequirements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What are the data requirements?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Каковы требования к данным?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Emotion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Emotion]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Распознавание эмоций]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationFillMaskDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The fill mask data is expected to have 2 fields – input_string (which contains the article with the masked(eg. [MASK]5D;, <mask>) tokens), title (which contains the actual word which fills the mask) like shown below. The below samples are borrowed from the {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные маски заполнения должны содержать 2 поля: входная_строка (содержит статью с замаскированными (например, токенами [MASK]5D;, <mask>)) и название (содержит фактическое слово, заполняющее маску), как показано ниже. Приведенные ниже примеры взяты из набора данных {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationQuestionAnsweringDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Extractive Question Answering data should contain 3 fields – question, context and answers field. The below table contains couple of samples from {link} dataset. The data in answers field should contain a string answer text like in the example below.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные для извлечения ответов на вопросы должны содержать 3 поля: поле вопроса, контекста и ответов. Приведенная ниже таблица содержит несколько примеров из набора данных {link}. Данные в поле ответов должны содержать строковый текст ответа, как в приведенном ниже примере.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationSummarizationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The summarization data is expected to have 2 fields – input_string (which contains the article), summary like shown below. The below samples are borrowed from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные для сводки должны содержать 2 поля: входная_строка (содержит статью) и сводка, как показано ниже. Ниже приведены примеры, взятые из набора данных {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationTextClassificationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[When evaluating your model for text classification, you could perform either single text classification or text pair classification. Text classification requires the evaluation data to include at least 2 fields – one for ‘input_string’ (which contains the input text) and ‘label’ like in this example. The below examples are from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[При оценке модели для классификации текстов можно выполнить одну классификацию текста или классификацию текстовых пар. Классификация текста требует, чтобы данные оценки включали по крайней мере 2 поля: одно для input_string (содержит входной текст) и label, как в этом примере. Приведенные ниже примеры взяты из набора данных {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationTextGenerationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The text generation data is expected to have 2 fields – input_string (which contains the context for generating further text), ground_truth (which contains the entire text article). The below samples are borrowed from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные для генерирования текста должны содержать 2 поля: входная_строка (содержит контекст для генерирования последующего текста) и истина_для_граундинга (содержит всю текстовую статью). Ниже приведены примеры, взятые из набора данных {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationTokenClassificationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification requires the evaluation data to include 2 fields, ‘input_string’ (which contains the input tokens) and ‘ner_tags_str’ (which contains the array of tags in string format) like in this example. The below examples are taken from {link}. Please note that the NER tags should be passed as space separated string. The Tags should be passed as a string literal of an array of string tags. Tag list for below - {'O': 0, 'B-PER': 1, 'I-PER': 2, 'B-ORG': 3, 'I-ORG': 4, 'B-LOC': 5, 'I-LOC': 6, 'B-MISC': 7, 'I-MISC': 8}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Классификация токенов требует, чтобы данные оценки включали 2 поля: input_string (содержит входные токены) и ner_tags_str (содержит массив тегов в строковом формате), как в этом примере. Приведенные ниже примеры взяты из {link}. Обратите внимание, что теги NER должны передаваться в виде строки, разделенной пробелами. Теги должны передаваться в виде строкового литерала массива строковых тегов. Список тегов для примера ниже - {'O': 0, 'B-PER': 1, 'I-PER': 2, 'B-ORG': 3, 'I-ORG': 4, 'B-LOC': 5, 'I-LOC': 6, 'B-MISC': 7, 'I-MISC': 8}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationTranslationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The translation data should have 2 fields – input_string (which contains the source language) and target language (ex. “ro”) like in the example below. The field names that map to source and target languages need to be language codes supported by the model. Please refer to the model card for details on supported languages.  The below examples are sampled from {link}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные для перевода должны содержать 2 поля: входная_строка (содержит исходный язык) и целевой язык (например, "ro"), как в приведенном ниже примере. Именами полей, соответствующих исходному и целевому языкам, должны быть коды языков, которые поддерживаются моделью. Подробные сведения о поддерживаемых языках см. в карточке модели.  Приведенные ниже примеры выбраны из {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.FileList" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The supported file types are csv, tsv, and JSON Lines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Поддерживаемые типы файлов: CSV, TSV и JSON Lines.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.FileLocation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Files will be uploaded to the default datastore and made available in your workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Файлы будут отправлены в стандартное хранилище данных и станут доступны в вашей рабочей области.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.FillMaskTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fill Mask example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример маски заполнения]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Gluemnli" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Glue mnli]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GLUE MNLI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.MicrosoftResearchParaphraseCorpus" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Research Paraphrase Corpus]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Research Paraphrase Corpus]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.QuestionAnsweringDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Extractive Question Answering data should contain 3 fields – question, context and answers field. The below table contains couple of samples from {link} dataset. The data in answers field should contain 2 more sub-fields – answer start and text like in the example below. The text contains the actual answer to the question from within the context and the answer_start is the number of characters before the start of the answer. The text and answer_start is an array with all possible answers and the start character for the corresponding answer text. For example, in the first example below, the answer helps many proteins bind the polypeptide starts at 236th character from the start of the context.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные для извлечения ответов на вопросы должны содержать 3 поля: поле вопроса, контекста и ответов. Приведенная ниже таблица содержит несколько примеров из набора данных {link}. Данные в поле ответов должны содержать еще 2 вложенных поля: начало ответа и текст (как в приведенном ниже примере). Текст содержит фактический ответ на вопрос из контекста, а начало_ответа — это количество символов до начала ответа. Текст и начало_ответа — это массив со всеми возможными ответами и начальным символом для соответствующего текста ответа. Например, в первом приведенном ниже примере ответ "помогает многим белкам связывать полипептид" начинается с 236-го символа от начала контекста.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.QuestionAnsweringTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question Answering example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример ответа на вопрос]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.RcdsWikipedia" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[rcds/wikipedia-for-mask-filling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[rcds/wikipedia-for-mask-filling]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.RestrictedFileList" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The supported file type is JSON Lines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Поддерживаемый тип файлов: JSON Lines.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.SAMSum" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SAMSum ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SAMSum ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.SingleTextClassificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Single text classification example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример классификации одного текста]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Squad" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stanford Question Answering Dataset (SQuAD)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stanford Question Answering Dataset (SQuAD)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.SummarizationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The summarization data is expected to have 2 fields – document, summary like shown below. The below samples are borrowed from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ожидается, что в сводных данных будет 2 поля: документ и сводка (как показано ниже). Ниже приведены примеры, взятые из набора данных {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.SummarizationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример сводки]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextClassificationDescription1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Single text classification requires the training data to include at least 2 fields – one for ‘Sentence1’ and ‘Label’ like in this example. Sentence 2 can be left blank in this case. The below examples are from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Для классификации одного текста необходимо, чтобы обучающие данные включали как минимум два поля — "Предложение1" и "Метка", как в этом примере. В данном случае поле "Предложение2" может быть пустым. Приведенные ниже примеры взяты из набора данных {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextClassificationDescription2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text pair classification, where you have two sentences to be classified (e.g., sentence entailment) will need the training data to have 3 fields – for ‘Sentence1’, ‘Sentence2’ and ‘Label’ like in this example. The below examples are from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Для классификации текстовых пар, где у вас есть два предложения для классификации (например, выводимости предложения), потребуются данные обучения для 3 полей: "Предложение1", "Предложение2" и "Метка", как в этом примере. Приведенные ниже примеры взяты из набора данных {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextClassificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text classification example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример классификации текстов]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationFinetuneDescription1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The text generation data is expected to have 2 fields – text, ground_truth like shown below. The below samples are borrowed from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ожидается, что данные для генерирования текста будут содержать 2 поля: текст и истина_для_граундинга, как показано ниже. Ниже приведены примеры, взятые из набора данных {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationFinetuneDescription2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The below samples are are formatted data the user might pass.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Приведенные ниже примеры представляют собой форматированные данные, которые может передать пользователь.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationFinetuneTitle1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation example - original data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример генерирования текста — исходные данные]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationFinetuneTitle2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation example - formatted  data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример генерирования текста — отформатированные данные]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример генерирования текста]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextPairClassificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text pair classification example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример классификации текстовых пар]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Информация]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TokenClassificationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification requires the training data to include 2 fields, ‘Tokens’ and ‘Tags’ like in this example. The tags could contain any strings depending on the fine-tune use case. The below examples are taken from {link}. Please note that the NER tags should be passed as an array of strings.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Классификация токенов требует, чтобы обучающие данные включали два поля: "Токены" и "Теги" (как в этом примере). Теги могут содержать любые строки в зависимости от варианта использования тонкой настройки. Приведенные ниже примеры взяты из {link}. Обратите внимание, что теги NER должны передаваться в виде массива строк.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TokenClassificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример классификации токенов]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TranslationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The translation data should have 2 fields – source language and target language like in the example below. The field names that map to source and target languages need to be language codes supported by the model. Please refer to the model card for details on supported languages.  The below examples are sampled from {link}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные для перевода должны содержать 2 поля: исходный язык и целевой язык (как в приведенном ниже примере). Именами полей, соответствующих исходному и целевому языкам, должны быть коды языков, которые поддерживаются моделью. Подробные сведения о поддерживаемых языках см. в карточке модели.  Приведенные ниже примеры выбраны из {link}.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TranslationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Translation example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Пример перевода]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Wmt16Dataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[WMT16 dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Набор данных WMT16]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Import" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Import]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Импорт]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.IncorrectColumnMappingError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Incorrect data formatting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Неправильное форматирование данных]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.PrepopulatedDataset.Multimodal" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multimodal]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Мультимодальный]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.PrepopulatedDataset.Sample" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Выборка]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.PrepopulatedDataset.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ready-to-go dataset for quick fine-tuning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Готовый набор данных для быстрой тонкой настройки]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.PrepopulatedDataset.examples" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[examples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[примеры]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.ProcessingFile" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Processing file...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Обрабатываем файл...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.RestrictedDataTypesMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The following file types are supported: {acceptedFileExtensions}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Поддерживаются следующие типы файлов: {acceptedFileExtensions}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.RestrictedFileList" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The supported file type is JSON Lines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Поддерживаемый тип файлов: JSON Lines.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SelectRegisteredDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Выбор данных]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.ChatCompletionFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need a jsonl file in chat format: For chat task type, each row in the data should be a list of JSON objects. Each row corresponds to a conversation and each object in the row is a turn/utterance in the conversation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Необходим файл JSONL в формате чата: для типа задачи чата каждая строка данных должна представлять собой список объектов JSON. Каждая строка соответствует беседе, а каждый объект в строке является ответом или речевым фрагментом в беседе.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.FillMask" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fill mask data needs to contain at least 2 columns: one for ‘Sentence1’ (string) and another for ‘Label’ (integer / string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные маски заливки должны содержать не менее двух столбцов: один для "Sentence1" (строка), другой для "Label" (целое число или строка).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.OpenAIFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need a jsonl file containing one prompt and the corresponding completion per line.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Вам нужен JSONL-файл содержащий одно приглашение и соответствующее завершение в каждой строке.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.OpenAIFinetuningPipelineChat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need a jsonl file in chat format: For chat task type, each row in the data should be a list of JSON objects. Each row corresponds to a conversation and each object in the row is a turn/utterance in the conversation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Необходим файл JSONL в формате чата: для типа задачи чата каждая строка данных должна представлять собой список объектов JSON. Каждая строка соответствует беседе, а каждый объект в строке является ответом или речевым фрагментом в беседе.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.QuestionAnsweringEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering data needs to contain at least 3 columns: for ‘Question’ (string), ‘Context’ (string), and ‘Answers’ (string). Additionally, it can optionally include ‘Answers_start’ (int) and ‘Answers_text’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные ответа на вопрос должны содержать не менее трех столбцов: "Question" (строка), "Context’" (строка) и "Answers" (строка). Кроме того, они могут дополнительно включать "Answers_start" (целое число) и "Answers_text" (строка).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.QuestionAnsweringFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering data needs to contain at least 5 columns: for ‘Question’ (string), ‘Context’ (string), ‘Answers’ (string), ‘Answers_start’ (int) and ‘Answers_text’ (string). Additionally, it can optionally include ‘doc_stride’ (int), ‘n_best_size’ (int) and ‘max_answer_length_in_tokens’ (int).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные ответов на вопросы должны содержать не менее пяти столбцов: для "Question" (строка), "Context" (строка), "Answers" (строка), "Answers_start" (целое число) и "Answers_text" (строка). Кроме того, эти данные также могут включать "doc_stride" (целое число), "n_best_size" (целое число) и "max_answer_length_in_tokens" (целое число).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.Summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization data needs to contain at least 2 columns: one for ‘Document’ (string) and another for ‘Summary’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Сводные данные должны содержать не менее 2 столбцов: один для "Document" (строка), а другой для "Summary" (строка).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TextClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text classification data needs to contain at least 2 columns: one for ‘Sentence1’ (string) and another for ‘Label’ (integer / string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные классификации текстов должны содержать не менее двух столбцов: один для "Sentence1" (строка), другой для "Label" (целое число или строка).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TextGenerationEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation data needs to contain at least 2 columns: one for ‘Sentence1’ (string) and another for ‘Label’ (integer / string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные создания текстов должны содержать не менее двух столбцов: один для "Sentence1" (строка), другой для "Label" (целое число или строка).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TextGenerationFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation data needs to contain 2 columns: one for ‘text’ (string) and another for 'ground_truth' (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные для генерации текста должны содержать 2 столбца: один для "Text" (строка), а другой для "ground_truth" (строка).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TokenClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification data needs to contain 2 columns: one for ‘Token’ (string) and another for ‘Tag’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные классификации маркеров должны содержать 2 столбца: один для "Token" (строка), а другой для "Tag" (строка).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TranslationEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Translation data needs to contain a column for ‘Source_language’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные перевода должны содержать столбец "Source_language" (строка).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TranslationFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Translation data needs to contain 2 columns: one for ‘Source_language’ (string) and another for ‘Target_language’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Данные перевода должны содержать два столбца: один для "Source_language" (строка), другой для "Target_language" (строка).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.DisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Display name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Отображаемое имя]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.DisplayNameErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Display name can not exceed 128 characters and can only contain letters, numbers, dashes and underscores.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Длина отображаемого имени не может превышать 128 символов и может содержать только буквы, цифры, тире и подчеркивания.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.DisplayNamePlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter a display name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Введите отображаемое имя]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.Entity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Entity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Сущность]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.EntityPlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter an entity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Введите сущность]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.ProjectName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Weights & Biases project name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Имя проекта весов и смещений]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.ProjectNamePlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter a project name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Введите имя проекта]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.ProjectNameRegexErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project name can not exceed 128 characters and can only contain letters, numbers, dashes and underscores.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Длина имени проекта не может превышать 128 символов и может содержать только буквы, цифры, тире и подчеркивания.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.ProjectNameRequiredErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A Weights & Biases project name is required.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Требуется имя проекта весов и смещений.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.Tags" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Теги]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.TagsPlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[azureOpenai/{ftjob-abcdef}, azureOpenai/{base-model}, azureOpenai/finetune]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[azureOpenai/{ftjob-abcdef}, azureOpenai/{base-model}, azureOpenai/finetune]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.WandBTagsErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags can only contain letters, numbers, dashes, spaces, semi-colons, forward slashes, curly brackets and underscores.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Теги могут содержать только буквы, цифры, тире, пробелы, точки с запятой, косые черты, фигурные скобки и символы подчеркивания.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.IntegrationDisabled.Link" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about Weights & Biases integration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Сведения об интеграции весов и смещений.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.IntegrationDisabled.Message" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your admin must enable Weights & Biases integration to log runs in your Weights & Biases project for fine-tuning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Администратору необходимо включить интеграцию Weights & Biases, чтобы регистрировать запуски в проекте Weights & Biases для точной настройки.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to log this fine-tuning job to Weights & Biases?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Зарегистрировать в журнале это задание тонкой настройки для весов и смещений?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.No" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Нет]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This integration will automatically log metrics, parameters, and other information related to the fine-tuning job to the specified Weights & Biases project.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Эта интеграция автоматически регистрирует метрики, параметры и другие сведения, связанные с заданием тонкой настройки, в указанном проекте весов и смещений.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.TooltipLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Weights & Biases integration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Дополнительные сведения об интеграции весов и смещений.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.Yes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Yes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Да]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
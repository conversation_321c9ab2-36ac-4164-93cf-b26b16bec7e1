﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\3\s\common\openai-core\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-TW" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";Agents.FilterTooltipDisabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only chat completion models that support agents are supported here. This list is pre-filtered for only supported chat completion models that support agents.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這裡僅支援會支援代理程式的聊天完成模型。此清單已預先篩選為支援代理程式的僅限支援的聊天完成模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Agents.UnableToFetchFiles" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unable to fetch files]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法擷取檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Assistants.FilterTooltipDisabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only chat completion models that support assistants are supported here. This list is pre-filtered for only supported chat completion models that support assistants.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這裡僅支援會支援助理的聊天完成模型。此清單已預先篩選為支援助理的僅限支援的聊天完成模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Assistants.UnableToFetchFiles" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unable to fetch files]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法擷取檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.Connection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.ContentFilter" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Content filter]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[內容篩選]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeployButton.CreateNewDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeployButton.FromBaseModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From base models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自基底模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeployButton.FromFineTunedModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From fine-tuned models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自微調模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeployModelDialog.CompletionTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a completion model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取完成的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeployModelDialog.FiltersDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only chat completion models are supported in the chat playground. This list is pre-filtered for only supported chat completion models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天遊樂場只支援聊天完成模型。此清單已針對僅支援的聊天完成模型預先進行篩選。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeployModelDialog.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a chat completion model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取聊天完成模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeployNewModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create new deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.Deployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeploymentDeletedErrorAgents" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The deployment associated with this agent has been deleted or is not supported in AI Foundry. Please select or create another deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與此代理程式相關聯的部署已被刪除或在 AI Foundry 中不支援。請選取或建立另一個部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeploymentDeletedErrorAssistants" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The deployment associated with this assistant has been deleted or is not supported in AI Foundry. Please select or create another deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與此助理相關聯的部署已被刪除或在 AI Foundry 中不支援。請選取或建立另一個部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeploymentName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.DeploymentType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.IncompatibleDeploymentError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This deployment is not compatible with assistant functionality. Please select or create another deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此部署與助理功能不相容。請選取或建立另一個部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.IncompatibleDeploymentErrorAgents" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This deployment is not compatible with agents functionality. Please select or create another deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此部署與代理程式功能不相容。請選取或建立另一個部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.ModelName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.ModelOnlyAvailableThroughCode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Currently, this model is only available through code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目前，此模型只能透過程式碼提供。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.ModelVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.NoDeploymentExists" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No deployment exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有部署存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.Parameters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Parameters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[參數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.Region" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[區域]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.Resource" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.SelectDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.SelectExistingDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select an existing deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取現有的部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.SelectExistingModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select an existing model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取現有的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.State" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[State]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatOptions.ThroughputUnits" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput units]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸送量單位]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatPlayground.AssistantsRole" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["Cognitive Services OpenAI Contributor"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[「認知服務 OpenAI 參與者」]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatPlayground.ConnectionName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatPlayground.FailedToFetchPermissions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to fetch permissions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法擷取權限]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatPlayground.NoAccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You have insufficient permissions to access API keys and the account is missing Chat completion permissions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您沒有足夠的權限可以存取 API 金鑰，且帳戶遺漏聊天完成權限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatPlayground.NoAccessTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unable to use model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法使用模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Not authorized]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatPlayground.ResourceName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatPlayground.Role" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["Cognitive Services OpenAI User" or "Cognitive Services OpenAI Contributor"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[「認知服務 OpenAI 使用者」或「認知服務 OpenAI 參與者」]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Role(s): Cognitive Services OpenAI User or Cognitive Services OpenAI Contributor]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatPlayground.RoleRequired" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have permission to access the resource "{resourceName}" because its connection "{connectionName}" is set to use role-based authentication. To resolve this issue, you can either assign the role of {role} to your user ID "{userId}" for the resource, or change the connection's authentication method to use an API key and try again. Contact your admin for help. {rbacDocLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您沒有存取資源 "{resourceName}" 的權限，因為其連線 "{connectionName}" 設定為使用角色型驗證。若要解決此問題，您可以將 {role} 的角色指派給資源的使用者識別碼 "{userId}"，或變更連線的驗證方法以使用 API 金鑰，然後再試一次。請與系統管理員連絡以尋求協助。{rbacDocLink}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[You don't have permissions on this resource because its connection is set to use role-based authentication. You can either change the role for your user ID on the resource provided in the following details, or change the connection's authentication to use an API key and try again. Contact your admin for help. {rbacDocLink} about permissions]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatPlayground.UserId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用者識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatSetup.GeneratePrompt.Button" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate system prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生系統提示]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Generate prompt]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatSetup.GeneratePrompt.Close" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關閉]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatSetup.GeneratePrompt.Placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Describe what you want the model to do. e.g. write a funny restaurant review...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述您希望模型執行的動作，例如撰寫有趣的餐廳評論...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatSetup.GeneratePrompt.Send" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Send]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[傳送]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatSetup.GeneratePrompt.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate a prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生一個提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatSetup.GeneratePrompt.UnknownError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An unknown error occurred.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[發生未知的錯誤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatSetup.Here" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這裡]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatSetup.LearnMoreAboutPermissions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about permissions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[進一步了解使用權限]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Consumer.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[consumer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[消費者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Consumer.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Consumer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[消費者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Defense.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[defense]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[防禦]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Defense.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Defense]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[防禦]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Education.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[education]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[教育]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Education.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Education]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[教育]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Energy.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[energy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[能源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Energy.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Energy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[能源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Entertainment.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[entertainment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[娛樂]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Entertainment.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Entertainment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[娛樂]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Finance.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[finance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金融]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Finance.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Finance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[財務]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Gaming.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[gaming]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[遊戲]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Gaming.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Gaming]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[遊戲]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Healthcare.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[healthcare]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[醫療保健]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Healthcare.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Healthcare]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[醫療保健]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Manufacturing.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[manufacturing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[製造]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Manufacturing.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Manufacturing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[製造]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.None.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[none]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.None.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[None]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Retail.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[retail]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零售]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Retail.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retail]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零售]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Sustainability.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[sustainability]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[永續性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Industry.Sustainability.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sustainability]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[永續性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.Body" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select from the latest text generation models from Azure OpenAI, Phi, DeepSeek, Meta and others.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從 Azure OpenAI、Phi、DeepSeek、Meta 等等選取最新的文字生成模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.ButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Re-write with AI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 AI 重新撰寫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.DeploymentLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.EditLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What would you like to change?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您想要變更什麼？]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.EditPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provide an example of how you’d like to change your text prompt. Include as much specificity as you need.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供如何變更文字提示的範例。盡可能包含您需要的明確性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.EditTextAria" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit text input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[編輯文字輸入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.IndustryPromptLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start with an industry system prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從產業系統提示開始]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.IndustryPromptSecondaryLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose an industry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請選擇產業]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Re-write your prompt with AI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 AI 重新撰寫您的提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RewriteWithAi.Update" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Update]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\1\s\common\models-details\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-TW" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";AOAIFinetuneInvalidRegion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI model fine-tune is not available in your workspace's region. Supported regions include: {{region}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 模型微調在您工作區的地區無法使用。支援的區域包括: {{region}}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service model fine-tune is not available in your workspace's region. Supported regions include: {{region}}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAIInvalidRegion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is not available in your workspace's region.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 在您工作區的區域中無法使用。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is not available in your workspace's region.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAINotSignedUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This subscription is not enabled for Azure OpenAI yet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[尚未針對 Azure OpenAI 啟用此訂用帳戶]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This subscription is not enabled for Azure OpenAI Service yet]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAIRequest" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request access to Azure OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要求存取 Azure OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Request access to Azure OpenAI Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAISignUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use of Azure OpenAI models in Azure Machine Learning requires Azure OpenAI resources. This subscription or region does not have access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure Machine Learning 中使用 Azure OpenAI 模型需要 Azure OpenAI 資源。此訂用帳戶或區域沒有存取權。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use of Azure OpenAI Service models in Azure Machine Learning requires Azure OpenAI Service resources. This subscription or region does not have access.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAISignUpWithSub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use of Azure OpenAI models in Azure Machine Learning requires Azure OpenAI resources. This subscription ({subscription}) or region ({location}) does not have access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure Machine Learning 中使用 Azure OpenAI 模型需要 Azure OpenAI 資源。此訂用帳戶 ({subscription}) 或區域 ({location}) 沒有存取權。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use of Azure OpenAI Service models in Azure Machine Learning requires Azure OpenAI Service resources. This subscription ({subscription}) or region ({location}) does not have access.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.BenchmarkBreadcrumbText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型基準]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare benchmarks across models and datasets available in the industry to assess which one meets your business scenario. {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較業界可用模型和資料集的基準，以評定符合您商務案例的基準。{1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageDescriptionEmbeddings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View benchmarks for embeddings models across various tasks for comparison. {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視不同工作的內嵌模型的基準，以進行比較。{1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageDescriptionLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about how model performance is scored]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解模型效能的評分方式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageDescriptionLinkEmbeddings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about how we benchmark embeddings models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解我們如何對內嵌模型進行基準測試]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assess model performance with evaluated metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用評估的計量評定模型效能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AzureOpenAiDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 部署]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service deployment]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AzureOpenAiPlayground" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI playground]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 遊樂場]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service playground]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AzureOpenAiService" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.BarChart" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bar chart]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[橫條圖]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.BreadcrumbText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse leaderboards]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[瀏覽排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.CompareBetweenMetrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare between metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Cost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.CostDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Estimated cost for the sum of cost per input tokens and cost per output tokens, with a ratio of 3:1.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[個別輸入權杖成本和個別輸出權杖成本總和的估計成本，比率為 3：1。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[USD per 1M Tokens; Lower is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.CostSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[USD per 1M Tokens; Lower is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每 100 萬個權杖 USD; 越低越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Estimated cost for the sum of cost per input tokens and cost per output tokens, with a ratio of 3:1.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.ExportButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Export as a report view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[匯出為報告檢視]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.HelpPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevant resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相關資源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.HelpPanel.linkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about benchmarks leaderboard details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解基準排行榜詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.SaveButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save current view as another leaderboard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將目前檢視儲存為另一個排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.SubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tier-1 is a small representative set of critical benchmarks. These benchmarks include important capabilities, have large sample sizes and correct examples, and are challenging.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[第 1 層是一組小型的關鍵基準代表集。這些基準包含重要的功能，擁有大量的樣本和正確的範例，並且具有挑戰性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multilingual]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多語言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HarmbenchContextualSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate used in safety benchmarking; Lower is safer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用於安全基準測試的攻擊成功率；數值越低越安全]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Attack success rates in contextually harmful behaviors; Lower is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HarmbenchCopyRightViolationsSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate used in safety benchmarking; Lower is safer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用於安全基準測試的攻擊成功率；數值越低越安全]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Attack success rates in copyright violations; Lower is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HarmbenchStandardSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate used in safety benchmarking; Lower is safer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用於安全基準測試的攻擊成功率；數值越低越安全]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Attack success rates in standard harmful behaviors; Lower is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HelpPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevant resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相關資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Benchmarking Leaderboard]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HelpPanel.linkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about benchmarks leaderboard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解基準測試排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HighLights" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Highlights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重點項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HighLightsDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Which model performs best in following criteria.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[哪種模型在下列準則中表現最佳。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Which model perform best in following criteria.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.KnowledgeInSensitiveDomainsSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy; Higher means more knowledge of sensitive domains in cybersecurity, biosecurity and chemical security]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[準確性; 越高表示對網路安全性、生物安全性和化學安全性的敏感領域有更深入的了解]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Higher accuracy denotes more knowledge of dangerous capabilities.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.LeaderboardByBenchmarks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Leaderboards by scenario]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[依案例排序的排行榜]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Leaderboard by scenarios]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.LeaderboardDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Take a deep dive into detailed metrics of model performance in these categories.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入探討這些類別中模型效能的詳細指標。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deep dive of details metrics of model performance]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.LeaderboardTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Leaderboards by benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據基準的排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.MetricDescriptionPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scores are presented at the dataset and the model levels. At the dataset level, the scored. At the dataset level, the scored.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分數會在資料集與模型層級顯示。在資料集層級，已評分。在資料集層級，已評分。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.Deploy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.EvaluateOwnData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate on your data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估您的資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.Model" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.ModelDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.ModelNotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model details not available in your Azure cloud region.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型詳細資料不適用於您的 Azure 雲端區域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.ModelNotAvailableInFDP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The model is not available in this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型不適用於這個資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.ModelNotAvailableInFoundry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model is not available in Azure AI Foundry.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Foundry 不提供此模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.QualityIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.Ranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[排名]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.TooltipContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An average of accuracy scores from comprehensive benchmark datasets measuring model capabilities, such as reasoning, knowledge, and coding.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用以測量模型功能 (例如，推理、知識及編碼) 的全面基準資料集中正確性分數的平均值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.TryInPlayground" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try in playground]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在遊樂場中試用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelsSelected" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.MostAttractiveQuadrant" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Most attractive quadrant]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最具吸引力的象限]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Most Attractive Quadrant]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.No" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[否。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Quality" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An average of accuracy scores from comprehensive benchmark datasets measuring model capabilities, such as reasoning, knowledge, and coding.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用以測量模型功能 (例如，推理、知識及編碼) 的全面基準資料集中正確性分數的平均值。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Quality index; higher is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityLeaderboard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality leaderboard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualitySubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index; Higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質索引; 越高越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[An average of accuracy scores from comprehensive benchmark datasets measuring model capabilities, such as reasoning, knowledge, and coding.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityVsCost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality vs Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質與成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityVsSafety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality vs Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質與安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityVsThroughput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality vs Throughput]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質與輸送量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.RadarChart" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Radar plot]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[雷達圖]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Safety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SafetyDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average attack success rate from benchmark datasets (HarmBench) evaluating behaviors in cybercrime & unauthorized intrusion, chemical & biological weapons/drugs, copyright violations, misinformation & disinformation, harassment & bullying, illegal activities, and general harm. Benchmarked with Azure AI Content Safety turned off.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從基準資料集 (HarmBench) 評估的平均攻擊成功率，涵蓋網路犯罪行為及未經授權的入侵、化學與生物武器/毒品、著作權違規、錯誤資訊與反資訊、騷擾與霸凌、非法活動，以及一般傷害。基準測試時 Azure AI 內容安全已關閉。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Average attack success rate from benchmark datasets (HarmBench) evaluating behaviors in cybercrime & unauthorized intrusion, chemical & biological weapons/drugs, copyright violations, misinformation & disinformation, harassment & bullying, illegal activities, and general harm.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SafetySubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate; Lower is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[攻擊成功率；越低越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Quality index; Higher is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SafetyXAxisTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[攻擊成功率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SeeDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare model performance in Quality, Safety, Cost, and Throughput, backed by industry standard public benchmarks. {link} about our scoring methodology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較模型在品質、安全性、成本和輸送量方面的效能，並以業界標準的公開效能評定作為支持。{link} 我們的評分方法。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Compare Quality, Cost, Throughput and other criteria. {link} about our scoring methodology.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Throughput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸送量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ThroughputDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of output tokens that are getting generated per second from the time the request is sent to the endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從要求傳送到端點時，每秒產生的輸出權杖數目。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Output Tokens per Second; Higher is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ThroughputSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output Tokens per Second; Higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒輸出權杖; 越高越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The number of output tokens that are getting generated per second from the time the request is sent to the endpoint.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ThroughputVsCost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput vs Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸送量與成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Find the best model by comparing model performance across various criteria]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較各種準則的模型效能以找出最佳模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Model leaderboards]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.CostUnit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[USD per 1M tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[美元 (每 1 百萬個權杖)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.ModelDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to model details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移至模型詳細資料]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Model details]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.QualityIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.SafetyIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[攻擊成功率]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Safety index]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.ThroughputUnit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output tokens per second]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒輸出語彙基元數量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ToxicityDetectionSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1-based accuracy in the ability to detect toxic content; Higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 型偵測有害內容的能力準確性; 越高越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Accuracy in the ability of detecting toxic content; Higher is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TradeOffs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trade-off charts]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權衡圖表]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Trade-offs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TradeOffsSubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You might need a model that excels in one category but is a lower performer in another. View the trade-offs for various categories.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可能需要一個在某一類別中表現優異，但在另一類別中表現較差的模型。檢視各類別的權衡取捨。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[How you choose on that best fit your need]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.BitextMining" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bitext mining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[雙文字採礦]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Coding" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coding]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[程式碼撰寫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.DocumentClustering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Document clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件叢集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.GeneralKnowledge" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[General knowledge]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[常識]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[據實性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.HarmbenchContextual" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Contextually harmful behavior]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[內容有害的行為]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Contextually harmful behavior (HarmBench)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.HarmbenchCopyRightViolations" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copyright violations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[著作權違規]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Copyright violations (HarmBench)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.HarmbenchStandard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard harmful behavior]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標準有害行為]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Standard harmful behavior (HarmBench)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.InformationRetrieval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Information retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資訊擷取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.KnowledgeInSensitiveDomains" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Knowledge in sensitive domains]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[敏感性領域的知識]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Math" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Math]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[數學]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.QA" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[問題解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Reasoning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Safety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.TextClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.ToxicityDetection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Toxicity detection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[毒性偵測]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Toxicity Detection]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BarChartCard.FullScreen" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Full screen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[全螢幕]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.LatencyMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Time to first token]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[距第一個權杖的時間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.Metric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.ModelVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.PublicDataBenchmark" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public data benchmark results]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公用資料基準結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.QualityIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.Seconds" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Seconds]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[秒]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.ThroughputMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated tokens per second]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒產生的權杖數量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.Tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權杖]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.USDPer1MTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[USD per 1M tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[美元 (每 1 百萬個權杖)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkMetric.NA" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[N/A]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不適用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.CostCaption" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(USD per 1M tokens; lower is better)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(每 100 萬個權杖 USD; 越低越好)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[(USD per 1M tokens; Lower is better)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.DefaultCaption" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(Higher is better)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(愈高愈好)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.EmbeddingsLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Embeddings Index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[內嵌索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.LatencyCaption" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(Lower is better)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(愈低愈好)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.QualityLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality Index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.X" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[X-axis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[X 軸]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.Y" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Y-axis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Y 軸]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正確性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Cost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Embeddings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Embeddings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[內嵌]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.EmbeddingsIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Embeddings index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[內嵌索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.F1Score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1 score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流暢性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.GPTSimilarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPT similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT 相似性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[據實性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Latency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延遲]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.MAP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mean average precision]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均精確度均值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.NDCGAt10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NDCG at 10]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NDCG 於 10]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Quality" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.QualityIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相關性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Safety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.SpearmanCorrelation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spearman 相互關聯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.VMeasure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 量值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Tooltip.Ranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ranking No.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[排名編號]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Tooltip.Score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Versus" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[vs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.AddModelToCompareButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model to compare]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要比較的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Back" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Back]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[返回]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ChartContainerHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metrics to compare]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要比較的計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ClearAllModelsTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clear all models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[清除所有模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ClearAllTasksTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clear all tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[清除所有標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.CompareView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較檢視]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.DialogTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Done" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Done]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Ellipsis" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[…]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.FilterPanelHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models to compare]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要比較的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.FilterPanelModelsCount" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.FilterPanelModelsHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Selected model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.FilterPanelTasksHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Popular tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[熱門工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.GroupByDatasets" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Group by datasets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[依數據集分組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.GroupByModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Group by models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[依模型分組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ListView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[List view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[清單檢視]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.MetricsDetails.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.MetricsDetails.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ModelVersionSelector" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version selector]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本選取器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.PageDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare benchmarks across models and datasets available in the industry to assess which one meets your business scenario. {1} about how model performance is scored.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較業界可用模型和資料集的基準，以評估哪一個符合您商務案例。{1}模型績效的評分方式。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Compare benchmarks across models and datasets available in the industry to assess which one meets your business scenario. ({Learn more}) about how model performance is scored.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.PageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assess model performance with evaluated metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用評估的計量評定模型效能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.RemoveModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Remove model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移除模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.SearchBoxAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜尋工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.SearchBoxPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜尋]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.SeeLess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See less]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看較少]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.SeeMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更多]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models evaluated on performance metrics can't be compared with models evaluated on embeddings metrics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[依據效能計量評估的模型，無法與根據內嵌計量評估的模型進行比較。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.DescriptionNoCostLatencyData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost and latency data are not yet supported for certain models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[某些模型尚不支援成本和延遲數據。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.Remove" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Remove]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are not comparable]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這些模型無法比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.TitleAxes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These axes are not comparable]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這些座標軸無法比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonButton.ReadMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入閱讀]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonCard.CompareMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare with more models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與更多模型比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonCard.ComparingWith" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Comparing with]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在與以下項目比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonCard.ComparisonHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonChartUtils.NoData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No data available for the selected metric and dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取的計量和資料集沒有可用的資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.AIQualityHeadline" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compares models based on accuracy, coherence, groundedness, relevance, fluency, and GPT similarity metrics. The default view is an average of these scores, but you can also view scores for each metric individually.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據精確度、一致性、基礎性、相關性、流暢性和 GPT 相似度計量來比較模型。默認檢視是這些分數的平均值，但您也可以個別檢視每個計量的分數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.AIQualityTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI quality comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 品質比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.BackToResults" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Back to benchmark results]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[回到基準結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.CalculatedDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset used to calculate score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用來計算分數的資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.CalculatedMetrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Calculated metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[度量轉換設定]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.Close" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關閉]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.CostHeadline" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compares models based on the dollar amount required to run 1M tokens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據執行 1M 令牌所需的美金金額來比較模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.CostTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成本比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.LatencyHeadline" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compares models based on the time taken to generate the first token or the response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據產生第一個令牌或回應所花費的時間來比較模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.LatencyTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延遲比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.Metric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.PublicData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公用資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.ThroughputHeadline" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compares models based on the number of tokens or requests processed per second.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據每秒處理的令牌數目或要求來比較模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.ThroughputTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸送量比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.AIQuality" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 品質]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.AIQualitySubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index; higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質索引; 越高越好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.Cost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Estimated cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預估費用]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Cost]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.CostSubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Estimated cost index; lower is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[估計成本索引; 越低越好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.CostTableHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.Latency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延遲]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.LatencySubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Time to first token; lower is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[到第一個權杖的時間; 越低越好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.Throughput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸送量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.ThroughputSubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated tokens per second; higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒產生的權杖數量; 越高越好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonView.CompareMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare with more models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與更多模型比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonView.ComparingWith" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Comparing with]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在與以下項目比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonView.ComparisonHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.GPTSimilarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures the similarity in semantic meaning and context between a sentence in the source data (ground truth) and a sentence generated by an AI model as its response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[測量來源資料中某個句子 (基準真實) 與 AI 模型所產生作為其回應的句子之間的語意和內容相似性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.NoDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[At the model level, the accuracy score is the average of the dataset-level accuracies for each model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在模型層級，正確度分數是每個模型之資料集層級正確性的平均值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures how well the language model can produce output that flows smoothly, reads naturally, and resembles human-like language. Important to assess the readability and user-friendliness of your model's generated responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[測量語言模型產生之輸出流暢、讀起來自然以及與人類語言類似的程度。對於評估模型產生之回應的可讀性與使用者方便程度非常重要。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.f1score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The F1 score is the weighted mean of the precision and recall, where an F1 score reaches its best value at 1 (perfect precision and recall) and worst at 0.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分數是精確度和重新叫用的加權平均數，F1 分數的最佳值為 1 (完美精確度和重新叫用) 最差值則為 0。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures the grammatical proficiency of a generative AI's predicted answer. Used to evaluate whether AI-generated text adheres to grammatical rules, syntactic structures, and proper vocabulary usage.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[測量生成式 AI 預測答案的文法程度。用來評估 AI 產生的文字是否遵循文法規則、語法結構及適當的詞彙使用方式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures how well the model's generated answers align with information from the source data. Essential for applications where factual correctness and contextual accuracy are key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[測量模型產生的答案符合來源資料中資訊的程度。對於事實正確性與內容相關正確性至關重要的應用程式為必要。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.input_token_cost_per_1M_tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dollar value for pay-as-you-go for 1 million input tokens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[100 萬個輸入權杖的隨用隨付金額。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Input token cost per 1M tokens]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.inter_token_latency_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the time between tokens received (inter token latency).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是權杖接收 (權杖間延遲) 之間的時間。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Intertoken latency]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_mean_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average time in seconds taken for processing a request, computed over multiple requests. To do this, we send a request to the endpoint every hour, for 2 weeks, and compute the average.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[處理要求所花費的平均秒數，透過多個要求所計算。若要做到這一點，我們會每小時傳送一個要求到端點，持續 2 週，並計算平均值。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency mean in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_p50_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[50th percentile value (the median) of latency (the time taken between the request and when we receive the entire response with a successful code). For example: When we send a request to the endpoint, 50% of the requests are completed in x seconds, with x being the latency measurement.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[第 50 個百分位數值 (中位數) 延遲 (從要求與收到成功代碼的過程所花費的時間)。例如: 當我們傳送要求到端點時，50% 的要求會在 x 秒內完成，x 是延遲度量。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P50 in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_p90_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[90th percentile value (the median) of latency (the time taken between the request and when we receive the entire response with a successful code). For example: When we send a request to the endpoint, 90% of the requests are completed in x seconds, with x being the latency measurement.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[第 90 個百分位數值 (中位數) 延遲 (從要求與收到成功代碼的過程所花費的時間)。例如: 當我們傳送要求到端點時，90% 的要求會在 x 秒內完成，x 是延遲度量。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P90 in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_p95_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[95th percentile value (the median) of latency (the time taken between the request and when we receive the entire response with a successful code). For example: When we send a request to the endpoint, 95% of the requests are completed in x seconds, with x being the latency measurement.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[第 95 個百分位數值 (中位數) 延遲 (從要求與收到成功代碼的過程所花費的時間)。例如: 當我們傳送要求到端點時，95% 的要求會在 x 秒內完成，x 是延遲度量。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P95 in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_p99_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[99th percentile value (the median) of latency (the time taken between the request and when we receive the entire response with a successful code). For example: When we send a request to the endpoint, 99% of the requests are completed in x seconds, with x being the latency measurement.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[第 99 個百分位數值 (中位數) 延遲 (從要求與收到成功代碼的過程所花費的時間)。例如: 當我們傳送要求到端點時，99% 的要求會在 x 秒內完成，x 是延遲度量。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P99 in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_ttft_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TTFT stands for total time to first token. This is the time taken for the first token in the response to be returned from the endpoint when streaming is enabled.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TTFT 代表第一個權杖的總時間。這是啟用串流時，回應中的第一個權杖從端點傳回所花費的時間。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency TTFT in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.map" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mean Average Precision (MAP) evaluates the quality of ranking and recommender systems. It measures both the relevance of suggested items and how good the system is at placing more relevant items at the top. Values can range from 0 to 1. The higher the MAP, the better the system can place relevant items high in the list.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均值平均精確度 (MAP) 會評估排名和推薦程式系統的品質。它會測量建議項目的相關性，以及系統將更多相關項目置於頂部方面的表現如何。值的範圍可以從 0 到 1。MAP 越高，系統就越會將相關項目放在清單上端。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.ndcgat10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Normalized Discounted Cumulative Gain (NDCG) evaluates the quality of information retrieval systems. NDCG helps measure a machine learning algorithm's ability to sort items based on relevance. It compares rankings to an ideal order where all relevant items are at the top of the list. K is a user-assigned parameter that defines the cutoff point (list length) while evaluating ranking quality. For example, if k=10, ndcg_at_10, will look at top-10 items.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標準化折扣累積收益 (NDCG) 會評估資訊擷取系統的品質。NDCG 可協助測量機器學習演算法根據相關性排序項目的能力。它會將排名與所有相關項目都位於清單頂端的理想順序進行比較。K 是個使用者指派的參數，會在評估排名品質時定義截止點 (清單長度)。例如，如果 k=10，ndcg_at_10，則會查看前 10 個項目。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.output_token_cost_per_1M_tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dollar value for pay-as-you-go for 1 million output tokens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[100 萬個輸出權杖的隨用隨付金額。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Output token cost per 1M tokens]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures the extent to which the model's generated responses are pertinent and directly related to the given questions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[測量模型產生的回應與指定問題相關且直接相關的程度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.spearmancorrelation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation based on cosine similarity is the main metric for STS (Semantic Textual Similarity) and Summarization Embedding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以餘弦相似性為基礎的 Spearman 相互關聯是 STS (語意文字相似性) 與摘要內嵌工作的主要計量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.throughput_gtps_token_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GTPS stands for generated tokens per second. This is the number of output tokens that are getting generated per second from the time the request is sent to the endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GTPS 代表每秒產生的權杖。這是從要求傳送到端點時，每秒產生的輸出權杖數目。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput GTPS token count]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.throughput_rps_request_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[RPS stands for requests per second. This is the total number of requests processed per second.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[RPS 代表每秒要求數。這是每秒處理的要求總數。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput RPS request count]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.throughput_ttps_token_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TTPS stands for total tokens per second. This is the number of total tokens processed per second including both from the input prompt and generated output tokens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TTPS 代表每秒權杖總數。這是每秒處理的權杖總數，包括來自輸入提示和產生的輸出權杖。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput TTPS token count]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.time_between_tokens_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the time between tokens received (inter token latency).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是權杖接收 (權杖間延遲) 之間的時間。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Time between tokens in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.vmeasure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure is a metric used to evaluate the quality of clustering. It is calculated as the harmonic mean of homogeneity (each cluster contains only members of a single class) and completeness (all members of a given class are assigned to the same cluster), ensuring a balance between the two for a meaningful score. Possible score lies between 0 and 1. Score of 1 stands for perfectly complete labeling.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 量值是用來評估叢集品質的計量。其計算方式為同質性 (每個叢集僅包含單一類別的成員) 和完整性 (給定類別的所有成員都獲派給同一個叢集) 的調和平均值，確保兩者之間的平衡以獲得有意義的分數。可能分數會介於 0 到 1 之間。1 分代表完美的完整標籤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.GPTSimilarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPT similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT 相似性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.NoMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric name not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有計量名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正確性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.f1score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1 score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流暢性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[據實性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.index" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average of all]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所有平均值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.input_token_cost_per_1M_tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input token cost per 1M tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每 100 萬個權杖的輸入權杖成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.inter_token_latency_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Intertoken latency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權杖間延遲]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_mean_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency mean (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延遲平均 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency mean]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_p50_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency P50 (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延遲 P50 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P50]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_p90_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency P90 (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延遲 P90 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P90]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_p95_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency P95 (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延遲 P95 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P95]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_p99_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency P99 (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延遲 P99 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P99]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_ttft_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency TTFT (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延遲 TTFT (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency TTFT]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.map" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mean average precision]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均精確度均值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.ndcgat10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NDCG at 10]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NDCG 於 10]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.output_token_cost_per_1M_tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output token cost per 1M tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每 100 萬個權杖的輸出權杖成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相關性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.spearmancorrelation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spearman 相互關聯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.throughput_gtps_token_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput GTPS (tokens per sec)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒輸送量 GTPS (令牌數)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput GTPS]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.throughput_rps_request_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput RPS (requests per sec)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒的輸送量 RPS (要求數)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput RPS]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.throughput_ttps_token_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput TTPS (tokens per sec)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸送量 TTPS 每秒 (令牌數)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput TTPS]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.time_between_tokens_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Time between tokens (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[令牌 (秒) 之間的時間]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Time between tokens]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.vmeasure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 量值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ModelCatalogBenchmarksPivot.NoBenchmarksData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No benchmarking data exists for given model. Please ensure the model version is correct.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指定的模型沒有任何基準資料。請確認模型版本正確。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ModelsTable.Index" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ModelsTable.Metrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.NextSteps.ButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try with your own data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用您自己的資料試試看]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.NextSteps.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate this model with your own data to see how it performs with your scenario.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用您自己的資料評估此模型，以了解其在案例中的執行方式。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Get benchmark results on your own data to see how this model performs with your scenario.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.NextSteps.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate results with your data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用您的資料產生結果]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[What's next?]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.BenchmarksReportsView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Benchmarks reports view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基準報告檢視]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.Datasets" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Datasets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.FileName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.GoToReportsView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to reports view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移至報表檢視]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.Models" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.ShareView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Share view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共用檢視]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.BitextMining" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bitext mining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[雙文字採礦]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Clustering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[叢集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.PairClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pair classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配對分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.QuestionAnswering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[問題解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Reranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新排名]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Retrieval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.STS" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[STS]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[STS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.TextGeneration" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.AccessRequest" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要求存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.CardHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Versions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.DeploymentType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.FeatureFlaggedModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Access will be granted according to Microsoft's eligibility criteria; you may not be eligible for access at this time. If you have already requested access, thank you for your patience while your submission is reviewed. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存取權會依 Microsoft 的資格準則授予，您目前可能不符合存取資格。如已要求存取權，請耐心等候審查提交。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.FeatureFlaggedTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Access to the model is required to be able to see the model versions:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要存取模型才能查看模型版本：]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Registration is required for this model: ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.FindingAvailability" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Finding availability...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在尋找可用性...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.GenerallyAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generally available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正式推出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.LearnMoreAvailability" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about region availability]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解區域可用性]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Learn more about regional availability]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.Lifecycle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Lifecycle]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生命週期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.LowerPlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[model versions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.MaxRequest" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max request]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大要求數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.ModelId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.NoVersions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No model versions available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有可用的模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.NotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.NotForProd" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not suitable for production use, please proceed with caution]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不適合生產環境使用，繼續使用時請謹慎]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.Preview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預覽]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.RetirementDate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retirement Date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[淘汰日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ClearChatError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clear the output to start a new dialog.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[清除輸出以開始新的對話方塊。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Common.Archive" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Archive]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[封存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Common.Restore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restore]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[還原]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Common.Restoring" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restoring]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在還原]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConnectionSelector.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service resource]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.ClickHere" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click {here} to learn more about Azure AI Content Safety and how to use it.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下 {here} 以深入了解 Azure AI 內容安全及其使用方法。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.ComingSoon" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This option will deploy in a notebook. Wizard support coming soon!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此選項將會部署在筆記本中。即將推出精靈支援!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Enable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy with Azure AI Content Safety (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure AI 內容安全部署 (預覽)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Enable Azure AI Content Safety (preview)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.EnableShort" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Re-enable Azure AI Content Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新啟用 Azure AI 內容安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Infotext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Content safety detects harmful user-generated and AI-generated content. Enabling content safety on your deployment can help your application comply with regulations or maintain the intended environment for your users.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[內容安全會偵測有害的使用者產生和 AI 產生的內容。在部署上啟用內容安全可協助您的應用程式遵守法規，或維護使用者預定的環境。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.LearnMoreAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Azure AI Content Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Azure AI 內容安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Options.Disabled.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model will be deployed without Azure AI Content Safety. You may be at higher risk of exposing users to harmful content in generated text.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型將在沒有 Azure AI 內容安全的情況下部署。您可能會有較高的風險，讓使用者暴露於所產生文字中的有害內容。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This model will be deployed without the Azure AI content moderation safe filters. By proceeding in this manner, there is a risk of exposing users to offensive or inappropriate content in your generated text, which could have a negative impact on their experience.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Options.Disabled.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Skip Azure AI Content Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跳過 Azure AI 內容安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Options.Enabled.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use Azure AI Content Safety to enable a safer content experience for both inputs and outputs. Azure AI Content Safety can detect and filter harmful content in four categories (hate, sexual, violence, and self-harm) at specified severity levels.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure AI 內容安全，為輸入和輸出啟用更安全的內容體驗。Azure AI 內容安全可偵測並篩選四種類別的有害內容 (仇恨、色情、暴力及自我傷害)，並分為特定嚴重性等級。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use Azure AI Content Safety to actively ensure safe content moderation for both your input and output. Azure AI Content Safety will efficiently and swiftly filter out offensive or inappropriate content in your text, aiming to enhance online experiences for your users.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Options.Enabled.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable Azure AI Content Safety (Recommended)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用 Azure AI 內容安全 (建議)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Proceed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Proceed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繼續]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.PromptToLaunch" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To create a deployment with content safety enabled, click {here}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要建立已啟用內容安全的部署，請按一下 {here}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.PromptToLaunchAria" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a deployment with content safety enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立已啟用內容安全的部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Warning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The model will be deployed without Azure AI content moderation safe filters. By proceeding in this manner, there is a risk of exposing users to offensive or inappropriate content in your generated text, which could have a negative impact on their experience.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型會在沒有 Azure AI 內容仲裁管理安全篩選的情況下部署。以這種方式繼續，使用者可能有接觸到您產生文字中具有冒犯性或不適當內容的風險，這可能會影響他們的體驗。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The model will be deployed without Azure AI content moderation safe filters. By proceeding in this manner, there is a risk of exposing users to offensive or inappropriate content in my generated text, which could have a negative impact on their experience.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateEvalWithModelAndPromptDialog.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateEvalWithModelAndPromptDialog.Create" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateEvalWithModelAndPromptDialog.CreateANewEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a new evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新的評估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.AGIEval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{zhong2023agieval,]A;title={AGIEval: A Human-Centric Benchmark for Evaluating Foundation Models}, ]A;author={Wanjun Zhong and Ruixiang Cui and Yiduo Guo and Yaobo Liang and Shuai Lu and Yanlin Wang and Amin Saied and Weizhu Chen and Nan Duan},]A;year={2023},]A;eprint={2304.06364},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{zhong2023agieval,]A;title={AGIEval: A Human-Centric Benchmark for Evaluating Foundation Models}, ]A;author={Wanjun Zhong and Ruixiang Cui and Yiduo Guo and Yaobo Liang and Shuai Lu and Yanlin Wang and Amin Saied and Weizhu Chen and Nan Duan},]A;year={2023},]A;eprint={2304.06364},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.AGIEval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AGIEval is a human-centric benchmark specifically designed to evaluate the general abilities of foundation models in tasks pertinent to human cognition and problem-solving. This benchmark is derived from 20 official, public, and high-standard admission and qualification exams intended for general human test-takers, such as general college admission tests (e.g., Chinese College Entrance Exam (Gaokao) and American SAT), law school admission tests, math competitions, lawyer qualification tests, and national civil service exams.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AGIEval 是一個以人為中心的基準，專門設計來評估基礎模型在與人類認知和解決問題相關任務中的一般能力。此基準衍生自針對一般應試者的 20 個官方、公開及高標準的入學及資格測驗，例如普通大學入學考試 (亦即中國的高考和美國的 SAT)、法學院入學考試、數學競賽、律師資格考試及國家公務員考試。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.AGIEval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AGIEval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AGIEval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.AGIEval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/microsoft/AGIEval",]A;"arxiv": "https://arxiv.org/pdf/2304.06364.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/microsoft/AGIEval",]A;"arxiv": "https://arxiv.org/pdf/2304.06364.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArenaHard.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{li2024crowdsourceddatahighqualitybenchmarks,]A;title={From Crowdsourced Data to High-Quality Benchmarks: Arena-Hard and BenchBuilder Pipeline},]A;author={Tianle Li and Wei-Lin Chiang and Evan Frick and Lisa Dunlap and Tianhao Wu and Banghua Zhu and Joseph E. Gonzalez and Ion Stoica},]A;year={2024},]A;eprint={2406.11939},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG},]A;url={https://arxiv.org/abs/2406.11939}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{li2024crowdsourceddatahighqualitybenchmarks，]A;title={從 Crowdsourced 數據到 High-Quality 基準： Arena-Hard 與 BenchBuilder 管線}，]A;author={Evanle Li and Wei-Lin Evan Frick and Evan Frick and Dunlap and]A;year={2024}，]A;eprint={2406.11939}，]A;archivePrefix={arXiv}，]A;primaryClass={cs。LG}，]A;url={https://arxiv.org/abs/2406.11939}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArenaHard.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Arena-Hard-Auto-v0.1 is an automatic evaluation tool for instruction-tuned LLMs. It contains 500 challenging user queries sourced from Chatbot Arena. We prompt GPT-4-Turbo as judge to compare the models' responses against a baseline model (default: GPT-4-0314). Notably, Arena-Hard-Auto has the highest correlation and separability to Chatbot Arena among popular open-ended LLM benchmarks. If you are curious to see how well your model might perform on Chatbot Arena, we recommend trying Arena-Hard-Auto.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Arena-Hard-Auto-v0.1 是指示微調 LLM 的自動評估工具。It contains 500 challenging user queries sourced from Chatbot Arena.我們提示 GPT-4-Turbo 做為判斷，以比較模型回應與基準模型 (預設值： GPT-4-0314)。在熱門的開放式 LLM 基準中，Arena-Hard-Auto 與 Chatbot Arena 具有最高的相互關聯性和分隔性。如果您想知道您的模型在 Chatbot 競技場上的效能如何，建議您嘗試 Arena-Hard-Auto。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArenaHard.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Arena Hard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[競技場硬式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArenaHard.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/lmarena/arena-hard-auto",]A;"arxiv": "https://arxiv.org/abs/2406.11939",]A;"webpage": "https://lmsys.org/blog/2024-04-19-arena-hard/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[“github”： "https://github.com/lmarena/arena-hard-auto"，]A;“arxiv”： "https://arxiv.org/abs/2406.11939"，]A;“網頁”： "https://lmsys.org/blog/2024-04-19-arena-hard/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArguAna.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{wachsmuth:2018a,]A;author={Wachsmuth, Henning and Syed, Shahbaz and Stein, Benno},]A;title={Retrieval of the Best Counterargument without Prior Topic Knowledge},]A;booktitle ={Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers){,]A;year={2018},]A;publisher={Association for Computational Linguistics},]A;location={Melbourne, Australia},]A;pages={241--251},]A;url={http://aclweb.org/anthology/P18-1023}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{wachsmuth:2018a,]A;author={Wachsmuth, Henning and Syed, Shahbaz and Stein, Benno},]A;title={Retrieval of the Best Counterargument without Prior Topic Knowledge},]A;booktitle ={Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers){,]A;year={2018},]A;publisher={Association for Computational Linguistics},]A;location={Melbourne, Australia},]A;pages={241--251},]A;url={http://aclweb.org/anthology/P18-1023}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArguAna.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A Full-Text Learning to Rank Dataset for Medical Information Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用於為醫療資訊擷取對資料集進行排序的全文學習]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArguAna.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ArguAna]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ArguAna]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArguAna.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/arguana",]A;"webpage": "http://argumentation.bplaced.net/arguana/data"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/arguana",]A;"webpage": "http://argumentation.bplaced.net/arguana/data"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BUCC.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{zweigenbaum-etal-2017-overview,]A;title = {Overview of the Second {BUCC} Shared Task: Spotting Parallel Sentences in Comparable Corpora},]A;author = {Zweigenbaum, Pierre  and]A;Sharoff, Serge  and]A;Rapp, Reinhard},]A;editor = {Sharoff, Serge  and]A;Zweigenbaum, Pierre  and]A;Rapp, Reinhard},]A;booktitle = {Proceedings of the 10th Workshop on Building and Using Comparable Corpora},]A;month = aug,]A;year = {2017},]A;address = {Vancouver, Canada},]A;publisher = {Association for Computational Linguistics},]A;url = {https://aclanthology.org/W17-2512},]A;doi = {10.18653/v1/W17-2512},]A;pages = {60--67},]A;abstract = {This paper presents the BUCC 2017 shared task on parallel sentence extraction from comparable corpora. It recalls the design of the datasets, presents their final construction and statistics and the methods used to evaluate system results. 13 runs were submitted to the shared task by 4 teams, covering three of the four proposed language pairs: French-English (7 runs), German-English (3 runs), and Chinese-English (3 runs). The best F-scores as measured against the gold standard were 0.84 (German-English), 0.80 (French-English), and 0.43 (Chinese-English). Because of the design of the dataset, in which not all gold parallel sentence pairs are known, these are only minimum values. We examined manually a small sample of the false negative sentence pairs for the most precise French-English runs and estimated the number of parallel sentence pairs not yet in the provided gold standard. Adding them to the gold standard leads to revised estimates for the French-English F-scores of at most +1.5pt. This suggests that the BUCC 2017 datasets provide a reasonable approximate evaluation of the parallel sentence spotting task.},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{zweigenbaum-etal-2017-overview,]A;title = {Overview of the Second {BUCC} Shared Task: Spotting Parallel Sentences in Comparable Corpora},]A;author = {Zweigenbaum, Pierre  and]A;Sharoff, Serge  and]A;Rapp, Reinhard},]A;editor = {Sharoff, Serge  and]A;Zweigenbaum, Pierre  and]A;Rapp, Reinhard},]A;booktitle = {Proceedings of the 10th Workshop on Building and Using Comparable Corpora},]A;month = aug,]A;year = {2017},]A;address = {Vancouver, Canada},]A;publisher = {Association for Computational Linguistics},]A;url = {https://aclanthology.org/W17-2512},]A;doi = {10.18653/v1/W17-2512},]A;pages = {60--67},]A;abstract = {本文呈現了從可比較的文句中平行句子擷取的 BUCC 2017 共用工作。它會重新叫用資料集的設計、呈現其最終結構與統計資料，以及用來評估系統結果的方法。4 個小組已提交 13 個執行至共用工作，涵蓋四個建議的語言組之中的三個: 法文-英文 (7 個執行)、德文-英文 (3 個執行) 和中文-英文 (3 個執行)。以黃金標準衡量的最佳 F 分數是 0.84 (德文-英文)、0.80 (法文-英文) 和 0.43 (中文-英文)。由於資料集的設計，其中並非所有黃金平行句子組都是已知的，因此這些只是最小值。我們已手動檢查最精確法文-英文句子的誤判句子組小樣本，並估計尚未達到所提供的黃金標準的平行句子組的數量。將它們新增到黃金標準會導致法文-英文 F 分數的修訂估計最多為 +1.5 分。這表示，BUCC 2017 資料集提供平行句子發現工作的合理近似評估。}，}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BUCC.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The BUCC mining task is a shared task on parallel sentence extraction from two monolingual corpora with a subset of them assumed to be parallel, and that has been available since 2016. For each language pair, the shared task provides a monolingual corpus for each language and a gold mapping list containing true translation pairs. These pairs are the ground truth. The task is to construct a list of translation pairs from the monolingual corpora. The constructed list is compared to the ground truth, and evaluated in terms of the F1 measure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BUCC 採礦工作是一項共用工作，從兩個單一語言主體進行平行句子擷取，假設其中的一個子集是並行的，且自 2016 年即已提供。針對每個語言組，共用工作會提供每個語言的單一語言主體，以及包含真實翻譯組的黃金對應清單。這些組是基準真相。工作就是從單一語言主體建構翻譯組清單。建構的清單會與基準真相進行比較，並根據 F1 度量進行評估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BUCC.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BUCC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BUCC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BUCC.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/bucc-bitext-mining",]A;"webpage": "https://aclanthology.org/W17-2512.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/bucc-bitext-mining",]A;"webpage": "https://aclanthology.org/W17-2512.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Banking77Classification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{casanueva2020efficient,]A;title={Efficient Intent Detection with Dual Sentence Encoders}, ]A;author={Iñigo Casanueva and Tadas Temčinas and Daniela Gerz and Matthew Henderson and Ivan Vulić},]A;year={2020},]A;eprint={2003.04807},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{casanueva2020efficient,]A;title={Efficient Intent Detection with Dual Sentence Encoders},]A;author={Iñigo Casanueva and Tadas Temčinas and Daniela Gerz and Matthew Henderson and Ivan Vulić},]A;year={2020},]A;eprint={2003.04807},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Banking77Classification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset composed of online banking queries annotated with their corresponding intents.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由線上銀行查詢所組成的資料集，並標註其對應意圖。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Banking77Classification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Banking77Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Banking77Classification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Banking77Classification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/banking77",]A;"arxiv": "https://arxiv.org/abs/2003.04807"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/banking77",]A;"arxiv": "https://arxiv.org/abs/2003.04807"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigBenchHard.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{srivastava2023beyond,]A;title={Beyond the Imitation Game: Quantifying and extrapolating the capabilities of language models},]A;author={BIG-bench authors},]A;journal={Transactions on Machine Learning Research},]A;issn={2835-8856},]A;year={2023},]A;url={https://openreview.net/forum?id=uyTL5Bvosj},]A;note={}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{srivastava2023beyond,]A;title={Beyond the Imitation Game: Quantifying and extrapolating the capabilities of language models},]A;author={BIG-bench authors},]A;journal={Transactions on Machine Learning Research},]A;issn={2835-8856},]A;year={2023},]A;url={https://openreview.net/forum?id=uyTL5Bvosj},]A;note={}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigBenchHard.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Beyond the Imitation Game Benchmark (BIG-bench) is a collaborative benchmark of more than 200 tasks intended to probe large language models and extrapolate their future capabilities. Task topics are diverse, drawing problems from linguistics, childhood development, math, common-sense reasoning, biology, physics, social bias, software development, and beyond. BIG-Bench focuses on tasks that are believed to be beyond the capabilities of current language models. **BIG-Bench Hard (BBH)** is a suite of 23 challenging BIG-Bench tasks for which prior language model evaluations did not outperform the average human-rater.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Beyond the Benchmark Game Benchmark (BIG-bench) 是一個超過 200 項任務的協作基準，目的是要探測大型語言模型並推斷其未來的功能。任務主題很多元，牽涉到語言學、兒童發展、數學、常識推理、生物學、物理學、社會偏見、軟體開發等問題。BIG-Bench 著重在被認為超出目前語言模型能力的任務。**BIG-Bench Hard (BBH)** 是一套由 23 項具有挑戰性的 BIG-Bench 任務所組成的套件，先前的語言模型對這些任務的評估並未超過真人評分者的平均值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigBenchHard.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BigBench-Hard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BigBench-Hard]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigBenchHard.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google/BIG-bench/tree/main",]A;"arxiv": "https://arxiv.org/abs/2210.09261",]A;"webpage": "https://github.com/suzgunmirac/BIG-Bench-Hard"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google/BIG-bench/tree/main",]A;"arxiv": "https://arxiv.org/abs/2210.09261",]A;"webpage": "https://github.com/suzgunmirac/BIG-Bench-Hard"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigCodeBenchInstruct.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={Program Synthesis with Large Language Models},]A;author={Jacob Austin, Augustus Odena, Maxwell Nye, Maarten Bosma, Henryk Michalewski, David Dohan, Ellen Jiang, Carrie Cai, Michael Terry, Quoc Le, Charles Sutton},]A;year={2021},]A;eprint={2108.07732},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={Program Synthesis with Large Language Models},]A;author={Jacob Austin, Augustus Odena, Maxwell Nye, Maarten Bosma, Henryk Michalewski, David Dohan, Ellen Jiang, Carrie Cai, Michael Terry, Quoc Le, Charles Sutton},]A;year={2021},]A;eprint={2108.07732},]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigCodeBenchInstruct.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Beyond the Imitation Game Benchmark (BIG-bench) is a collaborative benchmark of more than 200 tasks intended to probe large language models and extrapolate their future capabilities. Task topics are diverse, drawing problems from linguistics, childhood development, math, common-sense reasoning, biology, physics, social bias, software development, and beyond. BIG-Bench focuses on tasks that are believed to be beyond the capabilities of current language models. **BIG-Bench Hard (BBH)** is a suite of 23 challenging BIG-Bench tasks for which prior language model evaluations did not outperform the average human-rater.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Beyond the Benchmark Game Benchmark (BIG-bench) 是一個超過 200 項任務的協作基準，目的是要探測大型語言模型並推斷其未來的功能。任務主題很多元，牽涉到語言學、兒童發展、數學、常識推理、生物學、物理學、社會偏見、軟體開發等問題。BIG-Bench 著重在被認為超出目前語言模型能力的任務。**BIG-Bench Hard (BBH)** 是一套由 23 項具有挑戰性的 BIG-Bench 任務所組成的套件，先前的語言模型對這些任務的評估並未超過真人評分者的平均值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigCodeBenchInstruct.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BigCodeBench (instruct)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BigCodeBench (instruct)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigCodeBenchInstruct.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"hugging_face": "https://huggingface.co/datasets/mbpp",]A;"arxiv": "https://arxiv.org/pdf/2108.07732.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"hugging_face": "https://huggingface.co/datasets/mbpp",]A;"arxiv": "https://arxiv.org/pdf/2108.07732.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BoolQ.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{clark2019boolq,]A;title =     {BoolQ: Exploring the Surprising Difficulty of Natural Yes/No Questions},]A;author =    {Clark, Christopher and Lee, Kenton and Chang, Ming-Wei, and Kwiatkowski, Tom and Collins, Michael, and Toutanova, Kristina},]A;booktitle = {NAACL},]A;year = {2019},]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{clark2019boolq,]A;title =     {BoolQ: Exploring the Surprising Difficulty of Natural Yes/No Questions},]A;author =    {Clark, Christopher and Lee, Kenton and Chang, Ming-Wei, and Kwiatkowski, Tom and Collins, Michael, and Toutanova, Kristina},]A;booktitle = {NAACL},]A;year = {2019},]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BoolQ.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BoolQ is a question answering dataset for yes/no questions containing 15942 examples. These questions are naturally occurring - they are generated in unprompted and unconstrained settings. Each example is a triplet of (question, passage, answer), with the title of the page as optional additional context. The text-pair classification setup is similar to existing natural language inference tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BoolQ 是一個包含 15942 個範例是非題的問題回答資料集。這些問題會自然出現 - 它們是在沒有提示且不受限制的環境中生成。每個範例都是 (問題，段落，答案) 的三元組，頁面標題則是選擇性的附加內容。文字組分類設定與現有的自然語言推論任務類似。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BoolQ.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BoolQ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BoolQ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BoolQ.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google-research-datasets/boolean-questions",]A;"hugging_face": "https://huggingface.co/datasets/boolq",]A;"arxiv": "https://arxiv.org/pdf/1905.10044.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google-research-datasets/boolean-questions",]A;"hugging_face": "https://huggingface.co/datasets/boolq",]A;"arxiv": "https://arxiv.org/pdf/1905.10044.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.CodeXGlue.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{DBLP:journals/corr/abs-2102-04664,]A;author    = {Shuai Lu and]A;Daya Guo and]A;Shuo Ren and]A;Junjie Huang and]A;Alexey Svyatkovskiy and]A;Ambrosio Blanco and]A;Colin B. Clement and]A;Dawn Drain and]A;Daxin Jiang and]A;Duyu Tang and]A;Ge Li and]A;Lidong Zhou and]A;Linjun Shou and]A;Long Zhou and]A;Michele Tufano and]A;Ming Gong and]A;Ming Zhou and]A;Nan Duan and]A;Neel Sundaresan and]A;Shao Kun Deng and]A;Shengyu Fu and]A;Shujie Liu},]A;title     = {CodeXGLUE: {A} Machine Learning Benchmark Dataset for Code Understanding]A;and Generation},]A;journal   = {CoRR},]A;volume    = {abs/2102.04664},]A;year      = {2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{DBLP:journals/corr/abs-2102-04664,]A;author    = {Shuai Lu and]A;Daya Guo and]A;Shuo Ren and]A;Junjie Huang and]A;Alexey Svyatkovskiy and]A;Ambrosio Blanco and]A;Colin B. Clement and]A;Dawn Drain and]A;Daxin Jiang and]A;Duyu Tang and]A;Ge Li and]A;Lidong Zhou and]A;Linjun Shou and]A;Long Zhou and]A;Michele Tufano and]A;Ming Gong and]A;Ming Zhou and]A;Nan Duan and]A;Neel Sundaresan and]A;Shao Kun Deng and]A;Shengyu Fu and]A;Shujie Liu},]A;title     = {CodeXGLUE: {A} Machine Learning Benchmark Dataset for Code Understanding]A;and Generation},]A;journal   = {CoRR},]A;volume    = {abs/2102.04664},]A;year      = {2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.CodeXGlue.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CodeXGLUE stands for General Language Understanding Evaluation benchmark for CODE. It is a benchmark dataset for code intelligence.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CodeXGLUE 代表 CODE 的一般　Language Understanding 評估基準。這是程式碼情報的基準資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.CodeXGlue.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CodeXGlue Clone Detection BigCloneBench]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CodeXGlue 複製偵測 BigCloneBench]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.CodeXGlue.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/microsoft/codexglue_method_generation"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/microsoft/codexglue_method_generation"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.DROP.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{Dua2019DROP,]A;author={Dheeru Dua and Yizhong Wang and Pradeep Dasigi and Gabriel Stanovsky and Sameer Singh and Matt Gardner},]A;title={  {DROP}: A Reading Comprehension Benchmark Requiring Discrete Reasoning Over Paragraphs},]A;booktitle={Proc. of NAACL},]A;year={2019}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{Dua2019DROP,]A;author={Dheeru Dua and Yizhong Wang and Pradeep Dasigi and Gabriel Stanovsky and Sameer Singh and Matt Gardner},]A;title={  {DROP}: A Reading Comprehension Benchmark Requiring Discrete Reasoning Over Paragraphs},]A;booktitle={Proc. of NAACL},]A;year={2019}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.DROP.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DROP: A Reading Comprehension Benchmark Requiring Discrete Reasoning Over Paragraphs. DROP is a crowdsourced, adversarially-created, 96k-question benchmark, in which a system must resolve references in a question, perhaps to multiple input positions, and perform discrete operations over them (such as addition, counting, or sorting). These operations require a much more comprehensive understanding of the content of paragraphs than what was necessary for prior datasets.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DROP: 一種需要對各個段落進行離散推理的閱讀理解基準。DROP 是一個群眾外包、以對抗方式建立，包含 96,000 個問題的基準，在這個基準中，系統必須解析問題中的參考資料 (可能提及多個輸入位置)，並對這些參考資料執行離散操作 (例如相加、計數或排序)。與先前的資料集相比，這些操作需要更全面性地理解段落內容。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.DROP.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DROP]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DROP]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.DROP.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/drop",]A;"arxiv": "https://arxiv.org/pdf/1903.00161.pdf",]A;"webpage": "https://allenai.org/data/drop"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/drop",]A;"arxiv": "https://arxiv.org/pdf/1903.00161.pdf",]A;"webpage": "https://allenai.org/data/drop"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FEVER.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{Thorne18Fever,]A;author = {Thorne, James and Vlachos, Andreas and Christodoulopoulos, Christos and Mittal, Arpit},]A;title = {{FEVER}: a Large-scale Dataset for Fact Extraction and {VERification}},]A;booktitle = {NAACL-HLT},]A;year = {2018}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{Thorne18Fever,]A;author = {Thorne, James and Vlachos, Andreas and Christodoulopoulos, Christos and Mittal, Arpit},]A;title = {{FEVER}: a Large-scale Dataset for Fact Extraction and {VERification}},]A;booktitle = {NAACL-HLT},]A;year = {2018}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FEVER.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FEVER (Fact Extraction and VERification) consists of 185,445 claims generated by altering sentences extracted from Wikipedia and subsequently verified without knowledge of the sentence they were derived from.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[事實擷取和驗證 (FEVER) 是由 185,445 個宣告所產生，這些宣告是透過改變從維琪百科擷取的句子所產生，並隨後在不知道它們源自的句子的情況下進行驗證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FEVER.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FEVER]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FEVER]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FEVER.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/fever",]A;"webpage": "https://fever.ai/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/fever",]A;"webpage": "https://fever.ai/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FloresBitextMining.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{nllb2022,]A;author    = {NLLB Team, Marta R. Costa-jussà, James Cross, Onur Çelebi, Maha Elbayad, Kenneth Heafield, Kevin Heffernan, Elahe Kalbassi,  Janice Lam, Daniel Licht, Jean Maillard, Anna Sun, Skyler Wang, Guillaume Wenzek, Al Youngblood, Bapi Akula, Loic Barrault, Gabriel Mejia Gonzalez, Prangthip Hansanti, John Hoffman, Semarley Jarrett, Kaushik Ram Sadagopan, Dirk Rowe, Shannon Spruit, Chau Tran, Pierre Andrews, Necip Fazil Ayan, Shruti Bhosale, Sergey Edunov, Angela Fan, Cynthia Gao, Vedanuj Goswami, Francisco Guzmán, Philipp Koehn, Alexandre Mourachko, Christophe Ropers, Safiyyah Saleem, Holger Schwenk, Jeff Wang},]A;title     = {No Language Left Behind: Scaling Human-Centered Machine Translation},]A;year      = {2022}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{nllb2022,]A;author    = {NLLB Team, Marta R. Costa-jussà, James Cross, Onur Çelebi, Maha Elbayad, Kenneth Heafield, Kevin Heffernan, Elahe Kalbassi,  Janice Lam, Daniel Licht, Jean Maillard, Anna Sun, Skyler Wang, Guillaume Wenzek, Al Youngblood, Bapi Akula, Loic Barrault, Gabriel Mejia Gonzalez, Prangthip Hansanti, John Hoffman, Semarley Jarrett, Kaushik Ram Sadagopan, Dirk Rowe, Shannon Spruit, Chau Tran, Pierre Andrews, Necip Fazil Ayan, Shruti Bhosale, Sergey Edunov, Angela Fan, Cynthia Gao, Vedanuj Goswami, Francisco Guzmán, Philipp Koehn, Alexandre Mourachko, Christophe Ropers, Safiyyah Saleem, Holger Schwenk, Jeff Wang},]A;title     = {No Language Left Behind: Scaling Human-Centered Machine Translation},]A;year      = {2022}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FloresBitextMining.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FLORES is a benchmark dataset for machine translation between English and low-resource languages.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FLORES 是英文及低資源語言之間機器翻譯的基準資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FloresBitextMining.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FloresBitextMining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FloresBitextMining]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FloresBitextMining.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/facebook/flores"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/facebook/flores"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GPQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{rein2023gpqagraduatelevelgoogleproofqa,]A;title={GPQA: A Graduate-Level Google-Proof Q&A Benchmark},]A;author={David Rein and Betty Li Hou and Asa Cooper Stickland and Jackson Petty and Richard Yuanzhe Pang and Julien Dirani and Julian Michael and Samuel R. Bowman},]A;year={2023},]A;eprint={2311.12022},]A;archivePrefix={arXiv},]A;primaryClass={cs.AI},]A;url={https://arxiv.org/abs/2311.12022}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{rein2023gpqagraduatelevelgoogleproofqa，]A;title={GPQA： A Graduate-Level Google-Proof QA Benchmark}，]A;author={David Rein and Betty Li Julie and Asa Richard Stickland and RichardHouzhe and Richard Yenzhe Yen and Julien Dirani and Julien Dirani and Samuel R. Bowman}，]A;year={2023}，]A;eprint={2311.12022}，]A;archivePrefix={arXiv}，]A;primaryClass={cs。AI}，]A;url={#A(&A)2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GPQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPQA is a multiple-choice, Q&A dataset of very hard questions written and validated by experts in biology, physics, and chemistry. When attempting questions out of their own domain (e.g., a physicist answers a chemistry question), these experts get only 34% accuracy, despite spending >30m with full access to Google.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPQA 是一個多重選擇的QA數據集，其中包含由生物學、物理和化學專家撰寫及驗證的非常困難的問題。當嘗試來自自己網域的問題時 (例如，物理學家回答化學問題) 時，這些專家雖然花費 >30m 並擁有Google的完整存取權，但這些專家只能 34% 正確性(&A)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GPQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GPQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/Idavidrein/gpqa",]A;"arxiv": "https://arxiv.org/pdf/2311.12022"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[“hugging_face”： "https://huggingface.co/datasets/Idavidrein/gpqa"，]A;“arxiv”： "https://arxiv.org/pdf/2311.12022"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GSM8K.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{cobbe2021gsm8k,]A;title={Training Verifiers to Solve Math Word Problems},]A;author={Cobbe, Karl and Kosaraju, Vineet and Bavarian, Mohammad and Chen, Mark and Jun, Heewoo and Kaiser, Lukasz and Plappert, Matthias and Tworek, Jerry and Hilton, Jacob and Nakano, Reiichiro and Hesse, Christopher and Schulman, John},]A;journal={arXiv preprint arXiv:2110.14168},]A;year={2021}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{cobbe2021gsm8k,]A;title={Training Verifiers to Solve Math Word Problems},]A;author={Cobbe, Karl and Kosaraju, Vineet and Bavarian, Mohammad and Chen, Mark and Jun, Heewoo and Kaiser, Lukasz and Plappert, Matthias and Tworek, Jerry and Hilton, Jacob and Nakano, Reiichiro and Hesse, Christopher and Schulman, John},]A;journal={arXiv preprint arXiv:2110.14168},]A;year={2021}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GSM8K.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GSM8K is a dataset of 8.5K high quality grade school math word problems created by human problem writers. These problems take between 2 and 8 steps to solve, and solutions primarily involve performing a sequence of elementary calculations using basic arithmetic operations (+ - ×÷) to reach the final answer. A bright middle school student should be able to solve every problem. It can be used for multi-step mathematical reasoning. Chain of thought prompting is a method that enables models to decompose multi-step problems into intermediate steps. In other words, instead of asking the model to predict the answer directly, we ask to see the intermediate steps of the calculation. This technique has been shown in some cases to significantly improve quality.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GSM8K 是一個包含 8,500 個高品質小學數學應用問題的資料集，由人類問題撰寫者所創作。這些問題需要 2 到 8 個步驟才能求出解答，而解答主要牽涉到使用基本的算術運算 (+ - ×÷) 進行一系列簡單計算，才能獲得最終答案。一個聰明的中學生應該能夠解出所有問題。它可以用於多步驟的數學推理。思路鏈提示是一種方法，可以讓模型將多步驟問題分解成許多中間步驟。換句話說，我們不是要求模型直接預測答案，而是要查看計算的中間步驟。這個技術在某些案例中已經證明可以顯著提升品質。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GSM8K.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GSM8K Chain Of Thought]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GSM8K 思考鏈]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GSM8K.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/gsm8k",]A;"arxiv": "https://arxiv.org/abs/2110.14168",]A;"webpage": "https://openai.com/blog/grade-school-math"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/gsm8k",]A;"arxiv": "https://arxiv.org/abs/2110.14168",]A;"webpage": "https://openai.com/blog/grade-school-math"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HellaSwag.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{zellers2019hellaswag,]A;title={HellaSwag: Can a Machine Really Finish Your Sentence?},]A;author={Zellers, Rowan and Holtzman, Ari and Bisk, Yonatan and Farhadi, Ali and Choi, Yejin},]A;booktitle ={Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics},]A;year={2019}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{zellers2019hellaswag,]A;title={HellaSwag: Can a Machine Really Finish Your Sentence?},]A;author={Zellers, Rowan and Holtzman, Ari and Bisk, Yonatan and Farhadi, Ali and Choi, Yejin},]A;booktitle ={Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics},]A;year={2019}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HellaSwag.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HellaSwag is a dataset for commonsense natural language inference, where a machine must select the most likely followup to an event description. The dataset contains 70,000 examples, each with four possible endings, one of which is correct. The dataset is designed to be challenging for state-of-the-art models, by using Adversarial Filtering to select machine-generated wrong answers that are often misclassified by pretrained models. The dataset covers various domains and requires both world knowledge and logical reasoning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HellaSwag 是一個常識自然語言推論的資料集，機器必須在當中選取某個事件描述最有可能的後續發展。資料集包含 70,000 個範例，每個範例都有四個可能的結局，其中一個是正確的。此資料集的設計是要挑戰最先進的模型，透過使用「對抗過濾」選取機器生成的錯誤答案，而這些答案經常被預先定型的模型錯誤分類。資料集涵蓋各種領域，同時需要世界知識和邏輯推理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HellaSwag.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HellaSwag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HellaSwag]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HellaSwag.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/hellaswag",]A;"arxiv": "https://arxiv.org/abs/1905.07830",]A;"webpage": "https://rowanzellers.com/hellaswag"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/hellaswag",]A;"arxiv": "https://arxiv.org/abs/1905.07830",]A;"webpage": "https://rowanzellers.com/hellaswag"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{chen2021evaluating,]A;title={Evaluating Large Language Models Trained on Code},]A;author={Mark Chen and Jerry Tworek and Heewoo Jun and Qiming Yuan and Henrique Ponde de Oliveira Pinto and Jared Kaplan and Harri Edwards and Yuri Burda and Nicholas Joseph and Greg Brockman and Alex Ray and Raul Puri and Gretchen Krueger and Michael Petrov and Heidy Khlaaf and Girish Sastry and Pamela Mishkin and Brooke Chan and Scott Gray and Nick Ryder and Mikhail Pavlov and Alethea Power and Lukasz Kaiser and Mohammad Bavarian and Clemens Winter and Philippe Tillet and Felipe Petroski Such and Dave Cummings and Matthias Plappert and Fotios Chantzis and Elizabeth Barnes and Ariel Herbert-Voss and William Hebgen Guss and Alex Nichol and Alex Paino and Nikolas Tezak and Jie Tang and Igor Babuschkin and Suchir Balaji and Shantanu Jain and William Saunders and Christopher Hesse and Andrew N. Carr and Jan Leike and Josh Achiam and Vedant Misra and Evan Morikawa and Alec Radford and Matthew Knight and Miles Brundage and Mira Murati and Katie Mayer and Peter Welinder and Bob McGrew and Dario Amodei and Sam McCandlish and Ilya Sutskever and Wojciech Zaremba},]A;year={2021},]A;eprint={2107.03374},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{chen2021evaluating,]A;title={Evaluating Large Language Models Trained on Code},]A;author={Mark Chen and Jerry Tworek and Heewoo Jun and Qiming Yuan and Henrique Ponde de Oliveira Pinto and Jared Kaplan and Harri Edwards and Yuri Burda and Nicholas Joseph and Greg Brockman and Alex Ray and Raul Puri and Gretchen Krueger and Michael Petrov and Heidy Khlaaf and Girish Sastry and Pamela Mishkin and Brooke Chan and Scott Gray and Nick Ryder and Mikhail Pavlov and Alethea Power and Lukasz Kaiser and Mohammad Bavarian and Clemens Winter and Philippe Tillet and Felipe Petroski Such and Dave Cummings and Matthias Plappert and Fotios Chantzis and Elizabeth Barnes and Ariel Herbert-Voss and William Hebgen Guss and Alex Nichol and Alex Paino and Nikolas Tezak and Jie Tang and Igor Babuschkin and Suchir Balaji and Shantanu Jain and William Saunders and Christopher Hesse and Andrew N. Carr and Jan Leike and Josh Achiam and Vedant Misra and Evan Morikawa and Alec Radford and Matthew Knight and Miles Brundage and Mira Murati and Katie Mayer and Peter Welinder and Bob McGrew and Dario Amodei and Sam McCandlish and Ilya Sutskever and Wojciech Zaremba},]A;year={2021},]A;eprint={2107.03374},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HumanEval is a dataset for evaluating the functional correctness of code synthesis from natural language docstrings. It consists of 164 hand-written programming problems in Python, each with a function signature, docstring, body, and several unit tests. The problems cover a range of topics, such as language comprehension, reasoning, algorithms, and simple mathematics. The dataset is designed to measure the problem-solving capabilities of large language models trained on code, such as Codex.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HumanEval 是一個評估從自然語言 docstring 合成程式碼的功能正確性的資料集。它是由 Python 中的 164 個手寫程式設計問題所組成，每個問題都有一個函式簽章、docstring、本文和數個單元測試。問題涵蓋一系列的主題，例如語言理解、推理、演算法和簡單的數學。此資料集的設計目的是要測量經過程式碼定型的大型語言模型 (例如 Codex) 解決問題的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HumanEval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HumanEval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/openai/human-eval",]A;"arxiv": "https://arxiv.org/abs/2107.03374"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/openai/human-eval",]A;"arxiv": "https://arxiv.org/abs/2107.03374"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEvalPlus.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{chen2021evaluating,]A;title={Evaluating Large Language Models Trained on Code},]A;author={Mark Chen and Jerry Tworek and Heewoo Jun and Qiming Yuan and Henrique Ponde de Oliveira Pinto and Jared Kaplan and Harri Edwards and Yuri Burda and Nicholas Joseph and Greg Brockman and Alex Ray and Raul Puri and Gretchen Krueger and Michael Petrov and Heidy Khlaaf and Girish Sastry and Pamela Mishkin and Brooke Chan and Scott Gray and Nick Ryder and Mikhail Pavlov and Alethea Power and Lukasz Kaiser and Mohammad Bavarian and Clemens Winter and Philippe Tillet and Felipe Petroski Such and Dave Cummings and Matthias Plappert and Fotios Chantzis and Elizabeth Barnes and Ariel Herbert-Voss and William Hebgen Guss and Alex Nichol and Alex Paino and Nikolas Tezak and Jie Tang and Igor Babuschkin and Suchir Balaji and Shantanu Jain and William Saunders and Christopher Hesse and Andrew N. Carr and Jan Leike and Josh Achiam and Vedant Misra and Evan Morikawa and Alec Radford and Matthew Knight and Miles Brundage and Mira Murati and Katie Mayer and Peter Welinder and Bob McGrew and Dario Amodei and Sam McCandlish and Ilya Sutskever and Wojciech Zaremba},]A;year={2021},]A;eprint={2107.03374},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{chen2021evaluating,]A;title={Evaluating Large Language Models Trained on Code},]A;author={Mark Chen and Jerry Tworek and Heewoo Jun and Qiming Yuan and Henrique Ponde de Oliveira Pinto and Jared Kaplan and Harri Edwards and Yuri Burda and Nicholas Joseph and Greg Brockman and Alex Ray and Raul Puri and Gretchen Krueger and Michael Petrov and Heidy Khlaaf and Girish Sastry and Pamela Mishkin and Brooke Chan and Scott Gray and Nick Ryder and Mikhail Pavlov and Alethea Power and Lukasz Kaiser and Mohammad Bavarian and Clemens Winter and Philippe Tillet and Felipe Petroski Such and Dave Cummings and Matthias Plappert and Fotios Chantzis and Elizabeth Barnes and Ariel Herbert-Voss and William Hebgen Guss and Alex Nichol and Alex Paino and Nikolas Tezak and Jie Tang and Igor Babuschkin and Suchir Balaji and Shantanu Jain and William Saunders and Christopher Hesse and Andrew N. Carr and Jan Leike and Josh Achiam and Vedant Misra and Evan Morikawa and Alec Radford and Matthew Knight and Miles Brundage and Mira Murati and Katie Mayer and Peter Welinder and Bob McGrew and Dario Amodei and Sam McCandlish and Ilya Sutskever and Wojciech Zaremba},]A;year={2021},]A;eprint={2107.03374},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEvalPlus.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HumanEval is a dataset for evaluating the functional correctness of code synthesis from natural language docstrings. It consists of 164 hand-written programming problems in Python, each with a function signature, docstring, body, and several unit tests. The problems cover a range of topics, such as language comprehension, reasoning, algorithms, and simple mathematics. The dataset is designed to measure the problem-solving capabilities of large language models trained on code, such as Codex.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HumanEval 是一個評估從自然語言 docstring 合成程式碼的功能正確性的資料集。它是由 Python 中的 164 個手寫程式設計問題所組成，每個問題都有一個函式簽章、docstring、本文和數個單元測試。問題涵蓋一系列的主題，例如語言理解、推理、演算法和簡單的數學。此資料集的設計目的是要測量經過程式碼定型的大型語言模型 (例如 Codex) 解決問題的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEvalPlus.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HumanEvalPlus]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HumanEvalPlus]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEvalPlus.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/openai/human-eval",]A;"arxiv": "https://arxiv.org/abs/2107.03374"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/openai/human-eval",]A;"arxiv": "https://arxiv.org/abs/2107.03374"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.IFEval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={Instruction-Following Evaluation for Large Language Models},]A;author={Jeffrey Zhou, Tianjian Lu, Swaroop Mishra, Siddhartha Brahma, Sujoy Basu, Yi Luan, Denny Zhou, Le Hou},]A;year={2023},]A;eprint={2311.07911},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={Instruction-Following Evaluation for Large Language Models},]A;author={Jeffrey Zhou, Tianjian Lu, Swaroop Mishra, Siddhartha Brahma, Sujoy Basu, Yi Luan, Denny Zhou, Le Hou},]A;year={2023},]A;eprint={2311.07911},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.IFEval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[IFEval is a dataset that tests the capability of models to clearly follow explicit instructions. The models are tested on their ability to strictly follow formatting instructions rather than the actual contents generated, allowing strict and rigorous metrics to be used. It focuses on a set of "verifiable instructions" such as "write in more than 400 words" and "mention the keyword of AI at least 3 times". There are 25 types of those verifiable instructions and 541 prompts, with each prompt containing one or more verifiable instructions. We use "strict_accuracy" as the primary metric for IFEval, which is defined as the percentage of prompts such that the corresponding response strictly satisfies all verifiable instructions. We also include a secondary metric called "loose_accuracy" that can be found in the AML experiment. The "loose_accuracy" only requires potential transformations of the model response to satisfy all verifiable instructions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[IFEval 是一個資料集，可測試模型清楚地遵循明確指示的能力。系統會測試模型嚴格遵循格式指示的能力，而不是實際產生的內容，允許使用嚴謹且嚴格的計量。它著重於一組「可驗證的指示」，例如「以超過 400 個字撰寫」和「提及 AI 關鍵字至少 3 次」。有 25 種類型的可驗證指示和 541 個提示，而每個提示都包含一或多個可驗證的指示。我們使用 "strict_accuracy" 做為 IFEval 的主要計量，其定義為提示的百分比，因此對應的回應會嚴格滿足所有可驗證的指示。我們也包含一個稱為 "loose_accuracy" 的次要計量，您可在 AML 實驗中找到。"loose_accuracy" 只需要模型回應的潛在轉換，以滿足所有可驗證的指示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.IFEval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[IFEval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[IFEval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.IFEval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/papers/2311.07911",]A;"arxiv": "https://arxiv.org/abs/2311.07911"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/papers/2311.07911",]A;"arxiv": "https://arxiv.org/abs/2311.07911"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ImdbClassification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{maas-etal-2011-learning,]A;title = {Learning Word Vectors for Sentiment Analysis},]A;author = {Maas, Andrew L.  and]A;Daly, Raymond E.  and]A;Pham, Peter T.  and]A;Huang, Dan  and]A;Ng, Andrew Y.  and]A;Potts, Christopher},]A;editor = {Lin, Dekang  and]A;Matsumoto, Yuji  and]A;Mihalcea, Rada},]A;booktitle = {Proceedings of the 49th Annual Meeting of the Association for Computational Linguistics: Human Language Technologies},]A;month = jun,]A;year = {2011},]A;address = {Portland, Oregon, USA},]A;publisher = {Association for Computational Linguistics},]A;url = {https://aclanthology.org/P11-1015},]A;pages = {142--150},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{maas-etal-2011-learning,]A;title = {Learning Word Vectors for Sentiment Analysis},]A;author = {Maas, Andrew L.  and]A;Daly, Raymond E.  and]A;Pham, Peter T.  and]A;Huang, Dan  and]A;Ng, Andrew Y.  and]A;Potts, Christopher},]A;editor = {Lin, Dekang  and]A;Matsumoto, Yuji  and]A;Mihalcea, Rada},]A;booktitle = {Proceedings of the 49th Annual Meeting of the Association for Computational Linguistics: Human Language Technologies},]A;month = jun,]A;year = {2011},]A;address = {Portland, Oregon, USA},]A;publisher = {Association for Computational Linguistics},]A;url = {https://aclanthology.org/P11-1015},]A;pages = {142--150},}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ImdbClassification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Large Movie Review Dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大型影片檢閱資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ImdbClassification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ImdbClassification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ImdbClassification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ImdbClassification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/imdb",]A;"webpage": "http://www.aclweb.org/anthology/P11-1015"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/imdb",]A;"webpage": "http://www.aclweb.org/anthology/P11-1015"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LccSentimentClassification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{quasthoff-etal-2006-corpus,]A;title = {Corpus Portal for Search in Monolingual Corpora},]A;author = {Quasthoff, Uwe  and]A;  Richter, Matthias  and]A;  Biemann, Christian},]A;editor = {Calzolari, Nicoletta  and]A;Choukri, Khalid  and]A;Gangemi, Aldo  and]A;Maegaard, Bente  and]A;Mariani, Joseph  and]A;Odijk, Jan  and]A;Tapias, Daniel},]A;booktitle = {Proceedings of the Fifth International Conference on Language Resources and Evaluation ({LREC}{'}06)},]A;month = may,]A;year = {2006},]A;address = {Genoa, Italy},]A;publisher = {European Language Resources Association (ELRA)},]A;url = {http://www.lrec-conf.org/proceedings/lrec2006/pdf/641_pdf.pdf},]A;abstract = {A simple and flexible schema for storing and presenting monolingual language resources is proposed. In this format, data for 18 different languages is already available in various sizes. The data is provided free of charge for online use and download. The main target is to ease the application of algorithms for monolingual and interlingual studies.},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{quasthoff-etal-2006-corpus,]A;title = {Corpus Portal for Search in Monolingual Corpora},]A;author = {Quasthoff, Uwe  and]A;  Richter, Matthias  and]A;  Biemann, Christian},]A;editor = {Calzolari, Nicoletta  and]A;Choukri, Khalid  and]A;Gangemi, Aldo  and]A;Maegaard, Bente  and]A;Mariani, Joseph  and]A;Odijk, Jan  and]A;Tapias, Daniel},]A;booktitle = {Proceedings of the Fifth International Conference on Language Resources and Evaluation ({LREC}{'}06)},]A;month = may,]A;year = {2006},]A;address = {Genoa, Italy},]A;publisher = {European Language Resources Association (ELRA)},]A;url = {http://www.lrec-conf.org/proceedings/lrec2006/pdf/641_pdf.pdf},]A;abstract = {建議用來儲存及呈現單一語言資源的簡單彈性結構描述。已有 18 種不同語言的資料使用此格式以各種大小提供。資料為免費提供線上使用和下載。主要目標是簡化單一語言及語言間研究的演算法套用。}，}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LccSentimentClassification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A simple and flexible schema for storing and presenting monolingual language resources is proposed. In this format, data for 18 different languages is already available in various sizes. The data is provided free of charge for online use and download. The main target is to ease the application of algorithms for monolingual and interlingual studies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建議一個簡單且彈性的結構描述，用於儲存和呈現單一語言資源。使用此格式，已有 18 種不同語言的資料以各種大小提供。資料為免費提供，供線上使用和下載。主要目標是簡化單語及語言間研究的演算法套用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LccSentimentClassification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LccSentimentClassification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LccSentimentClassification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LccSentimentClassification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/DDSC/lcc",]A;"github": "https://github.com/fnielsen/lcc-sentiment"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/DDSC/lcc",]A;"github": "https://github.com/fnielsen/lcc-sentiment"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBench.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK},]A;author={Colin White, Samuel Dooley, Manley Roberts, Arka Pal, Benjamin Feuer, Siddhartha Jain, Ravid Shwartz-Ziv, Neel Jain, Khalid Saifullah, Sreemanti Dey, Shubh-Agrawal, Sandeep Singh Sandha, Siddartha Naidu, Chinmay Hegde, Yann LeCun, Tom Goldstein, Willie Neiswanger, Micah Goldblum},]A;year={2024},]A;eprint={2406.19314},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK},]A;author={Colin White, Samuel Dooley, Manley Roberts, Arka Pal, Benjamin Feuer, Siddhartha Jain, Ravid Shwartz-Ziv, Neel Jain, Khalid Saifullah, Sreemanti Dey, Shubh-Agrawal, Sandeep Singh Sandha, Siddartha Naidu, Chinmay Hegde, Yann LeCun, Tom Goldstein, Willie Neiswanger, Micah Goldblum},]A;year={2024},]A;eprint={2406.19314},]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBench.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LiveBench is designed to limit potential contamination by releasing new questions monthly, as well as having questions based on recently-released datasets, arXiv papers, news articles, and IMDb movie synopses. Each question has verifiable, objective ground-truth answers, allowing hard questions to be scored accurately and automatically, without the use of an LLM judge. LiveBench currently contains a set of 18 diverse tasks across 6 categories, and we will release new, harder tasks over time. We update questions each month such that the benchmark completely refreshes every 6 months. The initial version was LiveBench-2024-06-24. This is coding tasks of the LiveBench release_date in ["2024-07-26", "2024-06-24"]5D;]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LiveBench 的設計旨在透過每月發行新問題，以及根據最近發行的資料集、arXiv 文件、新聞文章和 IMDb 影片概要提出問題，以限制潛在的污染。每個問題都有可驗證、客觀的真實答案，允許對困難問題進行正確且自動的評分，而不需要使用 LLM 判斷工具。LiveBench 目前包含一組橫跨 6 個類別的 18 個不同工作，我們會隨著時間釋出全新、更困難的工作。我們每個月都會更新問題，如此一來，基準會每 6 個月完全重新整理一次。初始版本是 LiveBench-2024-06-24。這是 ["2024-07-26", "2024-06-24"]5D; 中 LiveBench release_date 的編碼工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBench.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LiveBench (code task)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LiveBench (程式碼工作)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBench.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchEasy.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK},]A;author={Colin White, Samuel Dooley, Manley Roberts, Arka Pal, Benjamin Feuer, Siddhartha Jain, Ravid Shwartz-Ziv, Neel Jain, Khalid Saifullah, Sreemanti Dey, Shubh-Agrawal, Sandeep Singh Sandha, Siddartha Naidu, Chinmay Hegde, Yann LeCun, Tom Goldstein, Willie Neiswanger, Micah Goldblum},]A;year={2024},]A;eprint={2406.19314},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK},]A;author={Colin White, Samuel Dooley, Manley Roberts, Arka Pal, Benjamin Feuer, Siddhartha Jain, Ravid Shwartz-Ziv, Neel Jain, Khalid Saifullah, Sreemanti Dey, Shubh-Agrawal, Sandeep Singh Sandha, Siddartha Naidu, Chinmay Hegde, Yann LeCun, Tom Goldstein, Willie Neiswanger, Micah Goldblum},]A;year={2024},]A;eprint={2406.19314},]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchEasy.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Livecode bench easy set of problems. LiveBench is designed to limit potential contamination by releasing new questions monthly, as well as having questions based on recently-released datasets, arXiv papers, news articles, and IMDb movie synopses. Each question has verifiable, objective ground-truth answers, allowing hard questions to be scored accurately and automatically, without the use of an LLM judge. LiveBench currently contains a set of 18 diverse tasks across 6 categories, and we will release new, harder tasks over time. We update questions each month such that the benchmark completely refreshes every 6 months. The initial version was LiveBench-2024-06-24. This is coding tasks of the LiveBench release_date in ["2024-07-26", "2024-06-24"]5D;]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Livecode Bench 簡易問題集合。LiveBench 的設計旨在透過每月發行新問題，以及根據最近發行的資料集、arXiv 文件、新聞文章和 IMDb 影片概要提出問題，以限制潛在的污染。每個問題都有可驗證、客觀的真實答案，允許對困難問題進行正確且自動的評分，而不需要使用 LLM 判斷工具。LiveBench 目前包含一組橫跨 6 個類別的 18 個不同工作，我們會隨著時間釋出全新、更困難的工作。我們每個月都會更新問題，如此一來，基準會每 6 個月完全重新整理一次。初始版本是 LiveBench-2024-06-24。這是 ["2024-07-26", "2024-06-24"]5D; 中 LiveBench release_date 的編碼工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchEasy.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LiveBench (Easy)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LiveBench (簡易)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchEasy.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchMedium.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK},]A;author={Colin White, Samuel Dooley, Manley Roberts, Arka Pal, Benjamin Feuer, Siddhartha Jain, Ravid Shwartz-Ziv, Neel Jain, Khalid Saifullah, Sreemanti Dey, Shubh-Agrawal, Sandeep Singh Sandha, Siddartha Naidu, Chinmay Hegde, Yann LeCun, Tom Goldstein, Willie Neiswanger, Micah Goldblum},]A;year={2024},]A;eprint={2406.19314},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK},]A;author={Colin White, Samuel Dooley, Manley Roberts, Arka Pal, Benjamin Feuer, Siddhartha Jain, Ravid Shwartz-Ziv, Neel Jain, Khalid Saifullah, Sreemanti Dey, Shubh-Agrawal, Sandeep Singh Sandha, Siddartha Naidu, Chinmay Hegde, Yann LeCun, Tom Goldstein, Willie Neiswanger, Micah Goldblum},]A;year={2024},]A;eprint={2406.19314},]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchMedium.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Livecodebench set of problems of medium difficult. LiveBench is designed to limit potential contamination by releasing new questions monthly, as well as having questions based on recently-released datasets, arXiv papers, news articles, and IMDb movie synopses. Each question has verifiable, objective ground-truth answers, allowing hard questions to be scored accurately and automatically, without the use of an LLM judge. LiveBench currently contains a set of 18 diverse tasks across 6 categories, and we will release new, harder tasks over time. We update questions each month such that the benchmark completely refreshes every 6 months. The initial version was LiveBench-2024-06-24. This is coding tasks of the LiveBench release_date in ["2024-07-26", "2024-06-24"]5D;]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一組中等困難問題的 Livecodebench 集合。LiveBench 的設計旨在透過每月發行新問題，以及根據最近發行的資料集、arXiv 文件、新聞文章和 IMDb 影片概要提出問題，以限制潛在的污染。每個問題都有可驗證、客觀的真實答案，允許對困難問題進行正確且自動的評分，而不需要使用 LLM 判斷工具。LiveBench 目前包含一組橫跨 6 個類別的 18 個不同工作，我們會隨著時間釋出全新、更困難的工作。我們每個月都會更新問題，如此一來，基準會每 6 個月完全重新整理一次。初始版本是 LiveBench-2024-06-24。這是 ["2024-07-26", "2024-06-24"]5D; 中 LiveBench release_date 的編碼工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchMedium.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LiveBench (Medium)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LiveBench (中等)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchMedium.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MATH.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendrycksmath2021,]A;title={Measuring Mathematical Problem Solving With the MATH Dataset},]A;author={Dan Hendrycks and Collin Burns and Saurav Kadavath and Akul Arora and Steven Basart and Eric Tang and Dawn Song and Jacob Steinhardt},]A;journal={NeurIPS},]A;year={2021}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendrycksmath2021,]A;title={Measuring Mathematical Problem Solving With the MATH Dataset},]A;author={Dan Hendrycks and Collin Burns and Saurav Kadavath and Akul Arora and Steven Basart and Eric Tang and Dawn Song and Jacob Steinhardt},]A;journal={NeurIPS},]A;year={2021}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MATH.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MATH is a new dataset of 12,500 challenging competition mathematics problems. Each problem in MATH has a full step-by-step solution which can be used to teach models to generate answer derivations and explanations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MATH 是一個包含 12,500 個具有挑戰性的競賽數學問題的新資料集。MATH 中的每個問題都有完整的逐步詳解，可用來教導模型生成答案的推導和說明。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MATH.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MATH]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MATH]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MATH.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/math/blob/main/README.md",]A;"arxiv": "https://arxiv.org/pdf/2103.03874.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/math/blob/main/README.md",]A;"arxiv": "https://arxiv.org/pdf/2103.03874.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPP.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{austin2021programsynthesislargelanguage,]A;title={Program Synthesis with Large Language Models},]A;author={Jacob Austin and Augustus Odena and Maxwell Nye and Maarten Bosma and Henryk Michalewski and David Dohan and Ellen Jiang and Carrie Cai and Michael Terry and Quoc Le and Charles Sutton},]A;year={2021},]A;eprint={2108.07732},]A;archivePrefix={arXiv},]A;primaryClass={cs.PL},]A;url={https://arxiv.org/abs/2108.07732}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{{{2021programsynthesislargelanguage，]A;title={Program Synthesis with Large Language Models}，]A;author={並列於 1987 年 8 月 12 日，以及 Maarten Bosma 和 David Dohanewski，David Dohan and David 江 and David Cai and Michael and Quoc Le and Charles Tomatoe}，]A;year={2021}，]A;eprint={2108.07732}，]A;archivePrefix={arXiv}，]A;primaryClass={cs.PL}，]A;url={https://arxiv.org/abs/2108.07732}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPP.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mostly Basic Python Problems Dataset (MBPP) consists of around 1,000 crowd-sourced Python programming problems, designed to be solvable by entry level programmers, covering programming fundamentals, standard library functionality, and so on. Each problem consists of a task description, code solution and 3 automated test cases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本 Python 問題數據集 (MBPP) 是由大約 1,000 個人群為來源的 Python 程式設計問題所組成，設計為可供進入層級程式設計師解決，涵蓋程式設計基礎、標準連結庫功能等等。每個問題都包含工作描述、程式代碼解決方案和3個自動化測試案例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPP.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MBPP]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MBPP]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPP.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/google-research-datasets/mbpp",]A;"github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"arxiv": "https://arxiv.org/abs/2108.07732"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[“hugging_face”： "https://huggingface.co/datasets/google-research-datasets/mbpp"，]A;“github”： "https://github.com/google-research/google-research/tree/master/mbpp"，]A;“arxiv”： "https://arxiv.org/abs/2108.07732"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPPPlus.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={Program Synthesis with Large Language Models},]A;author={Jacob Austin, Augustus Odena, Maxwell Nye, Maarten Bosma, Henryk Michalewski, David Dohan, Ellen Jiang, Carrie Cai, Michael Terry, Quoc Le, Charles Sutton},]A;year={2021},]A;eprint={2108.07732},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={Program Synthesis with Large Language Models},]A;author={Jacob Austin, Augustus Odena, Maxwell Nye, Maarten Bosma, Henryk Michalewski, David Dohan, Ellen Jiang, Carrie Cai, Michael Terry, Quoc Le, Charles Sutton},]A;year={2021},]A;eprint={2108.07732},]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPPPlus.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Mostly Basic Python Problems Dataset (MBPP) consists of high-quality crowd-sourced Python programming problems, designed to be solvable by entry-level programmers, covering programming fundamentals, standard library functionality, and so on. Each problem consists of a task description, code solution and 3 automated test cases. The creators provide two sets - mbpp.jsonl which is the full dataset consisting of around 1000 examples, and sanitized_mbpp.json - which is a subset of around 500 examples which the authors verified by hand. We use this smaller, high-quality subset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mostly Basic Python Problems Dataset (MBPP) 包含高品質的群眾外包 Python 程式設計問題，其設計成可由入門級程式設計人員解決，涵蓋程式設計基礎、標準程式庫功能等等。每個問題都包含工作描述、程式碼解決方案和 3 個自動化測試案例。建立者提供兩個集合：mbpp.jsonl (這是包含大約 1000 個範例的完整資料集) 及 sanitized_mbpp.json (這是作者手動驗證的大約 500 個範例的子集)。我們使用這個較小的高品質子集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPPPlus.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MBPPPLUS]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MBPPPLUS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPPPlus.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"hugging_face": "https://huggingface.co/datasets/mbpp",]A;"arxiv": "https://arxiv.org/pdf/2108.07732.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"hugging_face": "https://huggingface.co/datasets/mbpp",]A;"arxiv": "https://arxiv.org/pdf/2108.07732.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringP2P.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{scialom2020mlsum,]A;title={MLSUM: The Multilingual Summarization Corpus},]A;author={Scialom, Thomas and Dray, Paul-Alexis and Lamprier, Sylvain and Piwowarski, Benjamin and Staiano, Jacopo},]A;journal={arXiv preprint arXiv:2004.14900},]A;year={2020}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{scialom2020mlsum,]A;title={MLSUM: The Multilingual Summarization Corpus},]A;author={Scialom, Thomas and Dray, Paul-Alexis and Lamprier, Sylvain and Piwowarski, Benjamin and Staiano, Jacopo},]A;journal={arXiv preprint arXiv:2004.14900},]A;year={2020}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringP2P.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering of newspaper article contents and titles from MLSUM dataset. Clustering of 10 sets on the newpaper article topics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從 MLSUM 資料集群集的報紙文章內容和標題。群集的 10 組報紙文章主題。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringP2P.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLSUMClusteringP2P]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MLSUMClusteringP2P]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringP2P.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mlsum"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mlsum"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringS2S.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{scialom2020mlsum,]A;title={MLSUM: The Multilingual Summarization Corpus},]A;author={Scialom, Thomas and Dray, Paul-Alexis and Lamprier, Sylvain and Piwowarski, Benjamin and Staiano, Jacopo},]A;journal={arXiv preprint arXiv:2004.14900},]A;year={2020}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{scialom2020mlsum,]A;title={MLSUM: The Multilingual Summarization Corpus},]A;author={Scialom, Thomas and Dray, Paul-Alexis and Lamprier, Sylvain and Piwowarski, Benjamin and Staiano, Jacopo},]A;journal={arXiv preprint arXiv:2004.14900},]A;year={2020}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringS2S.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering of newspaper article contents and titles from MLSUM dataset. Clustering of 10 sets on the newpaper article topics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從 MLSUM 資料集群集的報紙文章內容和標題。群集的 10 組報紙文章主題。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringS2S.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLSUMClusteringS2S]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MLSUMClusteringS2S]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringS2S.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mlsum"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mlsum"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Humanities.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendryckstest2021,]A; title={Measuring Massive Multitask Language Understanding},]A; author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt}, ]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendryckstest2021,]A;title={Measuring Massive Multitask Language Understanding},]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Humanities.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Massive Multitask Language Understanding (MMLU) benchmark is a set of multiple choice questions covering 57 different task domains, from a wide spectrum of disciplines including Humanities, Social Sciences, STEM, and others. This dataset record is specific to the Humanities subset of the MMLU benchmark. The difficulty of questions ranges from elementary level knowledge to advanced professional expertise. Subjects include high school european history, high school us history, high school world history, international law, jurisprudence, prehistory, and professional law.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Massive Multitask Language Understanding (MMLU) 基準是一組涵蓋 57 個不同任務領域的選擇題，包括人類學、社會科學、STEM 等各式各樣的領域。此資料集記錄是專屬於 MMLU 基準的人文學科子集。問題的困難度從小學程度的知識到高階的專業知識。主題包括高中歐洲歷史、高中美國歷史、高中世界歷史、國際法、法理學、史前史和專業法。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Humanities.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU Humanities]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU 人文學科]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Humanities.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Other.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendryckstest2021,]A;title={Measuring Massive Multitask Language Understanding},]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendryckstest2021，]A;title={Measuring Massive Multitask Language Understanding}，]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Other.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Massive Multitask Language Understanding (MMLU) benchmark is a set of multiple choice questions covering 57 different task domains, from a wide spectrum of disciplines including Humanities, Social Sciences, STEM, and others.  This dataset record is specific to the "other" subset of the MMLU benchmark. The difficulty of questions ranges from elementary level knowledge to advanced professional expertise. Subjects include anatomy, business ethics, clinical knowledge, college medicine, global facts, human aging, management, marketing, medical genetics, miscellaneous, nutrition, professional accounting, professional medicine, and virology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Massive Multitask Language Understanding (MMLU) 基準是一組涵蓋 57 個不同任務領域的選擇題，包括人類學、社會科學、STEM 等各式各樣的領域。此資料集記錄是專屬於 MMLU 基準的「其他」子集。問題的困難度從小學程度的知識到高階的專業知識。主題包括解剖學、商業道德、臨床知識、大學醫學、全球事實、人類老化、管理學、行銷學、醫學遺傳學、雜項、營養學、專業會計、專業醫學和病毒學。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Other.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU Other]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU 其他]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Other.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Pro.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{wang2024mmluprorobustchallengingmultitask,]A;title={MMLU-Pro: A More Robust and Challenging Multi-Task Language Understanding Benchmark},]A;author={Yubo Wang and Xueguang Ma and Ge Zhang and Yuansheng Ni and Abhranil Chandra and Shiguang Guo and Weiming Ren and Aaran Arulraj and Xuan He and Ziyan Jiang and Tianle Li and Max Ku and Kai Wang and Alex Zhuang and Rongqi Fan and Xiang Yue and Wenhu Chen},]A;year={2024},]A;eprint={2406.01574},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL},]A;url={https://arxiv.org/abs/2406.01574}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{{對應 2024mmluprorobustchallengingmultitask，]A;title={MMLU-Pro： A More Robust and Challenging multi-Task Language Understanding Benchmark}，]A;author={Yubo Directory and 並會 馬峇和 Ge 其元 Ni 以及 Abhranil Chandra 和 Shi shi任 And Weiming 任 and Aaran Arulraj and Invalid He and Ziyan 江 and 方格力和 Max Ku 和 Kai Chen and Alex Chan and Alex Wei and Shiqi Fan and 並會音和 Minghu Chen}，]A;year={2024}，]A;eprint={2406.01574}，]A;archivePrefix={arXiv}，]A;primaryClass={cs。CL}，]A;url={https://arxiv.org/abs/2406.01574}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Pro.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU-Pro Dataset is built on Massive Multitask Language Understanding (MMLU) dataset, MMLU-Pro dataset is a more robust and challenging massive multi-task understanding dataset tailored to more rigorously benchmark large language models' capabilities. This dataset contains 12K complex questions across various disciplines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU-Pro 數據集建置在大型多任務 Language Understanding (MMLU) 數據集上，MMLU-Pro 數據集是更強固且具有挑戰性的大型多任務了解數據集，專為更嚴格地基準式大型語言模型的功能量身打造。此數據集包含 12K 個跨各種專業的複雜問題。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Pro.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU-Pro]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU-Pro]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Pro.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/TIGER-Lab/MMLU-Pro",]A;"arxiv": "https://arxiv.org/abs/2406.01574"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[“hugging_face”： "https://huggingface.co/datasets/TIGER-Lab/MMLU-Pro"，]A;“arxiv”： "https://arxiv.org/abs/2406.01574"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Social_Sciences.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendryckstest2021,]A;title={Measuring Massive Multitask Language Understanding},]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendryckstest2021，]A;title={Measuring Massive Multitask Language Understanding}，]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Social_Sciences.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Massive Multitask Language Understanding (MMLU) benchmark is a set of multiple choice questions covering 57 different task domains, from a wide spectrum of disciplines including Humanities, Social Sciences, STEM, and others. This dataset record is specific to the Social Sciences subset of the MMLU benchmark. The difficulty of questions ranges from elementary level knowledge to advanced professional expertise. Subjects include econometrics, high school geography, high school government and politics, high school macroeconomics, high school microeconomics, high school psychology, human sexuality, professional psychology, public relations, security studies, sociology, and us foreign policy.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Massive Multitask Language Understanding (MMLU) 基準是一組涵蓋 57 個不同任務領域的選擇題，包括人類學、社會科學、STEM 等各式各樣的領域。此資料集記錄是專屬於 MMLU 基準的社會科學子集。問題的困難度從小學程度的知識到高階的專業知識。主題包括計量經濟學、高中地理、高中政府與政治、高中總體經濟學、高中個體經濟學、高中心理學、人類性學、專業心理學、公共關係、安全研究、社會學，以及美國外交政策。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Social_Sciences.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU Social Sciences]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU 社會科學]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Social_Sciences.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Stem.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendryckstest2021,]A;title={Measuring Massive Multitask Language Understanding},]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendryckstest2021，]A;title={Measuring Massive Multitask Language Understanding}，]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Stem.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Massive Multitask Language Understanding (MMLU) benchmark is a set of multiple choice questions covering 57 different task domains, from a wide spectrum of disciplines including Humanities, Social Sciences, STEM, and others. This dataset record is specific to the Science, Technology, Engineering, and Mathematics (STEM) subset of the MMLU benchmark. The difficulty of questions ranges from elementary level knowledge to advanced professional expertise. Subjects include abstract algebra, astronomy, college biology, college chemistry, college computer science, college mathematics, college physics, computer security, conceptual physics, electrical engineering, elementary mathematics, high school biology, high school chemistry, high school computer science, high school mathematics, high school physics, high school statistics, and machine learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Massive Multitask Language Understanding (MMLU) 基準是一組涵蓋 57 個不同任務領域的選擇題，包括人類學、社會科學、STEM 等各式各樣的領域。此資料集記錄專屬於 MMLU 基準的科學、技術、工程與數學 (STEM) 子集。問題的困難度從小學程度的知識到高階的專業知識。主題包括抽象代數、天文學、大學生物、大學化學、大學電腦科學、大學數學、大學物理、電腦安全、概念物理、電子工程、初級數學、高中生物、高中化學、高中電腦科學、高中數學、高中物理、高中物理、高中統計學和機器學習。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Stem.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU STEM]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU STEM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Stem.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringP2P.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{Adelani2023MasakhaNEWS,]A;title={MasakhaNEWS: News Topic Classification for African languages},]A;author={David Ifeoluwa Adelani and  Marek Masiak and  Israel Abebe Azime and  Jesujoba Oluwadara Alabi and  Atnafu Lambebo Tonja and  Christine Mwase and  Odunayo Ogundepo and  Bonaventure F. P. Dossou and  Akintunde Oladipo and  Doreen Nixdorf and  Chris Chinenye Emezue and  Sana Sabah al-azzawi and  Blessing K. Sibanda and  Davis David and  Lolwethu Ndolela and  Jonathan Mukiibi and  Tunde Oluwaseyi Ajayi and  Tatiana Moteu Ngoli and  Brian Odhiambo and  Abraham Toluwase Owodunni and  Nnaemeka C. Obiefuna and  Shamsuddeen Hassan Muhammad and  Saheed Salahudeen Abdullahi and  Mesay Gemeda Yigezu and  Tajuddeen Gwadabe and  Idris Abdulmumin and  Mahlet Taye Bame and  Oluwabusayo Olufunke Awoyomi and  Iyanuoluwa Shode and  Tolulope Anu Adelani and  Habiba Abdulganiy Kailani and  Abdul-Hakeem Omotayo and  Adetola Adeeko and  Afolabi Abeeb and  Anuoluwapo Aremu and  Olanrewaju Samuel and  Clemencia Siro and  Wangari Kimotho and  Onyekachi Raphael Ogbu and  Chinedu E. Mbonu and  Chiamaka I. Chukwuneke and  Samuel Fanijo and  Jessica Ojo and  Oyinkansola F. Awosan and  Tadesse Kebede Guge and  Sakayo Toadoum Sari and  Pamela Nyatsine and  Freedmore Sidume and  Oreen Yousuf and  Mardiyyah Oduwole and  Ussen Kimanuka and  Kanda Patrick Tshinu and  Thina Diko and  Siyanda Nxakama and   Abdulmejid Tuni Johar and  Sinodos Gebre and  Muhidin Mohamed and  Shafie Abdi Mohamed and  Fuad Mire Hassan and  Moges Ahmed Mehamed and  Evrard Ngabire and  and Pontus Stenetorp},]A;journal={ArXiv},]A;year={2023},]A;volume={}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{Adelani2023MasakhaNEWS,]A;title={MasakhaNEWS: News Topic Classification for African languages},]A;author={David Ifeoluwa Adelani and  Marek Masiak and  Israel Abebe Azime and  Jesujoba Oluwadara Alabi and  Atnafu Lambebo Tonja and  Christine Mwase and  Odunayo Ogundepo and  Bonaventure F. P. Dossou and  Akintunde Oladipo and  Doreen Nixdorf and  Chris Chinenye Emezue and  Sana Sabah al-azzawi and  Blessing K. Sibanda and  Davis David and  Lolwethu Ndolela and  Jonathan Mukiibi and  Tunde Oluwaseyi Ajayi and  Tatiana Moteu Ngoli and  Brian Odhiambo and  Abraham Toluwase Owodunni and  Nnaemeka C. Obiefuna and  Shamsuddeen Hassan Muhammad and  Saheed Salahudeen Abdullahi and  Mesay Gemeda Yigezu and  Tajuddeen Gwadabe and  Idris Abdulmumin and  Mahlet Taye Bame and  Oluwabusayo Olufunke Awoyomi and  Iyanuoluwa Shode and  Tolulope Anu Adelani and  Habiba Abdulganiy Kailani and  Abdul-Hakeem Omotayo and  Adetola Adeeko and  Afolabi Abeeb and  Anuoluwapo Aremu and  Olanrewaju Samuel and  Clemencia Siro and  Wangari Kimotho and  Onyekachi Raphael Ogbu and  Chinedu E. Mbonu and  Chiamaka I. Chukwuneke and  Samuel Fanijo and  Jessica Ojo and  Oyinkansola F. Awosan and  Tadesse Kebede Guge and  Sakayo Toadoum Sari and  Pamela Nyatsine and  Freedmore Sidume and  Oreen Yousuf and  Mardiyyah Oduwole and  Ussen Kimanuka and  Kanda Patrick Tshinu and  Thina Diko and  Siyanda Nxakama and   Abdulmejid Tuni Johar and  Sinodos Gebre and  Muhidin Mohamed and  Shafie Abdi Mohamed and  Fuad Mire Hassan and  Moges Ahmed Mehamed and  Evrard Ngabire and  and Pontus Stenetorp},]A;journal={ArXiv},]A;year={2023},]A;volume={}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringP2P.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering of news article headlines from MasakhaNEWS dataset. Clustering of 10 sets on the news article label.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從 MasakhaNEWS 資料集群集的新聞文章標題。群集的 10 組新聞文章標籤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringP2P.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MasakhaNEWSClusteringP2P]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MasakhaNEWSClusteringP2P]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringP2P.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/masakhane/masakhanews"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/masakhane/masakhanews"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringS2S.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{Adelani2023MasakhaNEWS,]A;title={MasakhaNEWS: News Topic Classification for African languages},]A;author={David Ifeoluwa Adelani and  Marek Masiak and  Israel Abebe Azime and  Jesujoba Oluwadara Alabi and  Atnafu Lambebo Tonja and  Christine Mwase and  Odunayo Ogundepo and  Bonaventure F. P. Dossou and  Akintunde Oladipo and  Doreen Nixdorf and  Chris Chinenye Emezue and  Sana Sabah al-azzawi and  Blessing K. Sibanda and  Davis David and  Lolwethu Ndolela and  Jonathan Mukiibi and  Tunde Oluwaseyi Ajayi and  Tatiana Moteu Ngoli and  Brian Odhiambo and  Abraham Toluwase Owodunni and  Nnaemeka C. Obiefuna and  Shamsuddeen Hassan Muhammad and  Saheed Salahudeen Abdullahi and  Mesay Gemeda Yigezu and  Tajuddeen Gwadabe and  Idris Abdulmumin and  Mahlet Taye Bame and  Oluwabusayo Olufunke Awoyomi and  Iyanuoluwa Shode and  Tolulope Anu Adelani and  Habiba Abdulganiy Kailani and  Abdul-Hakeem Omotayo and  Adetola Adeeko and  Afolabi Abeeb and  Anuoluwapo Aremu and  Olanrewaju Samuel and  Clemencia Siro and  Wangari Kimotho and  Onyekachi Raphael Ogbu and  Chinedu E. Mbonu and  Chiamaka I. Chukwuneke and  Samuel Fanijo and  Jessica Ojo and  Oyinkansola F. Awosan and  Tadesse Kebede Guge and  Sakayo Toadoum Sari and  Pamela Nyatsine and  Freedmore Sidume and  Oreen Yousuf and  Mardiyyah Oduwole and  Ussen Kimanuka and  Kanda Patrick Tshinu and  Thina Diko and  Siyanda Nxakama and   Abdulmejid Tuni Johar and  Sinodos Gebre and  Muhidin Mohamed and  Shafie Abdi Mohamed and  Fuad Mire Hassan and  Moges Ahmed Mehamed and  Evrard Ngabire and  and Pontus Stenetorp},]A;journal={ArXiv},]A;year={2023},]A;volume={}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{Adelani2023MasakhaNEWS,]A;title={MasakhaNEWS: News Topic Classification for African languages},]A;author={David Ifeoluwa Adelani and  Marek Masiak and  Israel Abebe Azime and  Jesujoba Oluwadara Alabi and  Atnafu Lambebo Tonja and  Christine Mwase and  Odunayo Ogundepo and  Bonaventure F. P. Dossou and  Akintunde Oladipo and  Doreen Nixdorf and  Chris Chinenye Emezue and  Sana Sabah al-azzawi and  Blessing K. Sibanda and  Davis David and  Lolwethu Ndolela and  Jonathan Mukiibi and  Tunde Oluwaseyi Ajayi and  Tatiana Moteu Ngoli and  Brian Odhiambo and  Abraham Toluwase Owodunni and  Nnaemeka C. Obiefuna and  Shamsuddeen Hassan Muhammad and  Saheed Salahudeen Abdullahi and  Mesay Gemeda Yigezu and  Tajuddeen Gwadabe and  Idris Abdulmumin and  Mahlet Taye Bame and  Oluwabusayo Olufunke Awoyomi and  Iyanuoluwa Shode and  Tolulope Anu Adelani and  Habiba Abdulganiy Kailani and  Abdul-Hakeem Omotayo and  Adetola Adeeko and  Afolabi Abeeb and  Anuoluwapo Aremu and  Olanrewaju Samuel and  Clemencia Siro and  Wangari Kimotho and  Onyekachi Raphael Ogbu and  Chinedu E. Mbonu and  Chiamaka I. Chukwuneke and  Samuel Fanijo and  Jessica Ojo and  Oyinkansola F. Awosan and  Tadesse Kebede Guge and  Sakayo Toadoum Sari and  Pamela Nyatsine and  Freedmore Sidume and  Oreen Yousuf and  Mardiyyah Oduwole and  Ussen Kimanuka and  Kanda Patrick Tshinu and  Thina Diko and  Siyanda Nxakama and   Abdulmejid Tuni Johar and  Sinodos Gebre and  Muhidin Mohamed and  Shafie Abdi Mohamed and  Fuad Mire Hassan and  Moges Ahmed Mehamed and  Evrard Ngabire and  and Pontus Stenetorp},]A;journal={ArXiv},]A;year={2023},]A;volume={}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringS2S.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering of news article headlines from MasakhaNEWS dataset. Clustering of 10 sets on the news article label.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從 MasakhaNEWS 資料集群集的新聞文章標題。群集的 10 組新聞文章標籤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringS2S.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MasakhaNEWSClusteringS2S]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MasakhaNEWSClusteringS2S]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringS2S.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/masakhane/masakhanews"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/masakhane/masakhanews"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MultiNLI.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@InProceedings{N18-1101,]A;author = {Williams, Adina]A;and Nangia, Nikita]A;and Bowman, Samuel},]A;title = {A Broad-Coverage Challenge Corpus for ]A;Sentence Understanding through Inference},]A;booktitle = {Proceedings of the 2018 Conference of ]A;the North American Chapter of the ]A;Association for Computational Linguistics:]A;Human Language Technologies, Volume 1 (Long]A;Papers)},]A;year = {2018},]A;publisher = {Association for Computational Linguistics},]A;pages = {1112--1122},]A;location = {New Orleans, Louisiana},]A;url = {http://aclweb.org/anthology/N18-1101}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@InProceedings{N18-1101,]A;author = {Williams, Adina]A;and Nangia, Nikita]A;and Bowman, Samuel},]A;title = {A Broad-Coverage Challenge Corpus for]A;Sentence Understanding through Inference},]A;booktitle = {Proceedings of the 2018 Conference of]A;the North American Chapter of the]A;Association for Computational Linguistics:]A;Human Language Technologies, Volume 1 (Long]A;Papers)},]A;year = {2018},]A;publisher = {Association for Computational Linguistics},]A;pages = {1112--1122},]A;location = {New Orleans, Louisiana},]A;url = {http://aclweb.org/anthology/N18-1101}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MultiNLI.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Multi-Genre Natural Language Inference (MultiNLI) corpus is a crowd-sourced dataset of 433k sentence pairs annotated with textual entailment information, which served as the basis for the shared task of the RepEval 2017 Workshop at EMNLP in Copenhagen (https://repeval2017.github.io/shared/). This corpus is modeled after the Stanford NLI Corpus (SNLI), but differs in that covers a range of genres of spoken and written text, and supports a distinctive cross-genre generalization evaluation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多類型自然語言推斷 (MultiNLI) 語料庫是一組群眾外包資料集，其中有 43.3 萬組的成對句子，並以文字蘊涵訊息標註，作為哥本哈根 EMNLP 的 RepEval 2017 研討會共同工作的基礎 (https://repeval2017.github.io/shared/)。此語料庫以 Stanford NLI Corpus (SNLI) 為模型，但不同的是，它涵蓋了一系列口語和書寫文字的內容類型，並支援特殊的跨內容類型一般化評估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MultiNLI.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MultiNLI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MultiNLI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MultiNLI.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["webpage": "https://cims.nyu.edu/~sbowman/multinli/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["webpage": "https://cims.nyu.edu/~sbowman/multinli/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NaturalQuestions.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{47761,]A;title={Natural Questions: a Benchmark for Question Answering Research},]A;author={Tom Kwiatkowski and Jennimaria Palomaki and Olivia Redfield and Michael Collins and Ankur Parikh and Chris Alberti and Danielle Epstein and Illia Polosukhin and Matthew Kelcey and Jacob Devlin and Kenton Lee and Kristina N. Toutanova and Llion Jones and Ming-Wei Chang and Andrew Dai and Jakob Uszkoreit and Quoc Le and Slav Petrov},]A;year={2019},]A;journal={Transactions of the Association of Computational Linguistics}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{47761,]A;title={Natural Questions: a Benchmark for Question Answering Research},]A;author={Tom Kwiatkowski and Jennimaria Palomaki and Olivia Redfield and Michael Collins and Ankur Parikh and Chris Alberti and Danielle Epstein and Illia Polosukhin and Matthew Kelcey and Jacob Devlin and Kenton Lee and Kristina N. Toutanova and Llion Jones and Ming-Wei Chang and Andrew Dai and Jakob Uszkoreit and Quoc Le and Slav Petrov},]A;year={2019},]A;journal={Transactions of the Association of Computational Linguistics}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NaturalQuestions.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The NQ corpus contains questions from real users, and it requires QA systems to read and comprehend an entire Wikipedia article that may or may not contain the answer to the question. The inclusion of real user questions, and the requirement that solutions should read an entire page to find the answer, cause NQ to be a more realistic and challenging task than prior QA datasets.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NQ 語料庫包含來自真實使用者的問題，要求問答系統閱讀並理解不一定包含問題答案的整篇維基百科文章。由於是實際使用者的問題，而且必須閱讀一整頁才能找到答案，比起先前的問答資料集，NQ 是一個更實際也更具挑戰性的任務。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NaturalQuestions.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NaturalQuestions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NaturalQuestions]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NaturalQuestions.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google-research-datasets/natural-questions",]A;"hugging_face": "https://huggingface.co/datasets/natural_questions",]A;"webpage": "https://ai.google.com/research/NaturalQuestions"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google-research-datasets/natural-questions",]A;"hugging_face": "https://huggingface.co/datasets/natural_questions",]A;"webpage": "https://ai.google.com/research/NaturalQuestions"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NoData.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NoData.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NoData.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This dataset does not have a description yet. Please check back later.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此資料集還沒有描述。請稍後再回來查看。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NoData.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.OpenBookQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{OpenBookQA2018,]A;title={Can a Suit of Armor Conduct Electricity? A New Dataset for Open Book Question Answering},]A;author={Todor Mihaylov and Peter Clark and Tushar Khot and Ashish Sabharwal},]A;booktitle={EMNLP},]A;year={2018}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{OpenBookQA2018,]A;title={Can a Suit of Armor Conduct Electricity? A New Dataset for Open Book Question Answering},]A;author={Todor Mihaylov and Peter Clark and Tushar Khot and Ashish Sabharwal},]A;booktitle={EMNLP},]A;year={2018}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.OpenBookQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OpenBookQA is a dataset of 5,957 multiple-choice questions that require reasoning over a collection of 1,326 elementary science facts and additional external knowledge. The questions are designed to test the understanding of a broad range of topics and phenomena, and are not answerable from the provided facts alone. The questions cover various aspects of language understanding, such as coreference, word sense disambiguation, and logical inference. The dataset is modeled after open book exams, where students are allowed to consult a set of facts, but have to apply them to novel situations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OpenBookQA 是一個包含 5,957 個選擇題的資料集，需要對 1,326 個以上的基礎科學事實及其他外部知識進行推理。這些問題的設計目的是要測試對各種主題和現象的理解，無法單獨從提供的事實得出答案。問題涵蓋語言理解的各個層面，例如指代、詞義消岐和邏輯推論。資料集是在開放式測驗之後建立模型，學生在測驗中可以查閱一組事實，但必須將事實應用在新的情境。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.OpenBookQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OpenBookQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OpenBookQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.OpenBookQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/openbookqa",]A;"arxiv": "https://arxiv.org/abs/1809.02789"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/openbookqa",]A;"arxiv": "https://arxiv.org/abs/1809.02789"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Opusparcus.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{opusparcus-v1_en,]A;author={Mathias Creutz},]A;year={2018-01-01},]A;title={{Opusparcus: Open Subtitles Paraphrase Corpus for Six Languages (version 1.0)}},]A;publisher={Kielipankki},]A;type={data set},]A;url={http://urn.fi/urn:nbn:fi:lb-2018021221},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{opusparcus-v1_en,]A;author={Mathias Creutz},]A;year={2018-01-01},]A;title={{Opusparcus: Open Subtitles Paraphrase Corpus for Six Languages (version 1.0)}},]A;publisher={Kielipankki},]A;type={data set},]A;url={http://urn.fi/urn:nbn:fi:lb-2018021221},}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Opusparcus.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Opusparcus is a paraphrase corpus for six European language: German, English, Finnish, French, Russian, and Swedish. The paraphrases consist of subtitles from movies and TV shows.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Opusparcus 是六種歐洲語言的釋義主體: 德文、英文、芬蘭文、法文、俄文和瑞典文。此釋義由電影與電視節目的字幕所組成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Opusparcus.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Opusparcus]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Opusparcus]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Opusparcus.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/GEM/opusparcus",]A;"webpage": "https://metashare.csc.fi/repository/browse/opusparcus-open-subtitles-paraphrase-corpus-for-six-languages-version-10/****************************************************************"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/GEM/opusparcus",]A;"webpage": "https://metashare.csc.fi/repository/browse/opusparcus-open-subtitles-paraphrase-corpus-for-six-languages-version-10/****************************************************************"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.PIQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{Bisk2020,]A;author = {Yonatan Bisk and Rowan Zellers and Ronan Le Bras and Jianfeng Gao and Yejin Choi},]A;title = {PIQA: Reasoning about Physical Commonsense in Natural Language},]A;booktitle = {Thirty-Fourth AAAI Conference on Artificial Intelligence},]A;year = {2020},]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{Bisk2020,]A;author = {Yonatan Bisk and Rowan Zellers and Ronan Le Bras and Jianfeng Gao and Yejin Choi},]A;title = {PIQA: Reasoning about Physical Commonsense in Natural Language},]A;booktitle = {Thirty-Fourth AAAI Conference on Artificial Intelligence},]A;year = {2020},]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.PIQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PIQA is a dataset of 16,000 multiple-choice questions that require physical commonsense reasoning to be answered correctly. The questions are inspired by instructables.com, a website that provides users with instructions on how to build, craft, bake, or manipulate objects using everyday materials. The questions focus on everyday situations with a preference for atypical solutions, and challenge state-of-the-art natural language understanding systems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PIQA 是一個包含 16,000 個選擇題，需要正確回答物理常識推理的資料集。問題靈感來自 instructables.com，該網站提供使用者有關如何使用日常材料建造、製作、烘焙或操作物品的說明。問題著重在偏好非慣用解決方法的日常情境，並挑戰最先進的自然語言理解系統。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.PIQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PIQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PIQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.PIQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/piqa",]A;"arxiv": "https://arxiv.org/abs/1911.11641",]A;"webpage": "https://yonatanbisk.com/piqa/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/piqa",]A;"arxiv": "https://arxiv.org/abs/1911.11641",]A;"webpage": "https://yonatanbisk.com/piqa/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuAC.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{choi-etal-2018-quac,]A;title="{Q}u{AC}: Question Answering in Context",]A;author="Choi, Eunsol  and He, He andIyyer, Mohit  andYatskar, Mark  andYih, Wen-tau  andChoi, Yejin  andLiang, Percy  andZettlemoyer, Luke",]A;booktitle="Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing",]A;month=oct # "-" # nov,]A;year = "2018",]A;address="Brussels, Belgium",]A;publisher="Association for Computational Linguistics",]A;url="https://www.aclweb.org/anthology/D18-1241",]A;doi="10.18653/v1/D18-1241",]A;pages="2174--2184",]A;abstract="We present QuAC, a dataset for Question Answering in Context that contains 14K information-seeking QA dialogs (100K questions in total). The dialogs involve two crowd workers: (1) a student who poses a sequence of freeform questions to learn as much as possible about a hidden Wikipedia text, and (2) a teacher who answers the questions by providing short excerpts from the text. QuAC introduces challenges not found in existing machine comprehension datasets: its questions are often more open-ended, unanswerable, or only meaningful within the dialog context, as we show in a detailed qualitative evaluation. We also report results for a number of reference models, including a recently state-of-the-art reading comprehension architecture extended to model dialog context. Our best model underperforms humans by 20 F1, suggesting that there is significant room for future work on this data. Dataset, baseline, and leaderboard available at url http://quac.ai."}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{choi-etal-2018-quac,]A;title="{Q}u{AC}: Question Answering in Context",]A;author="Choi, Eunsol  and He, He andIyyer, Mohit  andYatskar, Mark  andYih, Wen-tau  andChoi, Yejin  andLiang, Percy  andZettlemoyer, Luke",]A;booktitle="Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing",]A;month=oct # "-" # nov,]A;year = "2018",]A;address="Brussels, Belgium",]A;publisher="Association for Computational Linguistics",]A;url="https://www.aclweb.org/anthology/D18-1241",]A;doi="10.18653/v1/D18-1241",]A;pages="2174--2184",]A;abstract="We present QuAC, a dataset for Question Answering in Context that contains 14K information-seeking QA dialogs (100K questions in total). The dialogs involve two crowd workers: (1) a student who poses a sequence of freeform questions to learn as much as possible about a hidden Wikipedia text, and (2) a teacher who answers the questions by providing short excerpts from the text. QuAC introduces challenges not found in existing machine comprehension datasets: its questions are often more open-ended, unanswerable, or only meaningful within the dialog context, as we show in a detailed qualitative evaluation. We also report results for a number of reference models, including a recently state-of-the-art reading comprehension architecture extended to model dialog context. Our best model underperforms humans by 20 F1, suggesting that there is significant room for future work on this data. Dataset, baseline, and leaderboard available at url http://quac.ai."}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuAC.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question Answering in Context is a dataset for modeling, understanding, and participating in information seeking dialog. Data instances consist of an interactive dialog between two crowd workers: (1) a student who poses a sequence of freeform questions to learn as much as possible about a hidden Wikipedia text, and (2) a teacher who answers the questions by providing short excerpts (spans) from the text. QuAC introduces challenges not found in existing machine comprehension datasets: its questions are often more open-ended, unanswerable, or only meaningful within the dialog context.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Question Answering in Context 是一個用於建模、理解及參與資訊尋找對話的資料集。資料實例由兩個群眾工作者之間的互動式對話組成: (1) 一個學生提出一連串型式不拘的問題，盡可能了解隱藏的維基百科文字，(2) 一個老師從該文字提供簡短的摘錄 (片段) 來回答問題。QuAC 引出現有機器理解資料集還未發現的挑戰: 問題通常比較開放式、無法回答，或只有在該對話情境中才有意義。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuAC.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[QuAC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[QuAC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuAC.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/quac",]A;"arxiv": "https://arxiv.org/abs/1808.07036",]A;"webpage": "https://quac.ai"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/quac",]A;"arxiv": "https://arxiv.org/abs/1808.07036",]A;"webpage": "https://quac.ai"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuoraRetrieval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuoraRetrieval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[QuoraRetrieval is based on questions that are marked as duplicates on the Quora platform. Given a question, find other (duplicate) questions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[QuoraRetrieval 是以在 Quora 平台上標示為重複的問題為基礎。給定一個問題，尋出其他 (重複) 問題。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuoraRetrieval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[QuoraRetrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[QuoraRetrieval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuoraRetrieval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/quora",]A;"webpage": "https://quoradata.quora.com/First-Quora-Dataset-Release-Question-Pairs"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/quora",]A;"webpage": "https://quoradata.quora.com/First-Quora-Dataset-Release-Question-Pairs"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SIQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{sap2019socialiqa,]A;title={SocialIQA: Commonsense Reasoning about Social Interactions},]A;author={Maarten Sap and Hannah Rashkin and Derek Chen and Ronan LeBras and Yejin Choi},]A;year={2019},]A;eprint={1904.09728},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{sap2019socialiqa,]A;title={SocialIQA: Commonsense Reasoning about Social Interactions},]A;author={Maarten Sap and Hannah Rashkin and Derek Chen and Ronan LeBras and Yejin Choi},]A;year={2019},]A;print={1904.09728},]A;rchivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SIQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We introduce Social IQa: Social Interaction QA, a new question-answering benchmark for testing social commonsense intelligence. Contrary to many prior benchmarks that focus on physical or taxonomic knowledge, Social IQa focuses on reasoning about people’s actions and their social implications. For example, given an action like "Jesse saw a concert" and a question like "Why did Jesse do this?", humans can easily infer that Jesse wanted "to see their favorite performer" or "to enjoy the music", and not "to see what's happening inside" or "to see if it works". The actions in Social IQa span a wide variety of social situations, and answer candidates contain both human-curated answers and adversarially-filtered machine-generated candidates. Social IQa contains over 37,000 QA pairs for evaluating models’ abilities to reason about the social implications of everyday events and situations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們介紹 Social IQa: Social Interaction QA，這是一個測試社交常識智慧的新問題解答基準。先前許多基準都著重在物理或分類知識，Social IQa 則著重在推論人們的動作及其社交含意。例如，如果指出像是「捷生看了一場音樂會」的動作，以及問了像是「捷生為什麼這樣做?」的問題，人類可以輕鬆推論出，因為捷生想要「看他最愛的表演者」或「享受音樂」，而不是「看裡面發生什麼事」或「看看它是否有用」。Social IQa 中的動作涵蓋各種社交情境，候選答案同時包含人類策劃的答案，以及經過對抗過濾的機器生成答案。Social IQa 包含超過 37,000 組問答，用於評估模型理解日常活動和情境的社交含意的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SIQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SIQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SIQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SIQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/social_i_qa",]A;"arxiv": "https://arxiv.org/pdf/1904.09728.pdf",]A;"webpage": "https://leaderboard.allenai.org/socialiqa/submissions/get-started"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/social_i_qa",]A;"arxiv": "https://arxiv.org/pdf/1904.09728.pdf",]A;"webpage": "https://leaderboard.allenai.org/socialiqa/submissions/get-started"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.STSBenchmark.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.STSBenchmark.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Semantic Textual Similarity Benchmark (STSbenchmark) dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語意文字相似性基準 (STSbenchmark) 資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.STSBenchmark.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[STSBenchmark]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[STSBenchmark]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.STSBenchmark.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/stsbenchmark-sts"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/stsbenchmark-sts"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SciDocsRR.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{specter2020cohan,]A;title={SPECTER: Document-level Representation Learning using Citation-informed Transformers},]A;author={Arman Cohan and Sergey Feldman and Iz Beltagy and Doug Downey and Daniel S. Weld},]A;booktitle={ACL},]A;year={2020}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{specter2020cohan,]A;title={SPECTER: Document-level Representation Learning using Citation-informed Transformers},]A;author={Arman Cohan and Sergey Feldman and Iz Beltagy and Doug Downey and Daniel S. Weld},]A;booktitle={ACL},]A;year={2020}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SciDocsRR.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ranking of related scientific papers based on their title.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相關科學論文的排名，根據其標題。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SciDocsRR.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SciDocsRR]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SciDocsRR]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SciDocsRR.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/scidocs-reranking",]A;"webpage": "https://allenai.org/data/scidocs"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/scidocs-reranking",]A;"webpage": "https://allenai.org/data/scidocs"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{2016arXiv160605250R,]A;author = {{Rajpurkar}, Pranav and {Zhang}, Jian and {Lopyrev}, Konstantin and {Liang}, Percy},]A;title = "{SQuAD: 100,000+ Questions for Machine Comprehension of Text}",]A;journal = {arXiv e-prints},]A;year = 2016,]A;eid = {arXiv:1606.05250},]A;pages = {arXiv:1606.05250},]A;archivePrefix = {arXiv},]A;eprint = {1606.05250},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{2016arXiv160605250R,]A;author = {{Rajpurkar}, Pranav and {Zhang}, Jian and {Lopyrev}, Konstantin and {Liang}, Percy},]A;title = "{SQuAD: 100,000+ Questions for Machine Comprehension of Text}",]A;journal = {arXiv e-prints},]A;year = 2016,]A;eid = {arXiv:1606.05250},]A;pages = {arXiv:1606.05250},]A;archivePrefix = {arXiv},]A;eprint = {1606.05250},}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stanford Question Answering Dataset (SQuAD) is a reading comprehension dataset, consisting of questions posed by crowdworkers on a set of Wikipedia articles, where the answer to every question is a segment of text, or span, from the corresponding reading passage, or the question might be unanswerable.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stanford Question Answering Dataset (SQuAD) 是一個閱讀理解資料集，由群眾工作者對一組維基百科文章提出的問題組成，其中每個問題的答案都是對應閱讀段落的一段文字 (或稱為片段)，問題也有可能無法回答。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[squad]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[squad]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/squad",]A;"arxiv": "https://arxiv.org/pdf/1606.05250.pdf",]A;"webpage": "https://rajpurkar.github.io/SQuAD-explorer"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/squad",]A;"arxiv": "https://arxiv.org/pdf/1606.05250.pdf",]A;"webpage": "https://rajpurkar.github.io/SQuAD-explorer"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad_v2.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{rajpurkar-etal-2018-know,]A;title = "Know What You Don{'}t Know: Unanswerable Questions for {SQ}u{AD}",]A;author = "Rajpurkar, Pranav and Jia, Robin and Liang, Percy",]A;editor = "Gurevych, Iryna  and Miyao, Yusuke",]A;booktitle = "Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers)",]A;month = "jul",]A;year = "2018",]A;address = "Melbourne, Australia",]A;publisher = "Association for Computational Linguistics",]A;url = "https://aclanthology.org/P18-2124",]A;doi = "10.18653/v1/P18-2124",]A;pages = "784--789",]A;eprint="1806.03822",]A;archivePrefix="arXiv",]A;primaryClass="cs.CL"]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{rajpurkar-etal-2018-know,]A;title = "Know What You Don{'}t Know: Unanswerable Questions for {SQ}u{AD}",]A;author = "Rajpurkar, Pranav and Jia, Robin and Liang, Percy",]A;editor = "Gurevych, Iryna  and Miyao, Yusuke",]A;booktitle = "Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers)",]A;month = "jul",]A;year = "2018",]A;address = "Melbourne, Australia",]A;publisher = "Association for Computational Linguistics",]A;url = "https://aclanthology.org/P18-2124",]A;doi = "10.18653/v1/P18-2124",]A;pages = "784--789",]A;eprint="1806.03822",]A;archivePrefix="arXiv",]A;primaryClass="cs.CL"]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad_v2.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stanford Question Answering Dataset (SQuAD) is a reading comprehension dataset, consisting of questions posed by crowdworkers on a set of Wikipedia articles, where the answer to every question is a segment of text, or span, from the corresponding reading passage, or the question might be unanswerable. SQuAD 2.0 combines the 100,000 questions in SQuAD1.1 with over 50,000 unanswerable questions written adversarially by crowdworkers to look similar to answerable ones. To do well on SQuAD2.0, systems must not only answer questions when possible, but also determine when no answer is supported by the paragraph and abstain from answering. We have used 20% of rows in validation rows sampled at random to compute the benchmark results.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stanford Question Answering Dataset (SQuAD) 是一個閱讀理解資料集，由群眾工作者對一組維基百科文章提出的問題組成，其中每個問題的答案都是對應閱讀段落的一段文字 (或稱為片段)，問題也有可能無法回答。SQuAD 2.0 結合了 SQuAD1.1 中的 100,000 個問題，以及由群眾工作者以對抗方式撰寫、看起來類似可回答之超過 50,000 個未回答的問題。為了在 SQuAD2.0 上有良好的表現，系統除了必須盡可能回答問題，還必須判斷何時段落不支援任何答案並放棄回答。我們在隨機取樣的驗證資料列中使用了 20% 的資料列來計算基準結果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad_v2.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[squad_v2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[squad_v2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad_v2.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/rajpurkar/squad_v2",]A;"arxiv": "https://arxiv.org/abs/1806.03822",]A;"webpage": "https://rajpurkar.github.io/SQuAD-explorer"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/rajpurkar/squad_v2",]A;"arxiv": "https://arxiv.org/abs/1806.03822",]A;"webpage": "https://rajpurkar.github.io/SQuAD-explorer"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SummEval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{fabbri2020summeval,]A;title={SummEval: Re-evaluating Summarization Evaluation},]A;author={Fabbri, Alexander R and Kryscinski, Wojciech and McCann, Bryan and Xiong, Caiming and Socher, Richard and Radev, Dragomir},]A;journal={arXiv preprint arXiv:2007.12626},]A;year={2020}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{fabbri2020summeval,]A;title={SummEval: Re-evaluating Summarization Evaluation},]A;author={Fabbri, Alexander R and Kryscinski, Wojciech and McCann, Bryan and Xiong, Caiming and Socher, Richard and Radev, Dragomir},]A;journal={arXiv preprint arXiv:2007.12626},]A;year={2020}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SummEval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News Article Summary Semantic Similarity Estimation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新聞文章摘要語意相似性估計。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SummEval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SummEval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SummEval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SummEval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/summeval"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/summeval"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Tatoeba.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{Artetxe_2019,]A;title={Massively Multilingual Sentence Embeddings for Zero-Shot Cross-Lingual Transfer and Beyond},]A;volume={7},]A;ISSN={2307-387X},]A;url={http://dx.doi.org/10.1162/tacl_a_00288},]A;DOI={10.1162/tacl_a_00288},]A;journal={Transactions of the Association for Computational Linguistics},]A;publisher={MIT Press - Journals},]A;author={Artetxe, Mikel and Schwenk, Holger},]A;year={2019},]A;month=nov, pages={597–610} }]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{Artetxe_2019,]A;title={Massively Multilingual Sentence Embeddings for Zero-Shot Cross-Lingual Transfer and Beyond},]A;volume={7},]A;ISSN={2307-387X},]A;url={http://dx.doi.org/10.1162/tacl_a_00288},]A;DOI={10.1162/tacl_a_00288},]A;journal={Transactions of the Association for Computational Linguistics},]A;publisher={MIT Press - Journals},]A;author={Artetxe, Mikel and Schwenk, Holger},]A;year={2019},]A;month=nov, pages={597–610} }]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Tatoeba.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[1,000 English-aligned sentence pairs for each language based on the Tatoeba corpus]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以 Tatoeba 語料庫為基礎，每個語言有 1,000 組與英文對齊的成對句子]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Tatoeba.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tatoeba]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tatoeba]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Tatoeba.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/tatoeba-bitext-mining",]A;"github": "https://github.com/facebookresearch/LASER/tree/main/data/tatoeba/v1"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/tatoeba-bitext-mining",]A;"github": "https://github.com/facebookresearch/LASER/tree/main/data/tatoeba/v1"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ToxicConversationsClassification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{jigsaw-unintended-bias-in-toxicity-classification,]A;author = {cjadams, Daniel Borkan, inversion, Jeffrey Sorensen, Lucas Dixon, Lucy Vasserman, nithum},]A;title = {Jigsaw Unintended Bias in Toxicity Classification},]A;publisher = {Kaggle},]A;year = {2019},]A;url = {https://kaggle.com/competitions/jigsaw-unintended-bias-in-toxicity-classification}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{jigsaw-unintended-bias-in-toxicity-classification,]A;author = {cjadams, Daniel Borkan, inversion, Jeffrey Sorensen, Lucas Dixon, Lucy Vasserman, nithum},]A;title = {Jigsaw Unintended Bias in Toxicity Classification},]A;publisher = {Kaggle},]A;year = {2019},]A;url = {https://kaggle.com/competitions/jigsaw-unintended-bias-in-toxicity-classification}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ToxicConversationsClassification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Collection of comments from the Civil Comments platform together with annotations if the comment is toxic or not.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自「民事註解」平台的註解集合，以及註解是否為有害的註釋。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ToxicConversationsClassification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ToxicConversationsClassification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ToxicConversationsClassification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ToxicConversationsClassification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/toxic_conversations_50k",]A;"webpage": "https://www.kaggle.com/competitions/jigsaw-unintended-bias-in-toxicity-classification/overview"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/toxic_conversations_50k",]A;"webpage": "https://www.kaggle.com/competitions/jigsaw-unintended-bias-in-toxicity-classification/overview"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TriviaQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{2017arXivtriviaqa,]A;author = {{Joshi}, Mandar and {Choi}, Eunsol and {Weld}, Daniel and {Zettlemoyer}, Luke},]A;title = "{triviaqa: A Large Scale Distantly Supervised Challenge Dataset for Reading Comprehension}",]A;journal = {arXiv e-prints},]A;year = 2017,]A;eid = {arXiv:1705.03551},]A;pages = {arXiv:1705.03551},]A;archivePrefix = {arXiv},]A;eprint = {1705.03551},]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{2017arXivtriviaqa,]A;author = {{Joshi}, Mandar and {Choi}, Eunsol and {Weld}, Daniel and {Zettlemoyer}, Luke},]A;title = "{triviaqa: A Large Scale Distantly Supervised Challenge Dataset for Reading Comprehension}",]A;journal = {arXiv e-prints},]A;year = 2017,]A;eid = {arXiv:1705.03551},]A;pages = {arXiv:1705.03551},]A;archivePrefix = {arXiv},]A;eprint = {1705.03551},]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TriviaQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TriviaqQA is a reading comprehension dataset containing over 650K question-answer-evidence triples. TriviaqQA includes 95K question-answer pairs authored by trivia enthusiasts and independently gathered evidence documents, six per question on average, that provide high quality distant supervision for answering the questions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TriviaqQA 是一個閱讀理解資料集，包含超過 65 萬個問題-解答-證據三元組。TriviaqQA 包含 95,000 個由冷知識愛好者所撰寫的問答組和獨立收集的證據文件，每個問題平均六個，為回答問題提供高品質的遠程監督。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TriviaQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TriviaQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TriviaQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TriviaQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/mandarjoshi90/triviaqa",]A;"hugging_face": "https://huggingface.co/datasets/trivia_qa",]A;"webpage": "http://nlp.cs.washington.edu/triviaqa/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/mandarjoshi90/triviaqa",]A;"hugging_face": "https://huggingface.co/datasets/trivia_qa",]A;"webpage": "http://nlp.cs.washington.edu/triviaqa/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{lin2021truthfulqa,]A;title={TruthfulQA: Measuring How Models Mimic Human Falsehoods},]A;author={Stephanie Lin and Jacob Hilton and Owain Evans},]A;year={2021},]A;eprint={2109.07958},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{lin2021truthfulqa,]A;title={TruthfulQA: Measuring How Models Mimic Human Falsehoods},]A;author={Stephanie Lin and Jacob Hilton and Owain Evans},]A;year={2021},]A;eprint={2109.07958},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TruthfulQA is a benchmark to measure whether a language model is truthful in generating answers to questions. The benchmark comprises 817 questions that span 38 categories, including health, law, finance and politics. Questions are crafted so that some humans would answer falsely due to a false belief or misconception. To perform well, models must avoid generating false answers learned from imitating human texts.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TruthfulQA 是用來衡量語言模型在生成問題答案時是否真實的基準。基準包含 817 個問題，涵蓋 38 種類別，包括健康、法律、金融和政治。問題經過精心設計，因此部分人類會因為錯誤信念或錯誤觀念而答錯。要表現良好，模型必須避免生成模仿人類文字而學到的錯誤答案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TruthfulQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TruthfulQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/sylinrl/TruthfulQA",]A;"hugging_face": "https://huggingface.co/datasets/truthful_qa",]A;"arxiv": "https://arxiv.org/abs/2109.07958"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/sylinrl/TruthfulQA",]A;"hugging_face": "https://huggingface.co/datasets/truthful_qa",]A;"arxiv": "https://arxiv.org/abs/2109.07958"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQATextGen.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{lin2021truthfulqa,]A;title={TruthfulQA: Measuring How Models Mimic Human Falsehoods},]A;author={Stephanie Lin and Jacob Hilton and Owain Evans},]A;year={2021},]A;eprint={2109.07958},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{lin2021truthfulqa,]A;title={TruthfulQA: Measuring How Models Mimic Human Falsehoods},]A;author={Stephanie Lin and Jacob Hilton and Owain Evans},]A;year={2021},]A;eprint={2109.07958},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQATextGen.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TruthfulQA is a benchmark to measure whether a language model is truthful in generating answers to questions. The benchmark comprises 817 questions that span 38 categories, including health, law, finance and politics. Questions are crafted so that some humans would answer falsely due to a false belief or misconception. To perform well, models must avoid generating false answers learned from imitating human texts.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TruthfulQA 是用來衡量語言模型在生成問題答案時是否真實的基準。基準包含 817 個問題，涵蓋 38 種類別，包括健康、法律、金融和政治。問題經過精心設計，因此部分人類會因為錯誤信念或錯誤觀念而答錯。要表現良好，模型必須避免生成模仿人類文字而學到的錯誤答案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQATextGen.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TruthfulQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TruthfulQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQATextGen.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/sylinrl/TruthfulQA",]A;"hugging_face": "https://huggingface.co/datasets/truthful_qa",]A;"arxiv": "https://arxiv.org/abs/2109.07958"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/sylinrl/TruthfulQA",]A;"hugging_face": "https://huggingface.co/datasets/truthful_qa",]A;"arxiv": "https://arxiv.org/abs/2109.07958"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TweetSentimentExtractionClassification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{tweet-sentiment-extraction,]A;author = {Maggie, Phil Culliton, Wei Chen},]A;title = {Tweet Sentiment Extraction},]A;publisher = {Kaggle},]A;year = {2020},]A;url = {https://kaggle.com/competitions/tweet-sentiment-extraction}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{tweet-sentiment-extraction,]A;author = {Maggie, Phil Culliton, Wei Chen},]A;title = {Tweet Sentiment Extraction},]A;publisher = {Kaggle},]A;year = {2020},]A;url = {https://kaggle.com/competitions/tweet-sentiment-extraction}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TweetSentimentExtractionClassification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[With all of the tweets circulating every second it is hard to tell whether the sentiment behind a specific tweet will impact a company, or a person's, brand for being viral (positive), or devastate profit because it strikes a negative tone. Capturing sentiment in language is important in these times where decisions and reactions are created and updated in seconds.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由於所有推文每秒鐘都在傳播，很難判斷特定推文背後的情感是否會因為爆紅而傳播 (正面)，或因為負面語調而導致破壞利潤，進而影響一家公司或一個人的品牌。在決策和反應都在幾秒鐘內創建和更新的時代，用語言捕捉情感至關重要。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TweetSentimentExtractionClassification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TweetSentimentExtractionClassification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TweetSentimentExtractionClassification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TweetSentimentExtractionClassification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/tweet_sentiment_extraction",]A;"webpage": "https://www.kaggle.com/competitions/tweet-sentiment-extraction/overview"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/tweet_sentiment_extraction",]A;"webpage": "https://www.kaggle.com/competitions/tweet-sentiment-extraction/overview"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.WinoGrande.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@InProceedings{ai2:winogrande,]A;title = {WinoGrande: An Adversarial Winograd Schema Challenge at Scale},]A;authors={Keisuke, Sakaguchi and Ronan, Le Bras and Chandra, Bhagavatula and Yejin, Choi},]A;year={2019}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@InProceedings{ai2:winogrande,]A;title = {WinoGrande: An Adversarial Winograd Schema Challenge at Scale},]A;authors={Keisuke, Sakaguchi and Ronan, Le Bras and Chandra, Bhagavatula and Yejin, Choi},]A;year={2019}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.WinoGrande.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[WinoGrande is a large-scale dataset of 44k problems, inspired by the original Winograd Schema Challenge (Levesque, Davis, and Morgenstern 2011), but adjusted to improve the scale and robustness against the dataset-specific bias. Formulated as a fill-in-a-blank task with binary options, the goal is to choose the right option for a given sentence which requires commonsense reasoning. The key steps of the dataset construction consist of (1) a carefully designed crowdsourcing procedure, followed by (2) systematic bias reduction using a novel AfLite algorithm that generalizes human-detectable word associations to machine-detectable embedding associations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[WinoGrande 是一個包含 44,000 個問題的大型資料集，靈感來自原始的 Winograd Schema Challenge (Levesque、Davis 和 Morgenstern 2011) 但經過調整，除了提升規模，也改善對資料集特有偏差的穩固性。規劃為有二元選項的填空任務，目標是要對需要常識推理的指定句子選出正確的選項。資料集建構的關鍵步驟包含 (1) 精心設計的群眾外包流程，接著 (2) 使用新穎的 AfLite 演算法，將人類可察覺的文字關聯歸納為機器可偵測到的內嵌關聯，來減少系統性偏差。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.WinoGrande.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[WinoGrande]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[WinoGrande]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.WinoGrande.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/winogrande",]A;"arxiv": "https://arxiv.org/abs/1907.10641",]A;"webpage": "https://leaderboard.allenai.org/winogrande/submissions/get-started"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/winogrande",]A;"arxiv": "https://arxiv.org/abs/1907.10641",]A;"webpage": "https://leaderboard.allenai.org/winogrande/submissions/get-started"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.DatasetCitation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Citation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[引文]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.DatasetLinks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Links]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連結]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資訊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料集詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployNotSupported" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments are not enabled for this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型未啟用部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployNotSupportedWithoutRegistration" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registration is required to use this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要註冊才能使用此模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployOrFinetuneModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy or fine-tune model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署或微調模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy or finetune model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentBlockedByAdminPolicy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model cannot be deployed due to a policy in your subscription. Contact your admin to request access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您訂用帳戶中的原則不允許部署此模型。連絡您的系統管理員以要求存取權。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployments" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentsTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI model deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 模型部署]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service model deployments]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DirectFromAzureDisabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Direct from Azure models can only be deployed through AI Foundry.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[直接來自 Azure 模型只能透過 AI Foundry 部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.Close" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關閉]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.InstallDependencies" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[1. Install dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[1. 安裝相依性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.InstallIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install Azure Identity:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝 Azure 身分識別:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.InstallSDK" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the SDK using pip:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 pip 安裝 SDK:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.RunCode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[2. Run code to download the model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[2. 執行程式碼以下載模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneInvalidRegion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure model fine-tune is not available in your workspace's region. Supported regions include: {{region}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 模型微調在您工作區的地區無法使用。支援的區域包括: {{region}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型正確性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Average" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型流暢性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型據實性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.ModelNames" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model names]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model relevance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型相關性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Similarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型相似性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正確性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Bitext_mining" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bitext mining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[雙文字採礦]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Clustering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[叢集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Dataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流暢性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[據實性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Model" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.ModelVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Pair_classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pair classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配對分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Primary metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[主要計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相關性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Reranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新排名]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Result" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Result]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Retrieval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Similarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相似度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Sts" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[STS]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[STS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Task" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Common.Done" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Done]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.Accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正確性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.F1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1 score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.MeanAveragePrecision" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MAP]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MAP]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.NdcgAt10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NDCG at 10]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NDCG 於 10]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.Precision" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average precision]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均精確度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.SpearmanCorrelation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[斯皮爾曼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.VMeasure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 量值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.LeaderboardListViewBase.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.LeaderboardListViewBase.MetricScatterChartPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There seems to be nothing here. Adjust filter configuration to view metric distribution.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這裡似乎沒有任何內容。調整篩選設定以檢視計量分佈。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Accuracy.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We present accuracy scores at the dataset and the model levels. At the dataset level, the score is the average value of an accuracy metric computed over all examples in the dataset. The accuracy metric used is exact-match in all cases except for the HumanEval dataset which uses a pass@1 metric. Exact match simply compares model generated text with the correct answer according to the dataset, reporting one if the generated text matches the answer exactly and zero otherwise. Pass@1 measures the proportion of model solutions that pass a set of unit tests in a code generation task. At the model level, the accuracy score is the average of the dataset-level accuracies for each model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們會在資料集和模型層級提供正確度分數。在資料集層級，該分數是針對資料集中所有範例計算而得的正確度計量平均值。除了使用 pass@1 計量的 HumanEval 資料集以外，在所有案例中使用的正確度計量會完全相符。完全相符會直接根據資料集比較模型產生的文字與正確答案，如果產生的文字完全符合答案，則報告一，否則報告零。Pass@1 可測量在程式碼產生工作中通過一組單元測試的模型解決方案比例。在模型層級，正確度分數是每個模型的資料集層級正確度平均值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Accuracy.Question" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What is accuracy?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[什麼是正確性?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Accuracy.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy scores are presented at the dataset and the model levels. At the dataset level, the score is the average value of an accuracy metric computed over all examples in the dataset. {1} about accuracy.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正確性分數會在資料集與模型層級顯示。在資料集層級，此分數是針對資料集當中所有範例計算出來之正確性計量的平均值。{1}正確性的相關資訊。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Accuracy.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正確性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Back" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Back to select metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[返回以選取計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.BiTextMining.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inputs are two sets of sentences from two different languages. For each sentence in the first set, the best match in the second set needs to be found. The matches are commonly translations. The provided model is used to embed each sentence and the closest pairs are found via cosine similarity. F1 serves as the main metric for bitext mining. Accuracy, precision and recall are also computed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入是兩種不同語言的兩組句子。針對第一個集合中的每一個句子，必須找到第二個集合中的最佳相符專案。相符專案通常是翻譯。提供的模型可用來內嵌每個句子，而且透過餘弦相似性找到最接近的配對。F1 會作為 Bitext 採礦的主要計量。也會計算正確性、精確度和重新叫用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.BiTextMining.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1 Score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.BiTextMining.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The F1 score is the weighted mean of the precision and recall, where an F1 score reaches its best value at 1 (perfect precision and recall) and worst at 0.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分數是精確度和重新叫用的加權平均數，F1 分數的最佳值為 1 (完美精確度和重新叫用) 最差值則為 0。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.BiTextMining.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BiTextMining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BiTextMining]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Classification.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A train and test set are embedded with the provided model. The train set embeddings are used to train a logistic regression classifier with 100 maximum iterations, which is scored on the test set. The main metric is accuracy with average precision and F1 additionally provided.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訓練和測試集內嵌於提供的模型中。訓練集內嵌會用來訓練具有 100 個反覆運算上限的羅吉斯迴歸分類器，這會在測試集上評分。主要計量是正確性，並另外提供平均精確度與 F1。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Classification.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正確性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Classification.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy measures how often a machine learning model correctly predicts the outcome. It is calculated by dividing the number of correct predictions by the total number of predictions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正確性可測量機器學習模型正確預測結果的頻率。計算方式是將正確預測的數目除以預測總數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Classification.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Clustering.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Given a set of sentences or paragraphs, the goal is to group them into meaningful clusters. A mini-batch k-means model with batch size 32 and k equal to the number of different labels (Pedregosa et al., 2011) is trained on the embedded texts. The model is scored using v-measure (Rosenberg and Hirschberg, 2007). Vmeasure does not depend on the cluster label, thus the permutation of labels does not affect the score.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[給定一組句子或段落，目標是將它們分組成有意義的叢集。迷你批次 k 表示批次大小為 32 和 k 等於不同標籤數目 (Pedregosa 等，2011) 的模型會接受内嵌文字的訓練。模型是使用 v 量值來評分 (Rosenberg 和 Hirschberg, 2007)。Vmeasure 不會取決於叢集標籤，因此標籤的排列不會影響分數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Clustering.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 量值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Clustering.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure is a metric used to evaluate the quality of clustering. It is calculated as the harmonic mean of homogeneity (each cluster contains only members of a single class) and completeness (all members of a given class are assigned to the same cluster), ensuring a balance between the two for a meaningful score. Possible score lies between 0 and 1. Score of 1 stands for perfectly complete labeling.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 量值是用來評估叢集品質的計量。其計算方式為同質性 (每個叢集僅包含單一類別的成員) 和完整性 (給定類別的所有成員都獲派給同一個叢集) 的調和平均值，確保兩者之間的平衡以獲得有意義的分數。可能分數會介於 0 到 1 之間。1 分代表完美的完整標籤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Clustering.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[叢集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.CalculationAnswer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence metric is calculated by asking another GPT model to evaluate on the output of your model using the below prompt:]A;]A;System:]A;You are an AI assistant. You will be given the definition of an evaluation metric for assessing the quality of an answer in a question-answering task. Your job is to compute an accurate evaluation score using the provided evaluation metric.]A;]A;User:]A;Coherence of an answer is measured by how well all the sentences fit together and sound naturally as a whole. Consider the overall quality of the answer when evaluating coherence. Given the question and answer, score the coherence of answer between one to five stars using the following rating scale:]A;One star: the answer completely lacks coherence]A;Two stars: the answer mostly lacks coherence]A;Three stars: the answer is partially coherent]A;Four stars: the answer is mostly coherent]A;Five stars: the answer has perfect coherency]A;]A;This rating value should always be an integer between 1 and 5. So the rating produced should be 1 or 2 or 3 or 4 or 5.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性計量的計算方式是要求其他 GPT 模型使用下列提示評估模型的輸出內容:]A;]A;系統:]A;您是 AI 助理。您將獲得評估計量的定義，以評估問題解答工作中答案的品質。您的工作是使用提供的評估計量計算精確的評估分數。]A;]A;使用者:]A;答案的一致性是以所有句子的搭配以及整體聽起來自然的程度來衡量。評估一致性時，請考慮答案的整體品質。根據問題與答案，使用下列評分等級，為答案的一致性評分，範圍為一到五顆星:]A;一顆星: 答案完全不一致]A;兩顆星: 答案多數不一致]A;三顆星: 答案部分一致]A;四顆星: 答案多數一致]A;五顆星: 答案完全一致]A;]A;此評分值應一律為 1 到 5 之間的整數。因此產生的評分應為 1、2、3、4 或 5。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.CalculationQuestion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How is our coherence score calculated?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們的一致性分數如何計算?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence evaluates how well the language model can produce output that flows smoothly, reads naturally, and resembles human-like language.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性會評估語言模型產生之輸出流暢、讀起來自然以及與人類語言類似的程度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.Question" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What is coherence?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[什麼是一致性?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence evaluates how well the language model can produce output that flows smoothly, reads naturally, and resembles human-like language. {1} about coherence.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性評估的是語言模型產生流暢、讀起來自然且類似人類語言之輸出的程度。{1}一致性的相關資訊。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Ellipsis" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[…]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.CalculationAnswer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency metric is calculated by asking another GPT model to evaluate on the output of your model using the below prompt:]A;]A;System:]A;You are an AI assistant. You will be given the definition of an evaluation metric for assessing the quality of an answer in a question-answering task. Your job is to compute an accurate evaluation score using the provided evaluation metric.]A;]A;User:]A;Fluency measures the quality of individual sentences in the answer, and whether they are well-written and grammatically correct. Consider the quality of individual sentences when evaluating fluency. Given the question and answer, score the fluency of the answer between one to five stars using the following rating scale:]A;One star: the answer completely lacks fluency]A;Two stars: the answer mostly lacks fluency]A;Three stars: the answer is partially fluent]A;Four stars: the answer is mostly fluent]A;Five stars: the answer has perfect fluency]A;]A;This rating value should always be an integer between 1 and 5. So the rating produced should be 1 or 2 or 3 or 4 or 5.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流暢性計量的計算方式是要求其他 GPT 模型使用下列提示評估模型的輸出內容:]A;]A;系統:]A;您是 AI 助理。您將獲得評估計量的定義，以評估問題解答工作中答案的品質。您的工作是使用提供的評估計量計算精確的評估分數。]A;]A;使用者:]A;流暢性是衡量答案中個別句子的品質，以及它們是否撰寫得當且文法正確。評估流暢性時，請考慮個別句子的品質。根據問題與答案，使用下列評分等級，為答案的流暢性評分，範圍為一到五顆星:]A;一顆星: 答案完全不流暢]A;兩顆星: 答案多數不流暢]A;三顆星: 答案部分流暢]A;四顆星: 答案多數流暢]A;五顆星: 答案完全流暢]A;]A;此評分值應一律為 1 到 5 之間的整數。因此產生的評分應為 1、2、3、4 或 5。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.CalculationQuestion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How is our fluency score calculated?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如何計算我們的流暢性分數?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency evaluates the language proficiency of a generative AI's predicted answer. It assesses how well the generated text adheres to grammatical rules, syntactic structures, and appropriate usage of vocabulary, resulting in linguistically correct and natural-sounding responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流暢性會評估生成式 AI 預測答案的語言精熟程度。它會評定所產生的文字是否符合文法規則、語法結構，以及詞彙的適當用法，從而給予語言正確且聽起來自然的回應。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.Question" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What is fluency?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[什麼是流暢性?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency measures the language proficiency of a generative AI's predicted answer. {1} about fluency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流暢性衡量的是生成式 AI 預測答案的語言精熟程度。{1}流暢性的相關資訊。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流暢性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.CalculationAnswer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity metric is calculated by asking another GPT model to evaluate on the output of your model using the below prompt:]A;]A;System:]A;You are an AI assistant. You will be given the definition of an evaluation metric for assessing the quality of an answer in a question-answering task. Your job is to compute an accurate evaluation score using the provided evaluation metric.]A;]A;User:]A;Equivalence, as a metric, measures the similarity between the predicted answer and the correct answer. If the information and content in the predicted answer is similar or equivalent to the correct answer, then the value of the Equivalence metric should be high, else it should be low. Given the question, correct answer, and predicted answer, determine the value of Equivalence metric using the following rating scale:]A;One star: the predicted answer is not at all similar to the correct answer]A;Two stars: the predicted answer is mostly not similar to the correct answer]A;Three stars: the predicted answer is somewhat similar to the correct answer]A;Four stars: the predicted answer is mostly similar to the correct answer]A;Five stars: the predicted answer is completely similar to the correct answer]A;]A;This rating value should always be an integer between 1 and 5. So the rating produced should be 1 or 2 or 3 or 4 or 5.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相似性計量的計算方式是要求其他 GPT 模型使用下列提示評估模型的輸出內容:]A;]A;系統:]A;您是 AI 助理。您將取得評估計量的定義，用於在回答問題的工作中評估答案的品質。您的工作是使用提供的評估計量來計算正確的評估分數。]A;]A;使用者:]A;等價作為計量會測量預測回答與正確回答之間的相似性。如果預測回答中的資訊和內容與正確回答相似或相同，則等價計量的值應為高，反之則為低。根據問題、正確回答和預測的回答，使用以下評分等級來判斷等價計量的值:]A;一顆星: 預測回答與正確回答完全不相似]A;兩顆星：預測的答案通常與正確答案不相似]A;三顆星: 預測回答與正確回答有些相似]A;四顆星: 預測回答大致與正確回答相似]A;五顆星: 預測回答與正確回答完全相似]A;]A;這個評分值應一律為介於 1 和 5 之間的整數，因此產生的評分應為 1、2、3、4 或 5。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.CalculationQuestion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How is our similarity score calculated?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如何計算相似度分數?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity is a measure that quantifies the similarity between a ground truth sentence (or document) and the prediction sentence generated by an AI model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相似性是一種量值，用來量化基準真相句子 (或文件) 與 AI 模型所產生之預測句子之間的相似度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.Question" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What is similarity?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[什麼是相似性?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity measures the similarity between a source data (ground truth) sentence and the generated response by a GPT-based AI model. {1} about similarity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相似性衡量的是來源資料 (基準真相) 句子與 GPT 型 AI 模型所產生的回應之間的相似性。{1}相似性的相關資訊。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相似度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.MetricDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計量描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Overview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Overview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[概觀]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PairClassification.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A pair of text inputs is provided and a label needs to be assigned. Labels are typically binary variables denoting duplicate or paraphrase pairs. The two texts are embedded and their distance is computed with various metrics (cosine similarity, dot product, euclidean distance, manhattan distance). Using the best binary threshold accuracy, average precision, f1, precision and recall are computed. The average precision score based on cosine similarity is the main metric.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已提供一組文字輸入，且需要指派標籤。標籤通常是表示重複或釋義組的二進位變數。這兩個文字為內嵌，而它們的距離是使用各種計量計算 (餘弦相似度、點乘積、歐克拉底亞距離、曼哈頓距離)。使用最佳二進位閾值正確性，計算平均精確度、f1、精確度及重新叫用。以餘弦相似度為基礎的平均精確度分數是主要計量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PairClassification.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Precision]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[精確度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PairClassification.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Precision measures the model's ability to identify instances of a particular class correctly.Precision shows how often an ML model is correct when predicting the target class. Pros: Works well with imbalanced classes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[精確度會測量模型正確識別特定類別執行個體的能力。精確度會顯示預測目標類別時 ML 模型正確出現的頻率。優點: 適用於不平衡的類別。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PairClassification.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pair Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配對分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Primary Metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[主要計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Reranking.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inputs are a query and a list of relevant and irrelevant reference texts. The aim is to rank the results according to their relevance to the query. The model is used to embed the references which are then compared to the query using cosine similarity. The resulting ranking is scored for each query and averaged across all queries. Metrics are mean MRR@k and MAP with the latter being the main metric.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入是查詢和相關及不相關參考文字的清單。目標是根據結果與查詢的相關性來排名結果。模型是用來內嵌參考，然後使用餘弦相似度來比較參考。所得到的排名是針對每個查詢進行評分，並對所有查詢進行平均。計量是平均值 MRR@k 和 MAP，而後者是主要計量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Reranking.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MAP]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MAP]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Reranking.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mean Average Precision (MAP) evaluates the quality of ranking and recommender systems. It measures both the relevance of suggested items and how good the system is at placing more relevant items at the top. Values can range from 0 to 1. The higher the MAP, the better the system can place relevant items high in the list.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均值平均精確度 (MAP) 會評估排名和推薦程式系統的品質。它會測量建議項目的相關性，以及系統將更多相關項目置於頂部方面的表現如何。值的範圍可以從 0 到 1。MAP 越高，系統就越會將相關項目放在清單上端。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Reranking.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新排名]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Retrieval.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Each dataset consists of a corpus, queries and a mapping for each query to relevant documents from the corpus. The aim is to find these relevant documents. The provided model is used to embed all queries and all corpus documents and similarity scores are computed using cosine similarity. After ranking the corpus documents for each query based on the scores, nDCG@k, MRR@k, MAP@k, precision@k and recall@k are computed for several values of k. nDCG@10 serves as the main metric. MTEB reuses datasets and evaluation from BEIR (Thakur et al., 2021).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每個資料集包含主體、查詢，以及每個查詢到主體中相關文件的對應。目標是尋找這些相關文件。提供的模型會用來內嵌所有查詢，而所有主體文件及相似性分數都是使用餘弦相似性來計算。根據分數對每個查詢的主體文件進行排名之後，nDCG@k、MRR@k、MAP@k、precision@k 和 recall@k 會計算數個 k 值。nDCG@10 做為主要計量。MTEB 會重複使用 BEIR 的資料集和評估 (Thakur 等，2021)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Retrieval.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NDCG at K=10]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NDCG 於 K=10]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Retrieval.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Normalized Discounted Cumulative Gain (NDCG) evaluates the quality of information retrieval systems. NDCG helps measure a machine learning algorithm's ability to sort items based on relevance. It compares rankings to an ideal order where all relevant items are at the top of the list. K is a user-assigned parameter that defines the cutoff point (list length) while evaluating ranking quality. For example, if k=10, ndcg_at_10, will look at top-10 items.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標準化折扣累積收益 (NDCG) 會評估資訊擷取系統的品質。NDCG 可協助測量機器學習演算法根據相關性排序項目的能力。它會將排名與所有相關項目都位於清單頂端的理想順序進行比較。K 是個使用者指派的參數，會在評估排名品質時定義截止點 (清單長度)。例如，如果 k=10，ndcg_at_10，則會查看前 10 個項目。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Retrieval.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.STS.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Given a sentence pair the aim is to determine their similarity. Labels are continuous scores with higher numbers indicating more similar sentences. The provided model is used to embed the sentences and their similarity is computed using various distance metrics. Distances are benchmarked with ground truth similarities using Pearson and Spearman correlations. Spearman correlation based on cosine similarity serves as the main metric.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[給定一組句子時，目標是要判定它們的相似性。標籤是連續分數，數字越高表示句子越相似。提供的模型可用來內嵌句子，並使用各種距離計量來計算其相似性。距離會使用 Pearson 和 Spearman 相互關聯來基準化與基準真相之相似性。以餘弦相似度為基礎的 Spearman 相互關聯會作爲主要計量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.STS.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spearman 相互關聯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.STS.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation based on cosine similarity is the main metric for STS (Semantic Textual Similarity) and Summarization Embedding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以餘弦相似性為基礎的 Spearman 相互關聯是 STS (語意文字相似性) 與摘要內嵌工作的主要計量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.STS.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Semantic Textual Similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語意文字相似性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.SeeMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See more.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更多。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Summarization.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A set of human-written and machine-generated summaries are provided. The aim is to score the machine summaries. The provided model is first used to embed all summaries. For each machine summary embedding, distances to all human summary embeddings are computed. The closest score (e.g. highest cosine similarity) is kept and used as the model’s score of a single machine-generated summary. Pearson and Spearman correlations with ground truth human assessments of the machine-generated summaries are computed. Like for STS, Spearman correlation based on cosine similarity serves as the main metric. (Reimers et al., 2016).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供一組人工寫入和機器產生的摘要。目標是為機器摘要評分。提供的模型會先用於內嵌所有摘要。針對每個機器摘要內嵌，會計算所有人類摘要內嵌的距離。會保留最接近的分數 (例如，最高餘弦相似性) 並做為單一機器產生之摘要的模型分數。會計算 Pearson 和 Spearman 與機器產生之摘要的基準真相人類評定的相互關聯。與 STS 相似，以餘弦相似性為基礎的 Spearman 相互關聯會作爲主要計量。(Reimers 等，2016)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Summarization.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spearman 相互關聯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Summarization.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation based on cosine similarity is the main metric for STS (Semantic Textual Similarity) and Summarization Embedding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以餘弦相似性為基礎的 Spearman 相互關聯是 STS (語意文字相似性) 與摘要內嵌工作的主要計量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Summarization.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.TaskDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about available metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解可用的計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ModelDisplayName.ToggleAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Toggle to collapse and expand model name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[切換至折疊並展開模型名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.NoDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No Dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有數據集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Dataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.DatasetScore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料集分數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Footer.SecondaryButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Done]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Metric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Model" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.NShots" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[N-Shots]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[N-Shots]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.EvaluationData.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.FewShotData.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Few shot data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[少量樣本資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.Ratio" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ratio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[外觀比例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.Style" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Style]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[樣式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sampling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取樣]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.SubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about how these metrics were generated on a specific dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解如何在特定資料集上產生這些計量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Tasks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Score details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分數詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LlamaFinetuneInvalidVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model version is not available for fine-tune. Choose a newer version than {version}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型版本無法進行微調。選擇比 {version} 更新的版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.FineTune.Cta" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscribe and train]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂閱並訓練]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.FineTune.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customize the pre-trained model to understand your data to generate more accurate predictions with fine-tuned models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自訂預先訓練的模型，以了解您的資料，以使用微調模型產生更精確的預測。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Customize the pretrained model to understand your data to generate more accurate predictions with finetuned models.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.FineTune.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune with your data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用您的資料進行微調]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Finetune with your data]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.Cta" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscribe and deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂閱並部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provision an inference API within seconds through Models as a Service. Explore the model in the playground. Pay only for tokens consumed. Fine-tune the model using hosted fine-tuning, without the need to create and manage compute.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過模型即服務在數秒內佈建推斷 API。在遊樂場中探索模型。只支付已使用的權杖。使用託管微調模型，而不需要建立和管理計算。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Provision an inference API within seconds through Models as a Service. Explore the model in the playground. Pay only for tokens consumed. Fine-tune the model using hosted fine-tuning, without needing to create and manage compute.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.DescriptionNoFT" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provision an inference API within seconds through Models as a Service. Explore the model in the playground. Pay only for tokens consumed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過模型即服務在數秒內佈建推斷 API。在遊樂場中探索模型。只支付已使用的安全性權杖。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go inference APIs and Hosted Fine-tuning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隨用隨付推斷 API 和託管微調]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Paygo inference APIs and Hosted Fine-tuning]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.TitleNoFT" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless APIs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無伺服器 API]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Pay-as-you-go inference APIs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.Pricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[價格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.SignUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign-up for the wait list]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊等候清單]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.CTA.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quickly deploy fine-tune models with your own data, without managing any infrastructure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用您自己的資料快速部署微調模型，而不管理任何基礎結構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.CTA.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go plan]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隨用隨付方案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.DeployDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy the model using pay-as-you-go managed service]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用隨用隨付管理的服務部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.FinetuneAsPlatformDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune the model using compute from your workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用工作區的計算來微調模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.FinetuneAsPlatformTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微調]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.FinetuneDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune the model using pay-as-you-go managed service]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用隨用隨付受管理的服務微調模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.PayAsYouGo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隨用隨付]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSNotSupported" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless deployments are not enabled for this model in this Azure Cloud region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型在此 Azure Cloud 區域中未啟用無伺服器部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MetricDetails.MetricDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MetricDetails.MetricName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelID" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelIDTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reference this model ID when deploying the model in code]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在程式碼中部署模型時參考此模型識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.BrowseLeaderboards" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse leaderboards]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[瀏覽排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.Cost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[費用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.ModelLeaderboard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model leaderboards]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.ModelLeaderboardDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See what models are performing best in different criteria. {link} about our scoring methodology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看哪些模型在不同準則中表現最佳。{link} 我們的評分方法。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[See what models are performing best in different criteria.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.Quality" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.Safety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.Throughput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸送量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NotesHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More details from the provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自提供者的更多詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Benchmarks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基準]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.DownloadButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.ErrorResponseMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unknown error downloading blob.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載 Blob 時發生未知的錯誤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.ErrorResponseTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.LoadArtifactGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while loading artifacts]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[載入成品時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.ScheduleZipGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while sending zip schedule request]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[傳送 zip 排程要求時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.AttributesHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attributes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.DataDriftHeader_Preview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data drift detector (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料漂移偵測器 (預覽)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Datasets.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[datasets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Datasets.NoDatasetsForModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No datasets are associated with this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有任何資料集與這個模型相關聯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.ConfirmButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Proceed to workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[前往工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.DeploymentStartedSuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{deploymentName} deployment started successfully]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功啟動 {deploymentName} 部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.NoWorkspaces" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are no workspaces in this subscription you have access to or that are in the registry region list.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此訂用帳戶沒有您具有存取權或位在登錄區域清單中的工作區。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.Project.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Projects are easy-to-manage containers for your work—and the key to collaboration, organization, and connecting data and other services.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專案是您工作的易於管理容器，以及共同作業、組織以及連接資料和其他服務的關鍵。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.Project.SelectCreateProject" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select or create a project]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取或建立專案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.Project.SelectCreateProject1RP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a project to work with]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取要處理的專案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.SelectWorkspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.SpecifyWorkspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specify the workspace where you want to deploy this model. Your last used workspace is selected by default.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指定您要部署此模型的工作區。預設會選取您上次使用的工作區。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.SuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Success]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Endpoints.DeployModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Endpoints.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Endpoints.NoEndpointsMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model is not deployed to any endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型未部署至任何端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.ModelNotFoundError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model was not found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.NoSubscriptionError.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you recently created a subscription, it may take a few minutes to appear here. {link}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您最近建立了訂用帳戶，可能需要幾分鐘的時間才會顯示在此。{link}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.NoSubscriptionError.Link" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a new subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立新訂閱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.NoSubscriptionError.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No active subscriptions are found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到使用中的訂用帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.UpdatingModelGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while updating the model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新模型時發生錯誤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.ArchiveDialog" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Archiving this asset will hide it by default from list queries. You can still continue to reference and use an archived model in your workflows.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據預設，封存此資產會從清單查詢中隱藏該資產。您仍然可以在工作流程中繼續參考並使用封存的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployAOAIMenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy the model using an Azure OpenAI resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure OpenAI 資源部署模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy the model using an Azure OpenAI Service resource]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployAOAIMenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV1MenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy to a web service (only for models based on frameworks)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署至 Web 服務 (僅適用於以架構為基礎的模型)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV1MenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Web service]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Web 服務]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy to web service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2BatchMenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy the model using the batch endpoint wizard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用批次端點精靈部署模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy the model using the new batch endpoint wizard]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2BatchMenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批次端點]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy to batch endpoint]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2MenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy the model using the real-time endpoint wizard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用即時端點精靈部署模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy the model using the new real-time endpoint wizard]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2MenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Real-time endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即時端點]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy to real-time endpoint]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2QuickMenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy a model with preconfigured parameters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用預先設定的參數來部署模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy an MLflow model in one step with preconfigured parameters]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2QuickMenuItemDescriptionMLflow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy an MLflow model in one step with preconfigured parameters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用預先設定的參數在一個步驟中部署 MLflow 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2QuickMenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Real-time endpoint (quick)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即時端點 (快速)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.FilterLatestVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show latest versions only]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[僅顯示最新版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.ItemType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.ViewArchived" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Include archived]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包含已封存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.HourlyBased" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hourly-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以小時為基礎]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.MeterRateHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rate per GPU per hour]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每小時每個 GPU 的費率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.SurchargeInfo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Surcharge amount depends on compute configuration. Azure Compute charges also apply. The deployment runs on-demand and is billed hourly. To stop the billing, you must delete the deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[附加費金額取決於計算設定。也適用 Azure 計算費用。部署會隨選執行，並依每小時計費。若要停止計費，您必須刪除部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.SurchargeMeterHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Surcharge on the managed compute deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受控的計算部署附加費]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[價格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.BaseModelInference" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Base model inference]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本模型推斷]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.InputTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input (per 1k token)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入 (每 1k 權杖)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.MaaSPaygoHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless API pay-as-you-go]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無伺服器 API 隨用隨付]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.NotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[N/A]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[N/A]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.OutputTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output (per 1k token)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸出 (每 1k 權杖)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.TokenBased" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權杖型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Delete.ModelDeleteSuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model {modelName} deleted successfully]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功刪除模型 {modelName}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Delete.ModelDeleteWarningMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model {modelName} can not be deleted because it is currently being used in one or more services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[因為目前有一或多個服務正在使用模型 {modelName}，所以無法刪除該模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Delete.ModelDeletedSuccessMessageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Success]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Delete.ModelDeletedWarningMessageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Warning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[警告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Description.EditLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[編輯描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Description.EditPanelTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit model description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[編輯模型描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.AlertEmailAddresses" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Alert email addresses]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[警示電子郵件地址]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.ComputeTarget" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute target]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算目標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.DriftThreshold" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drift threshold]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[漂移臨界值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.Features" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.Frequency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Frequency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[頻率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.Interval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Interval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[間隔]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.ScoringEndpoints" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scoring endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計分端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.StartDate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.State" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[State]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Errors.DeletingModelGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while deleting the model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除模型時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.ColumnNames.CreatedOn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立時間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.ColumnNames.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.ColumnNames.PredictionType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prediction type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預測類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.ColumnNames.SensitiveFeatures" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sensitive features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[敏感性特徵]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model fairness assessments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型公平性評定]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.HelpText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To create a new fairness assessment see the {link}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要建立新的公平性評定，請參閱 {link}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.HelpTextLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[fairness documentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公平性文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.NoFairnessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No model fairness assessments associated with this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有模型公平性評估與這個模型相關聯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.HiddenLayerScanned.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HiddenLayer Model Scanner did not detect any vulnerabilities, embedded code or integrity issues with the model artifacts.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HiddenLayer 模型掃描器未偵測到模型成品的任何弱點、內嵌程式碼或完整性問題。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[HiddenLayer Model Scanner identifies cybersecurity risks in AI models. A SAFE result from the scanner indicates no vulnerabilities, embedded code, or integrity issues.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.HiddenLayerScanned.ReadMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read More]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[閱讀更多內容]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.HiddenLayerScanned.ReadMoreDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{learnMoreLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{learnMoreLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.HiddenLayerScanned.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Verified by HiddenLayer Model Scanner]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已經 HiddenLayer 模型掃描器驗證]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Scanned and attested SAFE by HiddenLayer]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.List.DeleteButtonName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.List.DeleteButtonNoPermission" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have the proper role based access to delete a model. Missing permission: {permission}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您沒有適當的角色型存取權限來删除模型。缺少權限: {permission}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.List.DeleteDialogItemType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.List.DeleteDialogText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting models is a permanent action that cannot be undone. Are you sure you wish to proceed?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刪除模型是永久性動作，而且無法復原。確定要繼續嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.AssetId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Asset ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資產識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.CreatedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.CreatedByJob" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by job]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由作業建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.CreatedByModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由模型建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.CreatedOn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立時間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.RunId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Job (Run ID)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作業 (執行識別碼)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.Type" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.Version" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Properties" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.PropertiesHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Property" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[property]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Stages.Stage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[暫存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Stages.StageTooltipContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AzureML supports three stages out of the box: Development, Production, Archived to allow users to indicate the readiness of an asset and manage asset's lifecycle. {learnMoreLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AzureML 支援三個現成階段: 開發、生產、已封存，可讓使用者指出資產的整備程度和管理資產的生命週期。{learnMoreLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Stages.StageTooltipContentLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Update.ModelUpdateErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There was an error while updating the model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新模型時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.CreatedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.FeatureSet" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Feature set]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.FeatureStore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Feature store]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.Tags" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.Version" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[feature sets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.FetchError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while fetching model feature set lineage information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取模型功能集譜系資訊時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.NoFeaturesMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No feature sets are associated with this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有任何功能集與這個模型相關聯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelsEntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.AddToRegistry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Share model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共用模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.AddToRegistryAOAIModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sharing models is not supported for Azure OpenAI models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 模型不支援共用模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sharing models is not supported for Azure OpenAI Service models]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.AddToRegistryPrivateWorkspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sharing models is not supported for workspaces that are configured with private endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用私人端點設定的工作區不支援共用模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.CreateSubscriptionText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a subscription to deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立要部署的訂閱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DeployButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DeployButtonTextZeroOnboarding" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用此模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DeployButtonTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go is available in the following regions: [{regions}]5D;. This workspace's region is [{region}]5D;.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隨用隨付可在下列地區使用: [{regions}]5D;。此工作區的區域為 [{region}]5D;。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DeployButtonTooltipAnonymous" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to view the pricing and deploy this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登入以檢視定價並部署此模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to deploy a model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DownloadButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download all]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[全部下載]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DownloadModelNotificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model {modelName} artifacts]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型 {modelName} 成品]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.ErrorLoadMarketplace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unable to load marketplace settings for pay-as-you-go]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法載入隨用隨付的市集設定]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.EvaluateButtonDisabledJobWriteTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To evaluate a model, you must have {rbacAction} permission]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要評估模型，您必須具有 {rbacAction} 權限]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.EvaluateButtonDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation is not supported for this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型不支援評估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.EvaluateButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FailedToFetchJobDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while fetching job details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取作業詳細資料時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FinetuneAoaiButtonDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tuning is not supported for this model in this region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此區域中不支援針對此模型進行微調]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FinetuneButtonDisabledJobWriteTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To fine-tune a model, you must have {rbacAction} permission]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要微調模型，您必須具有 {rbacAction} 權限]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[To finetune a model, you must have {rbacAction} permission]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FinetuneButtonDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tuning is not supported for this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型不支援微調]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Finetuning is not supported for this model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FinetuneButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微調]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Finetune]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.MAAPTemporaryDisabledError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine tuning with managed compute is coming soon]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即將推出受控計算的微調]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.RefreshButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.RegisterButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.RegisterButtonTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.RegisterButtonTitleAOAIModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registering models is not supported for Azure OpenAI models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 模型不支援註冊模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Registering models is not supported for Azure OpenAI Service models]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.UseInPromptFlowText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use in Prompt flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在提示流程中使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Subscription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訂用帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.CtaDescriptionV2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To try out {modelName}, you'll need an Azure account. Sign in now if you already have an account, or sign up to create a new one.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要試用 {modelName}，您需要 Azure 帳戶。如果您已有帳戶，請立即登入，或註冊以建立新帳戶。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.CtaTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Access more features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存取更多功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.CtaTitleV2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in for seamless model deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請登入以順暢地進行模型部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignIn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeploy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock the potential of Azure AI deployment capabilities. Gain access by signing in now to seamlessly deploy your models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[發揮 Azure AI 部署功能的潛力。立即登入以取得存取權，以順暢地部署您的模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to deploy this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployDl" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock the potential of Azure AI capabilities. Sign in to download or seamlessly deploy this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[發揮 Azure AI 功能的潛力。登入以下載或順暢地部署此模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to download or deploy this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployDlFt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock the potential of Azure AI capabilities. Sign in to download, fine-tune, or seamlessly deploy this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[發揮 Azure AI 功能的潛力。登入以下載、微調或順暢地部署此模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to download, deploy, or fine-tune this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployDlFtTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to do more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登入以執行更多動作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployDlTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to download or deploy this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登入以下載或部署此模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployFt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock the potential of Azure AI capabilities. Sign in now to fine-tune or seamlessly deploy your models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[發揮 Azure AI 功能的潛力。立即登入以微調或順暢地部署您的模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to deploy or fine-tune this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployFtTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to fine-tune and deploy models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登入以微調及部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in for seamless model deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登入以進行無縫模型部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign up for an Azure account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊 Azure 帳戶]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Workspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SignInToDeploy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to deploy this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登入以部署此模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.DataMediaLanguages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data, media and languages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料、媒體和語言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Evaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More details from model provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自模型提供者的更多資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Inputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.ModelVersions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Versions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Outputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Pricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[價格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.SupportedDataTypes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Supported data types]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援的資料類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.SupportedLanguages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Supported languages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援的語言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Transparency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Transparency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透明度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Undisclosed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.FinetuningTask" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tuning task: {task}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微調工作: {task}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Finetuning task: {task}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.Languages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Languages: {language}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語言: {language}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.License" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[License: {license}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[授權: {license}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.MinInferenceSku" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Minimum inferencing sku: {sku}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最小推斷 SKU: {sku}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.Task" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task: {task}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作: {task}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Transparency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Transparency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透明度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewLicense" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View license]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視授權]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";here" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這裡]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
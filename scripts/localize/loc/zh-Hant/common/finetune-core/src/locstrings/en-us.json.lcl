﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\3\s\common\finetune-core\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-TW" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";FinetuneWizardHyperparameterSettings.ChoiceGroupOptions.Value.Custom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Custom]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自訂]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.ChoiceGroupOptions.Value.Default" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Default]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預設]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.ChoiceGroupOptions.Value.Random" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Random]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隨機]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.BatchSize.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批次大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.BatchSize.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch size (1-32)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批次大小 (1-32)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.BatchSize.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The batch size to use for training. When set to default, batch_size is calculated as 0.2% of examples in training set and the max is 32.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用於定型的批次大小。設定為預設時，batch_size 會以定型集當中的 0.2% 範例計算，而最大值為 32。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The batch size to use for training. When set to default, batch_size is calculated as 0.2% of examples in training set and the max is 256.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Beta.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Beta]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搶鮮版 (Beta)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Beta.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Beta (0.1-2.0)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Beta (0.1-2.0)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Beta.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The beta parameter for Direct Preference Optimization(DPO) method. A higher beta value will increase the weight of the KL penalty between the policy and reference model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[直接喜好設定最佳化 (DPO) 方法的 beta 參數。較高的 beta 值將增加原則與參考模型之間的 KL 懲罰權重。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationBetas.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification betas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分類 Beta]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationBetas.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If this is provided, we calculate F-beta scores at the specified beta values. The F-beta score is a generalization of F-1 score. This is only used for binary classification. With a beta of 1 (i.e. the F-1 score), precision and recall are given the same weight. A larger beta score puts more weight on recall and less on precision. A smaller beta score puts more weight on precision and less on recall. The value specified should be a comma separated list of doubles.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果提供此項目，我們會以指定的 Beta 值計算 F-Beta 分數。F-Beta 分數是 F-1 分數的一般化。這僅用於二元分類。Beta 為 1 (例如 F-1 分數)，精確度和重新叫用會獲得相同的權數。Beta 分數越大，重新叫用的權數就越高，精確度的權數越低。Beta 分數越小，精確度的權數就越高，重新叫用的權數越低。指定的值應為雙精確度浮點數的逗號分隔清單。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationNClasses.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification n classes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分類 n 個類別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationNClasses.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of classes in a classification task. This parameter is required for multiclass classification.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分類工作中的類別數目。多類別分類需要此參數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationPositiveClass.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification positive class]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分類正類別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ClassificationPositiveClass.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The positive class in binary classification. This parameter is needed to generate precision, recall, and F1 metrics when doing binary classification.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[二元分類中的正類別。執行二元分類時，需要此參數才能產生精確度、重新叫用和 F1 計量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeClassificationMetrics.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute classification metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算分類計量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeClassificationMetrics.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If set, we calculate classification-specific metrics such as accuracy and F-1 score using the validation set at the end of every epoch. In order to compute classification metrics, you must provide a validation_file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果設定，我們會使用每 epoch 結尾處的驗證集來計算分類特定計量，例如精確度和 F-1 分數。若要計算分類計量，您必須提供 validation_file。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeMultiplier.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 0.5-3.0 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在大部分情況下，建議使用 0.5-3.0 的範圍。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeMultiplier.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute multiplier]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算乘數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeMultiplier.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute multiplier (0.5-3.0)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算乘數 (0.5-3.0)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ComputeMultiplier.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multiplier on amount of compute use for exploring search space during training.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訓練期間探索搜尋空間的計算使用量乘數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalInterval.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 1-25 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在大部分情況下，建議使用 1-25 的範圍。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalInterval.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation interval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估間隔]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Eval interval]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalInterval.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation interval (1-25)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估間隔 (1-25)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalInterval.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of training steps between evaluations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估之間的訓練步驟數目。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalSamples.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 1-10 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在大部分情況下，建議使用 1-10 的範圍。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalSamples.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Samples for evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估範例]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Eval samples]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalSamples.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Samples for evaluation (1-10)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估範例 (1-10)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.EvalSamples.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of samples to use during evaluation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估期間使用的樣本數目。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.CreateText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.ErrorTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error while generating]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.FreeBeta" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Free beta]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[免費 Beta 版]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.GenerateText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.GraderPlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Describe how you want the response to be graded, and we'll generate a grader.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述您希望評分回應的方式，我們將產生一個評分器。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.GenerateGraderAndResponseFormat.ResponsePlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Describe how you want the model to respond, and we'll generate a JSON schema.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述您希望模型回應的方式，我們將產生一個 JSON 架構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Grader.EmptyError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grader schema is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要評分工具結構描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Grader.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grader schema]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評分工具結構描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Grader.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grader schema]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評分工具結構描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Grader.ValidatingSchema" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating schema...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在驗證結構描述...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.LearningRateMultiplier.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 0.0001-10.0 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在大部分情況下，建議使用 0.0001-10.0 的範圍。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.LearningRateMultiplier.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learning rate multiplier]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[學習速率乘數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.LearningRateMultiplier.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learning rate multiplier (0.0-10.0)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[學習速率乘數 (0.0-10.0)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.LearningRateMultiplier.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The fine-tuning learning rate is the original learning rate used for pre-training multiplied by this multiplier. We recommend experimenting with values between 0.5 and 2. Empirically, we've found that larger learning rates often perform better with larger batch sizes. Must be between 0.0 and 10.0.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微調學習率是用於預先定型的原始學習率乘以此乘數。建議您以介於 0.5 和 2 之間的值進行實驗。經驗上，我們發現較大的學習率往往在較大的批次大小下執行得更好。必須介於 0.0 和 10.0 之間。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The fine-tuning learning rate is the original learning rate used for pre-training multiplied by this multiplier. We recommend experimenting with values between 0.5 and 2. Empirically, we've found that larger learning rates often perform better with larger batch sizes. Must be between 0.0 and 5.0.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.NumberOfEpochs.RecommendationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In most cases range of 1-10 is recommended.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在大部分情況下，建議使用 1-10 的範圍。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.NumberOfEpochs.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of epochs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Epoch 數目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.NumberOfEpochs.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of epochs (1-10)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Epoch 數目 (1-10)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Number of epochs (1-5)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.NumberOfEpochs.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of training epochs. An epoch refers to one full cycle through the data set. If set to default, number of epochs will be determined dynamically based on the input data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[定型 Epoch 的數目。一個 Epoch 是指資料集的一個完整循環。如果設為預設，將會根據輸入資料動態決定 Epoch 數目。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.PromptLossWeight.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt loss weight]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示遺失權重]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.PromptLossWeight.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The prompt loss weight to use for training]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用於定型的提示遺失權重]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ReasoningEffort.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning effort]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推理能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ReasoningEffort.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning effort]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推理能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ResponseFormat.EmptyError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Response format schema is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要回應格式結構描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ResponseFormat.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Response format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[回應格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.ResponseFormat.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The response format can be added as null if there is no valid schema.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果沒有有效的結構描述，回應格式可以新增為 null。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Seed.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Seed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[種子]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Seed.TitleWithMinMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Seed (0-2147483647)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[種子 (0-2147483647)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardHyperparameterSettings.OpenAIFinetune.Seed.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The seed controls the reproducibility of the job. Passing in the same seed and job parameters should produce the same results, but may differ in rare cases. If a seed is not specified, one will be generated for you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[種子控制工作的重現性。傳入相同的種子和作業參數應該會產生相同的結果，但在少數情況下可能會有所不同。如果未指定種子，則會為您產生一個種子。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.AoaiFilesError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to retrieve files:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法擷取檔案:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.arcScienceMcqSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grade-school multiple-choice science questions requiring advanced reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要進階推理的小學多選科學問題]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.arcScienceMcqSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[arc_science_mcq_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[arc_science_mcq_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.arcScienceMcqSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scientific reasoning and QA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[科學推理和 QA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.chatTrainSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Grade school math problems requiring multi-step arithmetic reasoning and calculation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要多步驟算術推理和計算的小學數學問題]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.chatTrainSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[textGSM8K_train_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[textGSM8K_train_sample]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[chat_train_sample]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.chatTrainSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mathematical reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[數學推理]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Mathematical reasoning safety training]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.cuadLegalContractsSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Labeled commercial contracts with 41 important legal clause types]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有 41 個重要法律條款類型的已標記商業合約]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.cuadLegalContractsSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[cuad_legal_contracts_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[cuad_legal_contracts_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.cuadLegalContractsSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Legal agent for contract analysis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[合約分析法律代理程式]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Legal contract clause identification]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.helpText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get started with a pre-prepared sample dataset to help you understand how to format data for fine-tuning and test end-to-end workloads. These datasets are intended as examples; they are not intended to produce production quality models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始使用預先準備的範例資料集，協助您了解如何格式化資料以進行微調及測試端對端工作負載。這些資料集僅作為範例，並不適用於產生生產品質的模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Each dataset contains professionally curated examples designed for optimal fine-tuning results, supporting a wide range of model capabilities and advanced safety alignment.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.medMcqaHealthcareSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multiple-choice medical exam questions covering diverse healthcare topics and subjects]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[涵蓋各種醫療保健主題的多選醫療測驗問題]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.medMcqaHealthcareSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[medMCQA_healthcare_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[medMCQA_healthcare_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.medMcqaHealthcareSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assistive medical agent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輔助醫療代理程式]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Medical education and assessment]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.openOrcaDPOSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Imitation learning data with step-by-step explanations from GPT-4 and GPT-3.5]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自 GPT-4 和 GPT-3.5 的模仿學習資料，包含逐步說明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.openOrcaDPOSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[open_orca_dpo_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[open_orca_dpo_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.openOrcaDPOSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Imitative reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模仿推理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.stockToolCallingSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Current and previous stock market queries data, covering price retrieval using tool calls]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目前和過去的股票市場查詢資料，涵蓋使用工具呼叫擷取的價格資訊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.stockToolCallingSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[stock_tool_calling_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[stock_tool_calling_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.stockToolCallingSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Agent for stock market monitoring]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[股票市場監控代理程式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Curated Sample Datasets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[策劃的範例資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.vizWizVisualQnaSampleDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visual questions from blind users with images and spoken queries]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用影像和口語查詢的視障使用者視覺問題]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.vizWizVisualQnaSampleDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[vizWiz_visual_qna_sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[vizWiz_visual_qna_sample]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CuratedDatasets.vizWizVisualQnaSampleUseCase" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assistive vision agent for the visually impaired]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視障人士的輔助視覺代理程式]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Assistive technology for blind users]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.CustomFileListMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The following file types are supported: {acceptedFileExtensions}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援下列檔案類型: {acceptedFileExtensions}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.ReviewDataDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Preview of top 3 rows from your dataset (Total: {totalRowsContent} rows)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料集前 3 列預覽 (總計: {totalRowsContent} 列)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.ReviewDataInfo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the preview of the data you have selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是您選取資料的預覽]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.TestDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Test data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[測試資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.TrainingDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Training data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訓練資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Dataset.ValidationDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[驗證資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.DatasetRequirements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What are the data requirements?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料需求為何?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose a file to upload from your local drive. File must be under 200 MB.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選擇要從本機磁碟上傳的檔案。檔案必須小於 200 MB。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Error" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.IncorrectColumnMappingMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your data must include columns with the following labels: {columnLabels}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的資料必須包含具有下列標籤的資料行: {columnLabels}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.IncorrectFileTypeError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File is not of a supported type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案不是支援的類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.IncorrectFileTypeEvaluating" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File is not of a supported type. Evaluation jobs support JSON Lines files only.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案不是支援的類型。評估作業僅支援 JSON Lines 檔案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.IncorrectFileTypeFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File is not of a supported type. Fine-tuning jobs support csv, tsv, and JSON Lines formats.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案不是支援的類型。微調作業支援 csv、tsv 和 JSON Lines 格式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Errors.TrainingDataMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Training data is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要訓練資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileImportError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File import error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案匯入錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileImportSuccessful" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File import successful]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案成功匯入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileList" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The supported file types are csv, tsv, and JSON Lines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援的檔案類型為 csv、tsv 和 JSON 行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileLocation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Files will be uploaded to the default datastore and made available in your workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案將上傳到預設資料存放區，並可在工作區中使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.FileUploadError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File upload error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案上傳錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.AOAILearnMoreMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about data preparation for fine-tuning OpenAI models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解微調 OpenAI 模型的資料準備。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.CnnDailyMail" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CNN daily mail]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CNN 每日郵件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Conll2003Dataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CoNLL2003 dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CoNLL2003 資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.CustomFileListMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The following file types are supported: {acceptedFileExtensions}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援下列檔案類型: {acceptedFileExtensions}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.DatasetRequirements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What are the data requirements?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料需求為何?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Emotion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Emotion]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[表情]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationFillMaskDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The fill mask data is expected to have 2 fields – input_string (which contains the article with the masked(eg. [MASK]5D;, <mask>) tokens), title (which contains the actual word which fills the mask) like shown below. The below samples are borrowed from the {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[填滿遮罩資料預期有 2 個欄位 - input_string (其包含具有遮罩的文章 (例如 [MASK]5D;、<mask>) 權杖)、標題 (其包含填滿遮罩的實際文字)，如下所示。下列範例取自 {link} 資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationQuestionAnsweringDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Extractive Question Answering data should contain 3 fields – question, context and answers field. The below table contains couple of samples from {link} dataset. The data in answers field should contain a string answer text like in the example below.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取式問題解答資料應包含 3 個欄位 - 問題、內容及解答欄位。下表包含來自 {link} 資料集的數個範例。解答欄位中的資料應包含字串解答文字，如下列範例所示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationSummarizationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The summarization data is expected to have 2 fields – input_string (which contains the article), summary like shown below. The below samples are borrowed from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要資料預期有 2 個欄位 - input_string (其包含文章)、摘要，如下所示。下列範例取自 {link} 資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationTextClassificationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[When evaluating your model for text classification, you could perform either single text classification or text pair classification. Text classification requires the evaluation data to include at least 2 fields – one for ‘input_string’ (which contains the input text) and ‘label’ like in this example. The below examples are from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評估文字分類的模型時，您可以執行單一文字分類或文字組分類。文字分類需要評估資料至少包含 2 個欄位 –一個適用於 'input_string' (包含輸入文字) 和 'label'，如本範例所示。下列範例來自 {link} 資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationTextGenerationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The text generation data is expected to have 2 fields – input_string (which contains the context for generating further text), ground_truth (which contains the entire text article). The below samples are borrowed from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生資料預期有 2 個欄位 - input_string (其包含產生進一步文字的內容)，ground_truth (其包含整個文字文章)。下列範例取自 {link} 資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationTokenClassificationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification requires the evaluation data to include 2 fields, ‘input_string’ (which contains the input tokens) and ‘ner_tags_str’ (which contains the array of tags in string format) like in this example. The below examples are taken from {link}. Please note that the NER tags should be passed as space separated string. The Tags should be passed as a string literal of an array of string tags. Tag list for below - {'O': 0, 'B-PER': 1, 'I-PER': 2, 'B-ORG': 3, 'I-ORG': 4, 'B-LOC': 5, 'I-LOC': 6, 'B-MISC': 7, 'I-MISC': 8}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語彙基元分類需要評估資料包含 2 個欄位、'input_string' (包含輸入語彙基元) 和 'ner_tags_str' (包含字串格式的標記陣列)，如本範例所示。下列範例取自 {link}。請注意，NER 標記應該以空格分隔字串形式傳遞。標記應該傳遞為字串標記陣列的字串常值。下列的標記清單 - {'O': 0, 'B-PER': 1, 'I-PER': 2, 'B-ORG': 3, 'I-ORG': 4, 'B-LOC': 5, 'I-LOC': 6, 'B-MISC': 7, 'I-MISC': 8}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.EvaluationTranslationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The translation data should have 2 fields – input_string (which contains the source language) and target language (ex. “ro”) like in the example below. The field names that map to source and target languages need to be language codes supported by the model. Please refer to the model card for details on supported languages.  The below examples are sampled from {link}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[翻譯資料應該有 2 個欄位: input_string (其包含來源語言) 和目標語言 (例如“ro”)，如以下範例所示。要與來源和目標語言進行比對的欄位名稱，必須是受模型支援的語言代碼。如需支援語言的詳細資料，請參閱模型卡片。下列範例取樣自 {link}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.FileList" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The supported file types are csv, tsv, and JSON Lines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援的檔案類型為 csv、tsv 和 JSON 行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.FileLocation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Files will be uploaded to the default datastore and made available in your workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案將上傳到預設資料存放區，並可在工作區中使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.FillMaskTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fill Mask example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[填滿遮罩範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Gluemnli" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Glue mnli]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Glue mnli]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.MicrosoftResearchParaphraseCorpus" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Research Paraphrase Corpus]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Research Paraphrase Corpus]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.QuestionAnsweringDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Extractive Question Answering data should contain 3 fields – question, context and answers field. The below table contains couple of samples from {link} dataset. The data in answers field should contain 2 more sub-fields – answer start and text like in the example below. The text contains the actual answer to the question from within the context and the answer_start is the number of characters before the start of the answer. The text and answer_start is an array with all possible answers and the start character for the corresponding answer text. For example, in the first example below, the answer helps many proteins bind the polypeptide starts at 236th character from the start of the context.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取式問題解答資料應包含 3 個欄位 - 問題、內容及解答欄位。下表包含來自 {link} 資料集的數個範例。解答欄位中的資料應包含另外 2 個子欄位 - 解答開始和文字，如下列範例所示。文字包含內容中問題的實際解答，而 answer_start 是解答開始之前的字元數。文字和 answer_start 是一個陣列，具有所有可能的解答，以及對應解答文字的開始字元。例如，在下面的第一個範例中，解答可協助許多蛋白質連結多肽，從內容開始的第 236 個字元開始。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.QuestionAnsweringTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question Answering example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[問題解答範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.RcdsWikipedia" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[rcds/wikipedia-for-mask-filling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[rcds/wikipedia-for-mask-filling]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.RestrictedFileList" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The supported file type is JSON Lines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援的檔案類型為 JSON 行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.SAMSum" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SAMSum ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SAMSum]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.SingleTextClassificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Single text classification example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[單一文字分類範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Squad" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stanford Question Answering Dataset (SQuAD)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stanford 問題解答資料集 (SQuAD)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.SummarizationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The summarization data is expected to have 2 fields – document, summary like shown below. The below samples are borrowed from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要資料預期有 2 個欄位 - 文件、摘要，如下所示。下列範例取自 {link} 資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.SummarizationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextClassificationDescription1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Single text classification requires the training data to include at least 2 fields – one for ‘Sentence1’ and ‘Label’ like in this example. Sentence 2 can be left blank in this case. The below examples are from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[單一文字分類要求定型資料至少包含 2 個欄位 - 一個用於 'Sentence1' 和 'Label'，如此範例中所示。此案例中的 Sentence 2 可以保留空白。下列範例來自 {link} 資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextClassificationDescription2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text pair classification, where you have two sentences to be classified (e.g., sentence entailment) will need the training data to have 3 fields – for ‘Sentence1’, ‘Sentence2’ and ‘Label’ like in this example. The below examples are from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字組分類，其中有兩個要分類的句子 (例如句子蘊涵)，要求定型資料有 3 個欄位 - 如此範例中的 'Sentence1'、'Sentence2' 和 'Label'。下列範例來自 {link} 資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextClassificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text classification example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字分類範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationFinetuneDescription1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The text generation data is expected to have 2 fields – text, ground_truth like shown below. The below samples are borrowed from {link} dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生資料預期有 2 個欄位 - text、ground_truth，如下所示。下列範例取自 {link} 資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationFinetuneDescription2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The below samples are are formatted data the user might pass.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下列範例是使用者可能傳遞的格式化資料。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationFinetuneTitle1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation example - original data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生範例 - 原始資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationFinetuneTitle2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation example - formatted  data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生範例 - 格式化資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextGenerationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TextPairClassificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text pair classification example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字組分類範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資訊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TokenClassificationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification requires the training data to include 2 fields, ‘Tokens’ and ‘Tags’ like in this example. The tags could contain any strings depending on the fine-tune use case. The below examples are taken from {link}. Please note that the NER tags should be passed as an array of strings.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權杖分類要求訓練資料包含 2 個欄位，如此範例中的 'Tokens' 和 'Tags'。根據微調使用案例，標記可能包含任何字串。下列範例取自 {link}。請注意，NER 標記應該以字串陣列形式傳遞。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TokenClassificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權杖分類範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TranslationDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The translation data should have 2 fields – source language and target language like in the example below. The field names that map to source and target languages need to be language codes supported by the model. Please refer to the model card for details on supported languages.  The below examples are sampled from {link}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[翻譯資料應該有 2 個欄位 - 來源語言和目標語言，如下列範例所示。要與來源和目標語言進行比對的欄位名稱，必須是受模型支援的語言代碼。如需支援語言的詳細資料，請參閱模型卡片。下列範例取樣自 {link}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.TranslationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Translation example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[翻譯範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.HelpPanel.Wmt16Dataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[WMT16 dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[WMT16 資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.Import" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Import]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[匯入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.IncorrectColumnMappingError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Incorrect data formatting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不正確的資料格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.PrepopulatedDataset.Multimodal" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multimodal]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.PrepopulatedDataset.Sample" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.PrepopulatedDataset.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ready-to-go dataset for quick fine-tuning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於快速微調的現成資料集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.PrepopulatedDataset.examples" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[examples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.ProcessingFile" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Processing file...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在處理檔案...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.RestrictedDataTypesMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The following file types are supported: {acceptedFileExtensions}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援下列檔案類型: {acceptedFileExtensions}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.RestrictedFileList" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The supported file type is JSON Lines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援的檔案類型為 JSON 行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SelectRegisteredDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.ChatCompletionFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need a jsonl file in chat format: For chat task type, each row in the data should be a list of JSON objects. Each row corresponds to a conversation and each object in the row is a turn/utterance in the conversation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您需要聊天格式的 jsonl 檔案：若為聊天工作類型，資料中的每一資料列都應該是 JSON 物件清單。每個資料列對應到交談，且資料列中的每個物件都是交談中的回合/表達。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.FillMask" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fill mask data needs to contain at least 2 columns: one for ‘Sentence1’ (string) and another for ‘Label’ (integer / string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[填滿遮罩資料至少必須包含 2 個資料行: 一個用於 'Sentence1' (字串)，另一個用於 'Label' (整數/字串)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.OpenAIFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need a jsonl file containing one prompt and the corresponding completion per line.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您需要 JSON 檔案，其中包含一個提示及每行對應的完成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.OpenAIFinetuningPipelineChat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need a jsonl file in chat format: For chat task type, each row in the data should be a list of JSON objects. Each row corresponds to a conversation and each object in the row is a turn/utterance in the conversation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您需要聊天格式的 jsonl 檔案：若為聊天工作類型，資料中的每一資料列都應該是 JSON 物件清單。每個資料列對應到交談，且資料列中的每個物件都是交談中的回合/表達。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.QuestionAnsweringEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering data needs to contain at least 3 columns: for ‘Question’ (string), ‘Context’ (string), and ‘Answers’ (string). Additionally, it can optionally include ‘Answers_start’ (int) and ‘Answers_text’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[問題解答資料至少必須包含 3 個資料行: 適用於 'Question' (字串)、'Context' (字串) 和 'Answers' (字串)。此外，它也可以選擇性包含 'Answers_start' (int) 和 'Answers_text' (字串)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.QuestionAnsweringFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering data needs to contain at least 5 columns: for ‘Question’ (string), ‘Context’ (string), ‘Answers’ (string), ‘Answers_start’ (int) and ‘Answers_text’ (string). Additionally, it can optionally include ‘doc_stride’ (int), ‘n_best_size’ (int) and ‘max_answer_length_in_tokens’ (int).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[問題解答資料至少必須包含 5 個資料行: 適用於 'Question' (字串)、'Context' (字串)、'Answers' (字串)、'Answers_start' (int) 和 'Answers_text' (字串)。此外，它也可以選擇性包含 'doc_stride' (int)、'n_best_size' (int) 和 'max_answer_length_in_tokens' (int)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.Summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization data needs to contain at least 2 columns: one for ‘Document’ (string) and another for ‘Summary’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要資料必須包含至少 2 個資料行: 一個用於 'Document' (字串)，另一個用於 'Summary' (字串)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TextClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text classification data needs to contain at least 2 columns: one for ‘Sentence1’ (string) and another for ‘Label’ (integer / string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字分類資料至少必須包含 2 個資料行: 一個用於 'Sentence1' (字串)，另一個用於 'Label' (整數/字串)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TextGenerationEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation data needs to contain at least 2 columns: one for ‘Sentence1’ (string) and another for ‘Label’ (integer / string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生資料至少必須包含 2 個資料行: 一個用於 'Sentence1' (字串)，另一個用於 'Label' (整數/字串)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TextGenerationFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation data needs to contain 2 columns: one for ‘text’ (string) and another for 'ground_truth' (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生資料必須包含 2 個資料行: 一個用於 ‘Text’(字串)，另一個用於 ‘Ground_truth’(字串)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TokenClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification data needs to contain 2 columns: one for ‘Token’ (string) and another for ‘Tag’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權杖分類資料必須包含 2 個資料行: 一個用於 'Token' (字串)，另一個用於 'Tag' (字串)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TranslationEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Translation data needs to contain a column for ‘Source_language’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[翻譯資料必須包含 'Source_language' (字串) 的欄位。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneWizardTrainingandValidationDetailDialog.SubText.TranslationFinetuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Translation data needs to contain 2 columns: one for ‘Source_language’ (string) and another for ‘Target_language’ (string).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[翻譯資料必須包含 2 個資料行: 一個用於 'Source_language' (字串)，另一個用於 'Target_language' (字串)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.DisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Display name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.DisplayNameErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Display name can not exceed 128 characters and can only contain letters, numbers, dashes and underscores.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示名稱不得超過 128 個字元，且只能包含字母、數字、虛線和底線。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.DisplayNamePlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter a display name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請輸入顯示名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.Entity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Entity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[實體]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.EntityPlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter an entity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入實體]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.ProjectName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Weights & Biases project name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Weights & Biases 專案名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.ProjectNamePlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter a project name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入專案名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.ProjectNameRegexErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project name can not exceed 128 characters and can only contain letters, numbers, dashes and underscores.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專案名稱不得超過 128 個字元，且只能包含字母、數字、虛線和底線。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.ProjectNameRequiredErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A Weights & Biases project name is required.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要 Weights & Biases 專案名稱。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.Tags" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.TagsPlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[azureOpenai/{ftjob-abcdef}, azureOpenai/{base-model}, azureOpenai/finetune]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[azureOpenai/{ftjob-abcdef}, azureOpenai/{base-model}, azureOpenai/finetune]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.Fields.WandBTagsErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags can only contain letters, numbers, dashes, spaces, semi-colons, forward slashes, curly brackets and underscores.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤只能包含字母、數字、虛線、空格、分號、斜線、大括號和底線。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.IntegrationDisabled.Link" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about Weights & Biases integration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 [權數與偏差]5D; 整合。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.IntegrationDisabled.Message" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your admin must enable Weights & Biases integration to log runs in your Weights & Biases project for fine-tuning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的系統管理員必須啟用 [權數與偏差]5D; 整合，才能在 [權數與偏差]5D; 專案中記錄執行以進行微調。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to log this fine-tuning job to Weights & Biases?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您要將此微調作業記錄至 Weights & Biases 嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.No" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[否]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This integration will automatically log metrics, parameters, and other information related to the fine-tuning job to the specified Weights & Biases project.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此整合會自動將與微調工作相關的計量、參數及其他資訊記錄到指定的 Weights & Biases 專案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.TooltipLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Weights & Biases integration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Weights & Biases 整合。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";WandBIntegrationFields.LogToWandBSwitch.Yes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Yes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
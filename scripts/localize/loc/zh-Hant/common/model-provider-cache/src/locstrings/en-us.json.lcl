﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\1\s\common\model-provider-cache\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-TW" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";Announcements.AI21.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI21's cost-effective model optimized for long context, now available on Azure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI21 針對長內容最佳化的符合成本效益模型，現已在 Azure 上推出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AI21.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mamba-based models with unrivaled efficiency, latency, and long context handling.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有無可比對效率、延遲和長內容處理的 Mamba 型模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AI21.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI21 Jamba-Instruct is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隆重推出 AI21 Jamba-Instruct!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AI21.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet Jamba 1.5 Large + Jamba 1.5 Mini]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[認識 Jamba 1.5 Large + Jamba 1.5 Mini]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AIStudioStatic.BlogLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AIStudioStatic.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore models from Meta, Mistral, and more, and get access to new features like Azure AI Services.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索來自 Meta、Mistral 等的模型，並存取新功能，例如 Azure AI 服務。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Discover a catalog of thousands of large and small models from providers including {Meta}, {Cohere}, {Mistral}, and more]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AIStudioStatic.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a project to get more models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立專案以取得更多模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Get access to even more models]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.ActionControl.CheckOutModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check out model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.ActionControl.CheckOutModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check out models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.ActionControl.ExploreMoreModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore more models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索更多模組]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.ActionControl.TryLimitedAccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try limited access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嘗試有限存取權]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latest version of GPT-4o, the most advanced multimodal model from OpenAI, is now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最新版的 GPT-4o 是 OpenAI 中最進階的多模式模型，現已推出]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[OpenAI's most cost effective model, GPT-4o mini is now available on Azure AI Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latest multi-modal models include gpt-4.1 (now live) and gpt-4.1-mini/gpt-4.1-nano coming soon]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最新的多重模式模型包括 gpt-4.1(現已上線) 以及即將推出的 gpt-4.1-mini/gpt-4.1-nano]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText11" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[o3 and o4-mini offer significantly enhanced reasoning, quality, and performance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o3 和 o4-mini 提供顯著增強的推理功能、品質和效能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText12" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create high quality images for industry use cases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為業界使用案例建立高品質影像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The o1 series feature an enhanced reasoning abilities to solve science and coding problems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o1 系列具有增強的推理能力，可解決科學與編碼問題。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The o1 series feature an enhanced reasoning abilities to solve science and coding problems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o1 系列具有增強的推理能力，可解決科學與編碼問題。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ GPT-4o realtime audio, now 60% lower cost with improved voice quality and input reliability]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ GPT-4o 即時音訊，現在 60% 較低的成本，並提升語音品質和輸入可靠性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build with advanced audio processing and asynchronous speech generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用進階音訊處理和異步語音產生來建置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[o3-mini includes the o1 features with significant cost-efficiencies for scenarios requiring high performance.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o3-mini 包含 o1 功能，對於需要高效能的案例具有重大的成本效率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced audio at a fraction of the cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以成本的一部分為分級的進階音訊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText8" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The latest GPT model that excels at diverse text and image tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最新的 GPT 模型，擅長處理多樣的文字和影像工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText9" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create your agent with computer-use-preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用電腦使用預覽建立您的代理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try improved GPT-4o]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嘗試改良的 GPT-4o]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Introducing GPT-4o mini]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[gpt-4.1 Model Series]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[gpt-4.1 模型系列]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title11" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock Enterprise Agent workflows with o3 and o4-mini]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 o3 和 o4-mini 解鎖 Enterprise Agent 工作流程]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title12" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing gpt-image-1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[gpt-image-1 的介紹]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Introducing Gpt-image-1]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experience the {o1} models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[體驗 {o1} 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[o1 is generally available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o1 已正式推出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Realtime Audio Updates]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即時音訊 匯報]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[4o Audio Preview is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[4o 音訊預覽在這裡！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[o3-mini is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o3-mini 就在這裡！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[4o Mini Audio is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[4o 迷你音訊在這裡！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title8" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing GPT-4.5 Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT-4.5 預覽版簡介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title9" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Responses API with CUA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有 CUA 的回應 API]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Bria.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A text-to-image model trained exclusively on licensed data with legal liability coverage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字轉換影像模型，專門針對具有法務責任涵蓋範圍的授權資料定型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Bria.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Announcing {Bria23Fast}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已宣佈 {Bria23Fast}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Announcing {Bria32Fast}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Cohere} Rerank, the leading AI model for reranking, is now available on Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一流的重新排序 AI 模型 {Cohere} Rerank 現已可於 Azure 使用]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Cohere's Enterprise AI models include Command R, Command R+, and Embed v3.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere's collection now includes Command R 08-2024 and Command R+ 08-2024.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere 的集合現在包含 Command R 08-2024 和 Command R+ 08-2024。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multimodal capabilities for {Embed3} now available in Cohere's collection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Embed3} 的多模式功能現在已可在 Cohere 的集合中使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A powerful and efficient model for enhancing search systems across 100 languages.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一個強大且高效的模型，用於提升 100 種語言的搜尋系統。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere’s Command A and Embed 4 offer max performance with minimal compute.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere 的 Command A 和 Embed 4 以最小的計算提供最佳效能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Cohere} Rerank is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隆重推出 {Cohere} Rerank!]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[{Cohere} models are here!]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News from Cohere!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自 Cohere 的新消息！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News from Cohere!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自 Cohere 的新消息!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere Rerank v3.5 on Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 上的 Cohere Rerank v3.5]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing Command A and Embed 4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Command A 和 Embed 4 的簡介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Core42.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Jais} from {Core42}, available first on Azure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Core42} 的 {Jais}，可先在 Azure 上使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Core42.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Jais} from {Core42} now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Core42} 的 {Jais} 現在已可使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Deci.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing {DeciLM}, {DeciCoder} and {DeciDiffusion}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{DeciLM}、{DeciCoder} 和 {DeciDiffusion} 簡介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Deci.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{DeciAI} Generative AI models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{DeciAI} 生成式 AI 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A highly capable reasoning model designed to excel at science and coding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專為執行科學與編碼工作而設計的高能力推理模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We present DeepSeek-V3, a strong Mixture-of-Experts (MoE) language model with 671B total parameters with 37B activated for each token.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們推出 DeepSeek-V3，這是一款強大的混合式專家 (MoE) 語言模型，擁有 671B 的總參數，其中每個權仗啟用 37B 參數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DeepSeek-V3-0324 demonstrates notable improvements over its predecessor, DeepSeek-V3, in several key aspects.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-V3-0324 相較於其前身 DeepSeek-V3 在幾個關鍵方面顯示出顯著改進。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing DeepSeek-R1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-R1 簡介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DeepSeek-V3 is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-V3 現已推出!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DeepSeek-V3-0324 is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-V3-0324 現已推出!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Gretel.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate production-quality synthetic data optimized for AI and machine learning development.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生針對 AI 和機器學習開發優化的生產質量綜合數據。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Gretel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Gretel Navigator Tabular is now available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Gretel Navigator 表格式現已推出！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meta's next generation model, {Llama31405B} available on Azure now]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Meta 的下一代模型 {Llama31405B}，現可在 Azure 上使用]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Serverless APIs for Meta-Llama-3-8B-Instruct and Meta-Llama-3-70B-Instruct models]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try out Meta's latest {Llama32} SLMs and image reasoning models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[試試看 Meta 的最新 {Llama32} SLM 和影像推理模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Llama32} 11B Vision Instruct and 90B Vision Instruct are here for your image reasoning use cases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Llama32} 11B Vision Instruct 和 90B Vision Instruct，以供影像推理使用案例使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhanced reasoning, math, and instruction following with performance comparable to Llama 3.1 405B.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增強推理、數學和指示，其效能可與 Llama 3.1 405B 相容。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build highly personalized experiences for every use case at a lower cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以較低的成本為每個使用案例打造高度個人化的體驗]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet {Llama31405B}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[認識 {Llama31405B}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Build the future of AI with {Meta} {LlaMA3}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meta {Llama32} models are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Meta {Llama32} 模型隆重登場!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama 3.3 from Meta]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自中繼的 Llama 3.3]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing Llama 4 models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推出 Llama 4 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Large (2407) and Nemo, Mistral AI's latest models, now available in Azure AI Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral AI 的最新模型 Large (2407) 和 Nemo，現已在 Azure AI Foundry 中推出]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Large (2407) and Nemo, Mistral AI's latest models, now available in Azure AI Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ministral 3B provide a compute-efficient and low-latency solution.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ministral 3B 提供有效計算和低延遲的解決方案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experience the power of new Mistral Large 24.11 with improved function calling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用改良功能呼叫體驗新Mistral Large 24.11的威力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empowering Developers, Transforming Code]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[讓開發人員能夠使用、正在轉換程序代碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhance Mistral AI models' performance with customization.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用自定義來增強訊息 AI 模型的效能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhanced Mistral Small 3 with multimodal capabilities and a 128k context length. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增強版 Mistral Small 3 具備多模態功能，並擁有 128k 的内容長度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Document conversion to markdown with interleaved images and text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件轉換為含有圖片與文字交錯的 Markdown 格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral Large (2407) & Nemo are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral Large (2407) 和 Nemo 都在這裡!]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[{MistralSmall} is now available!]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New SLM from Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自 Mistral 的新 SLM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News from Mistral AI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自星體 AI 的新聞]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Codestral 2501 from Mistral AI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自 Mistral AI 的 Codestral 2501]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral Finetuning available for 2411/NeMo/3B]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[2411/NeMo/3B 適用的Mistral Finetuning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News from Mistral AI: Mistral Small 3.1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral AI: Mistral Small 3.1 的最新消息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral OCR 25.03]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral OCR 25.03]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Muse.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unleash creativity with Muse! Explore how to generate diverse, consistent, and innovative 3D gameplay!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 [馬斯]5D; 釋放創造力！探索如何產生多元、一致且創新的 3D 遊戲！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Muse.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse is now available on AI Foundry!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[現在可在 AI Foundry 上使用 Muse！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.NTTDATA.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{tsuzumi} is a lightweight, fine-tuneable model with strong Japanese capabilities!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{tsuzumi} 是輕量型、可微調的模型，並具有強大的日文功能!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.NTTDATA.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{tsuzumi} is now available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{tsuzumi} 現已推出!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nixtla.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TimeGEN-1 the timeseries forecasting model, available first on Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TimeGEN-1 時間序列預測模型，首先在 Azure 上提供]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nixtla.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla's TimeGEN-1 is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla's TimeGEN-1 推出了!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Production-ready models, optimized for performance, hosted by {AzureAI}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生產環境就緒模型，針對效能的最佳化，由 {AzureAI} 裝載]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TCO and performance optimized models to power your AI applications]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擁有權總成本與效能最佳化模型，為您的 AI 應用程式提供動力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{NVIDIA} AI foundation models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{NVIDIA} AI 基礎模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{NVIDIANIM} now available on {AIFoundry}!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{NVIDIANIM} 現在可以在 {AIFoundry} 上使用！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.OpenAI.SubTextopenai-050825-KA" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Push the open model frontier with gpt-oss-120b and gpt-oss-20b, released under the permissive Apache 2.0 license.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 gpt-oss-120b 和 gpt-oss-20b 推動開放模型的前沿，並在寬鬆的 Apache 2.0 授權下發佈。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.OpenAI.Titleopenai-050825-KA" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Announcing new gpt-oss models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[宣布全新 gpt-oss 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft's latest {Phi3} SLMs offer groundbreaking performance at a small size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 最新 {Phi3} SLM 以小尺寸提供突破性的效能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft's latest {Phi35} MoE and Mini models now support 20+ languages.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的最新 {Phi35} MoE 和迷你模型現在支援 20 多種語言。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4 14B, a highly capable model for low latency scenarios]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4 14B，適用於低延遲案例的高度支援模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[First multimodal SLM with 3 inputs (text, audio, image) in a unified architecture.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[首個在統一結構中具備三種輸入 (文字、音訊、影像) 的多模態 SLM。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhanced quality, reasoning, efficiency, and speed, all packed into a compact size.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提升的品質、推理能力、效率與速度，而且精巧。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Improving harm protections with competitive reasoning capabilities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過競爭性推理功能改善傷害保護。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Post-trained to address output limitations with better safety performance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[經過訓練後可解決輸出限制並提供更好的安全效能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Phi3} Finetuning is available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Phi3} 微調功能已推出!]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[{Phi3} Serverless APIs are now available!]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Phi35} models are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Phi35} 模型在這裡!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4 is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4 在這裡！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-Multimodal is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-Multimodal 正式發佈!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-Mini is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-Mini 正式發佈!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A new reasoning model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[全新的推理模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MAI-DS-R1 reasoning model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MAI-DS-R1 推理模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Sdaia.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ALLaM-2-7B is here! A robust 7B LLM model crafted to boost Arabic language technology]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隆重推出 ALLaM-2-7B!為提升阿拉伯文語言技術所打造的強固 7B LLM 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Sdaia.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ALLaM-2-7B: latest Arabic LLM]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ALLaM-2-7B: 最新的阿拉伯文 LLM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovGPT4o.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPT-4o, the highest performing multimodal model from OpenAI, now available on {Azure}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT-4o 是 OpenAI 的最高執行多模式模型，現已在 {Azure} 上提供]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovGPT4o.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPT-4o is available on {Azure}!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT-4o 現在 {Azure} 上提供!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovLlama31.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A collection of pretrained and instruction turned generative text models in 8 and 70B sizes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一組預先訓練與指令的集合以 8 到 70B 的大小轉換了產生文字模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovLlama31.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet {LlaMA31} series]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[認識 {LlaMA31} 系列]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovPhi3.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft's {Phi3} SLMs offer groundbreaking performance at a small size.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的 {Phi3} SLM 以小尺寸提供突破性的效能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovPhi3.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore {Phi3} models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 {Phi3} 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{StabilityAI} models available first on {Azure}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{StabilityAI} 模型可先在 {Azure} 上使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stability AI’s most advanced model delivers professional-grade image generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[穩定性 AI 最進階的模型提供專業等級的影像產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{StabilityAI} models are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{StabilityAI} 模型在這裡!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stable Diffusion 3.5 Large is now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[穩定擴散 3.5 大型版本現已推出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.SubTextaoai-022725" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The latest GPT model that excels at diverse text and image tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最新的 GPT 模型，擅長處理多樣的文字和影像工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.SubTextopenai-240625-AO" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[codex-mini is a fine-tuned o4-mini model for fast, precise CLI automation, scripting, and repo refactoring]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[codex-mini 是微調的 o4-mini 模型，適用於快速、精確的 CLI 自動化、指令碼處理和存放庫重構]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.SubTextopenai-270525-Kz" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate up to 20s 1080p videos with Sora and its unique API: text-to-video now, image-to-video coming soon.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Sora 及其獨特的 API 生成高達 20 秒的 1080p 影片：文字轉換影片功能現已推出，影像轉換影片功能即將推出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.Titleaoai-022725" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing GPT-4.5 Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT-4.5 預覽版簡介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.Titleopenai-240625-AO" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[codex-mini: Fast, Scalable Code Generation for the CLI Era]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[codex-mini：適用於 CLI Era 的快速、可調整程式碼產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.Titleopenai-270525-Kz" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Sora]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.blackforestlabs.SubTextblackforestlabs-230725-jn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Black Forest Labs foundational image generation and editing models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Black Forest Labs 基礎影像產生與編輯模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.blackforestlabs.Titleblackforestlabs-230725-jn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FLUX.1 Kontext [pro]5D; and FLUX1.1 [pro]5D; are now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FLUX.1 Kontext [pro]5D; 和 FLUX1.1 [pro]5D; 現已可供使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.deepseek.SubTextdeepseek-030625-AP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Improved reasoning, fewer hallucinations, better coding and functions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[改善推理能力、較少錯覺、更好的編碼與函式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.deepseek.Titledeepseek-030625-AP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing DeepSeek-R1-0528]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-R1-0528 簡介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.mistral.SubTextmistralai-120525-op" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral Medium 3 delivers state-of-the-art performance at lower cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral Medium 3 以較低的成本提供最先進的效能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.mistral.Titlemistralai-120525-op" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral Medium 3 (25.05)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral Medium 3 (25.05)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.phi.SubTextmicrosoft-160525-rb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Route prompts to different models to help save cost while maintaining quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將提示路由到不同的模型，協助節省成本，同時維持品質]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.phi.SubTextmicrosoft-300425-jp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Small models with the power to reason, solve, and unlock complex understanding]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具備推理、解決問題及解鎖複雜理解能力的小型模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.phi.Titlemicrosoft-160525-rb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing Model Router]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型路由器簡介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.phi.Titlemicrosoft-300425-jp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New Phi reasoning models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新的 Phi 推理模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.xai.SubTextxai-180525-Pu" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A model suite blending superior reasoning with extensive pre-training knowledge.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[混合優異推理與廣泛訓練前知識的一個模型套件。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.xai.Titlexai-180525-Pu" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing Grok 3]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Grok 3 簡介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.common.section_1_client_creation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To create a client with the OpenAI SDK using an API key, initialize the client by passing your API key to the SDK's configuration. This allows you to authenticate and interact with OpenAI's services seamlessly:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用 API 金鑰來建立具有 OpenAI SDK 的用戶端，請將您的 API 金鑰傳遞至 SDK 的設定以初始化用戶端。這可讓您無縫地驗證並與 OpenAI 的服務進行互動:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For OpenAI API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對 OpenAI API 端點，請部署模型以產生端點 URL，並獲取 API 金鑰來驗證服務。在此範例中，端點和金鑰是持有端點 URL 和 API 金鑰的字串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.csharp.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://www.nuget.org/packages/OpenAI-DotNet/). For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-dotnet) and [samples]5D;(https://github.com/openai/openai-dotnet).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://www.nuget.org/packages/OpenAI-DotNet/). 如需 Azure OpenAI SDK 的其他資訊，請參閱完整 [documentation]5D;(https://github.com/openai/openai-dotnet) 與 [samples]5D;(https://github.com/openai/openai-dotnet).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.java.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about  Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-java) and [samples]5D;(https://github.com/openai/openai-java).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例代碼段。如需有關 Azure OpenAI SDK 的其他資訊，請參閱完整 [documentation]5D;(https://github.com/openai/openai-java) 與 [samples]5D;(https://github.com/openai/openai-java).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.javascript.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-node) and [samples]5D;(https://github.com/openai/openai-node).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例代碼段。如需有關 Azure OpenAI SDK 的其他資訊，請參閱完整 [documentation]5D;(https://github.com/openai/openai-node) 與 [samples]5D;(https://github.com/openai/openai-node).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.python.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-python) and [samples]5D;(https://github.com/openai/openai-python).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例代碼段。如需有關 Azure OpenAI SDK 的其他資訊，請參閱完整 [documentation]5D;(https://github.com/openai/openai-python) 與 [samples]5D;(https://github.com/openai/openai-python).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.python.install_sdk" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure Open AI SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 PIP 安裝 Azure Open AI SDK (需要: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 認證進行驗證]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create a personal access token]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not need to give any permissions to the token. Note that the token will be sent to a Microsoft service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您不需要授與權杖任何權限。請注意，權杖將傳送至 Microsoft 服務。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the code snippets below, create an environment variable to set your token as the key for the client code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下列程式碼片段，請建立環境變數，將您的權杖設定為用戶端程式碼的金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using bash:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您正在使用 bash:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_3c" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authorization is easiest using [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity). It finds the best credential to use in its running environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity) 授權最簡單。它會在其執行環境中找到最佳的認證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're in powershell:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您在 PowerShell 中:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_4c" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Set the values of the client ID, tenant ID, and client secret of the AAD application as environment variables: `AZURE_CLIENT_ID`, `AZURE_TENANT_ID`, `AZURE_CLIENT_SECRET`.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將 AAD 應用程式的用戶端識別碼、租用戶識別碼和用戶端密碼值設定為環境變數: `AZURE_CLIENT_ID`，`AZURE_TENANT_ID`，`AZURE_CLIENT_SECRET`。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using Windows command prompt:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您正在使用 Windows 命令提示字元:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝相依性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run a basic code sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[執行基本程式碼範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_3_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a basic call to the chat completion API. The call is synchronous.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此範例示範對聊天完成 API 的基本呼叫。呼叫為同步的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore more samples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索更多範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a multi-turn conversation with the chat completion API. When using the model for a chat application, you'll need to manage the history of that conversation and send the latest messages to the model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此範例示範與聊天完成 API 的多回合交談。將該模型用於聊天應用程式時，您必須管理該交談的歷程記錄，並將最新的訊息傳送至模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a better user experience, you will want to stream the response of the model so that the first token shows up early and you avoid waiting for long responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為了獲得更佳的使用者體驗，您會想要串流模型的回應，好讓第一個權杖提早顯示，並避免長時間等待回應。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4_s_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run a multi-turn conversation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[執行多回合交談]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4_s_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stream the output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[串流輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Going beyond rate limits]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[超出速率限制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_5_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rate limits for the playground and free API usage are intended to help you experiment with models and prototype your AI application. For use beyond those limits, and to bring your application to scale, you must provision resources from an Azure account, and authenticate from there. You don't need to change anything else in your code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[遊樂場和免費 API 使用量的速率限制是為了協助您實驗模型和 AI 應用程式的原型。針對超出這些限制的使用量，並為了調整應用程式規模，您必須從 Azure 帳戶佈建資源，並從該處進行驗證。您不需要變更程式碼中的其他內容。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The rate limits for the playground and free API usage are intended to help you experiment with models and prototype your AI application. For use beyond those limits, and to bring your application to scale, you must provision resources from an Azure account, and authenticate from there. You don't need to change anything else in your code. Use this link to discover how to go beyond the free tier limits in Azure AI.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_5_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure hosted. AI powered, can make mistakes. Subject to [Product Terms]5D;(https://aka.ms/amlprodexps/terms) & [Privacy Statement]5D;(https://aka.ms/amlprodexps/privacy). Not intended for production/sensitive data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 託管。由 AI 提供，可能有錯誤。受[產品條款]5D;(https://aka.ms/amlprodexps/terms)和[隱私權聲明]5D;(https://aka.ms/amlprodexps/privacy)。不適用於生產/敏感性資料。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure hosted. AI powered, can make mistakes. . Subject to Product Terms & Privacy Statement. Not intended for production/sensitive data.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Started]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.csharp.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package). For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[NuGet 套件的連結和一些範例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package)。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples)。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The link to the NuGet package and some examples. For additional information about Azure AI Inference SDK, see full documentation and samples.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.csharp.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for C# supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於 C# 的 Azure SDK 支援 Azure 身分識別套件，可讓您輕鬆地從 Microsoft 身分識別平台取得認證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.csharp.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [dotnet runtime]5D;(https://dotnet.microsoft.com/en-us/download/dotnet/)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝 [dotnet 執行階段]5D;(https://dotnet.microsoft.com/en-us/download/dotnet/)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Install dotnet runtime]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.csharp.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open power shell at the desired project folder, restore the environment and run the program.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在所需的專案資料夾開啟電源殼層、還原環境並執行程式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.curl.mistral_ocr_callout" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: Only base64 data is supported, document url or image url is not supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 僅支援 base64 資料，不支援文件 URL 或影像 URL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.curl.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將下列專案貼到殼層中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.curl.section_3_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Call the chat completion API and pass the chat history:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[呼叫聊天完成 API 並傳遞聊天記錄:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.curl.section_3_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is an example of calling the endpoint and streaming the response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是呼叫端點並串流回應的範例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/java/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/java/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/java/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/java/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 認證進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for Java supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於 Java 的 Azure SDK 支援 Azure 身分識別套件，可讓您輕鬆地從 Microsoft 身分識別平台取得認證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication with AAD requires adding azure identity package. To install, add this in your pom.xml:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 AAD 驗證需要新增 Azure 身分識別套件。若要安裝，請將此新增至您的 pom.xml:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To install, add this <dependency> in your maven pom.xml:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要安裝，請在 maven pom.xml 中新增此 <dependency>:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a sample.java file and run as a package, for instance:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對下列每個程式碼片段，將內容複製到sample.java 檔案，並以套件執行，例如:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 認證進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以使用 [Azure 身分識別庫]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity) 向 Azure Active Directory 驗證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the @azure/identity package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下方顯示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) 提供者，或 Azure SDK 所提供的其他認證提供者，請安裝@azure/identity 套件:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [Node.js]5D;(https://nodejs.org/).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝 [Node.js]5D;(https://nodejs.org/)。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Install Node.js.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy the following lines of text and save them as a file package.json inside your folder.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[複製下列文字行，並將它們儲存為資料夾內的檔案 package.json。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: @azure/core-sse is only needed when you stream the chat completions response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 只有當您串流聊天完成回應時，才需要@azure/core-sse。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open a terminal window in this folder and run npm install.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此資料夾中開啟終端機視窗，並執行 npm 安裝。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a file sample.js and run with node sample.js.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對下列每個程式碼片段，將內容複製到檔案 sample.js 並使用節點 sample.js 執行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 認證進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以使用 [Azure 身分識別庫]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 向 Azure Active Directory 驗證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下方顯示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) 提供者，或 Azure SDK 所提供的其他認證提供者，請安裝 [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 套件:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure AI Inference SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 PIP 安裝 Azure AI 推斷 SDK (需要: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To authenticate with the model you will need to set up an Azure production key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用模型進行驗證，您必須設定 Azure 生產金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not need to give any permissions to the token. Note that the token will be sent to a Microsoft service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您不需要授與權杖任何權限。請注意，權杖將傳送至 Microsoft 服務。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the code snippets below, create an environment variable to set your token as the key for the client code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下列程式碼片段，請建立環境變數，將您的權杖設定為用戶端程式碼的金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_3c" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authorization is easiest using [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity). It finds the best credential to use in its running environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity) 授權最簡單。它會在其執行環境中找到最佳的認證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using bash:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您正在使用 bash:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_4c" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Set the values of the client ID, tenant ID, and client secret of the AAD application as environment variables: `AZURE_CLIENT_ID`, `AZURE_TENANT_ID`, `AZURE_CLIENT_SECRET`.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將 AAD 應用程式的用戶端識別碼、租用戶識別碼和用戶端密碼值設定為環境變數: `AZURE_CLIENT_ID`，`AZURE_TENANT_ID`，`AZURE_CLIENT_SECRET`。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're in powershell:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您在 PowerShell 中:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using Windows command prompt:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您正在使用 Windows 命令提示字元:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝相依性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run a basic code sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[執行基本程式碼範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_3_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a call to embeddings API.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此範例示範內嵌 API 的呼叫。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_3_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[It is leveraging the Azure AI model inference endpoint and your AAD token. The call is synchronous.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[其正在利用 Azure AI 模型推斷端點和您的 AAD 權杖。呼叫為同步的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Going beyond rate limits]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[超出速率限制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_4_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rate limits for the playground and free API usage are intended to help you experiment with models and prototype your AI application. For use beyond those limits, and to bring your application to scale, you must provision resources from an Azure account. You don't need to change anything else in your code. Use this link to discover how to go beyond the free tier limits in Azure AI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[遊樂場和免費 API 使用量的速率限制是為了協助您實驗模型和 AI 應用程式的原型。針對超出這些限制的使用量，並為了調整應用程式規模，您必須從 Azure 帳戶佈建資源。您不需要變更程式碼中的其他專案。使用此連結，探索如何超越 Azure AI 中的免費層限制。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_4_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure hosted. AI powered, can make mistakes. Subject to [Product Terms]5D;(https://www.microsoft.com/licensing/terms/productoffering/MicrosoftAzure/MCA) & [Privacy Statement]5D;(https://www.microsoft.com/licensing/terms/product/PrivacyandSecurityTerms/MCA). Not intended for production/sensitive data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 託管。由 AI 提供，可能有錯誤。受[產品條款]5D;(https://www.microsoft.com/licensing/terms/productoffering/MicrosoftAzure/MCA)和[隱私權聲明]5D;(https://www.microsoft.com/licensing/terms/product/PrivacyandSecurityTerms/MCA)。不適用於生產/敏感性資料。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure hosted. AI powered, can make mistakes. . Subject to Product Terms & Privacy Statement. Not intended for production/sensitive data.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Started]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package). For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[NuGet 套件的連結和一些範例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package)。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 認證進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for C# supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於 C# 的 Azure SDK 支援 Azure 身分識別套件，可讓您輕鬆地從 Microsoft 身分識別平台取得認證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install dotnet runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝 dotnet 執行階段]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open power shell at the desired project folder, restore the environment and run the program.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在所需的專案資料夾開啟電源殼層、還原環境並執行程式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 認證進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以使用 [Azure 身分識別庫]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity) 向 Azure Active Directory 驗證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the @azure/identity package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下方顯示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) 提供者，或 Azure SDK 所提供的其他認證提供者，請安裝@azure/identity 套件:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [Node.js]5D;(https://nodejs.org/)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝 [Node.js]5D;(https://nodejs.org/)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Install Node.js.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy the following lines of text and save them as a file package.json inside your folder.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[複製下列文字行，並將它們儲存為資料夾內的檔案 package.json。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: @azure/core-sse is only needed when you stream the chat completions response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 只有當您串流聊天完成回應時，才需要@azure/core-sse。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open a terminal window in this folder and run npm install.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此資料夾中開啟終端機視窗，並執行 npm 安裝。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a file sample.js and run with node sample.js.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對下列每個程式碼片段，將內容複製到檔案 sample.js 並使用節點 sample.js 執行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 認證進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以使用 [Azure 身分識別庫]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 向 Azure Active Directory 驗證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下方顯示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) 提供者，或 Azure SDK 所提供的其他認證提供者，請安裝 [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 套件:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure AI Inference SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 PIP 安裝 Azure AI 推斷 SDK (需要: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ChatCompletion.common.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using API Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 API 金鑰進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ChatCompletion.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For Serverless API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若為無伺服器 API 端點，請部署模型以產生端點 URL 與 API 金鑰，以針對服務進行驗證。在此範例端點和金鑰中，有持有端點 URL 與 API 金鑰的字串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ChatCompletion.common.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The API endpoint URL and API key can be found on the Deployments + Endpoint page once the model is deployed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型后，即可在 [部署 + 端點]5D; 頁面上找到 API 端點 URL 與 API 金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ChatCompletion.common.section_1_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure inferencing package includes a client for Chat Completion, `ChatCompletionsClient`. A client can be authenticated using the API key. The code sample creates and authenticates a synchronous ChatCompletionsClient:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 推斷套件包含聊天完成的用戶端『ChatCompletionsClient』。用戶端可以使用 API 金鑰進行驗證。程式代碼範例會建立並驗證同步 ChatCompletionsClient：]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure inferencing package includes client for Chat Completion, `ChatCompletionsClient`. A client can be authenticated using the API key. The code sample create and authenticates a synchronous ChatCompletionsClient:]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.Embeddings.common.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using API Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 API 金鑰進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.Embeddings.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For Serverless API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若為無伺服器 API 端點，請部署模型以產生端點 URL 與 API 金鑰，以針對服務進行驗證。在此範例端點和金鑰中，有持有端點 URL 與 API 金鑰的字串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.Embeddings.common.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The API endpoint URL and API key can be found on the Deployments + Endpoint page once the model is deployed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型后，即可在 [部署 + 端點]5D; 頁面上找到 API 端點 URL 與 API 金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.Embeddings.common.section_1_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure inferencing package includes a client for Embeddings, `EmbeddingsClient`. A client can be authenticated using the API key. The code sample creates and authenticates a synchronous EmbeddingsClient:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 推斷套件包含內嵌用戶端 『EmbeddingsClient』。用戶端可以使用 API 金鑰進行驗證。程式代碼範例會建立並驗證同步 EmbeddingsClient：]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure inferencing package includes client for Embeddings, `EmbeddingsClient`. A client can be authenticated using the API key. The code sample create and authenticates a synchronous EmbeddingsClient:]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ImageGeneration.curl.editing_prompt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Make this black and white]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將此項設為黑白]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ImageGeneration.curl.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To generate an image, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要產生影像，將下列項目貼到殼層中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ImageGeneration.curl.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To edit an image, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要編輯影像，將下列項目貼到殼層中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.VideoGeneration.curl.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To generate a video, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要產生視訊，將下列項目貼到殼層中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1119903703" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [Node.js]5D;(https://nodejs.org/).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝 [Node.js]5D;(https://nodejs.org/)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1135713175" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the code snippets below, create an environment variable to set your token as the key for the client code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下列程式碼片段，請建立環境變數，將您的權杖設定為用戶端程式碼的金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1147064527" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下方顯示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) 提供者，或 Azure SDK 所提供的其他認證提供者，請安裝 [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 套件:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1197875290" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://www.nuget.org/packages/OpenAI-DotNet/). For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-dotnet) and [samples]5D;(https://github.com/openai/openai-dotnet).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[NuGet 套件的連結和一些範例]5D;(https://www.nuget.org/packages/OpenAI-DotNet/)。有關 Azure OpenAI SDK 的其他資訊，請參閱完整 [文件]5D;(https://github.com/openai/openai-dotnet) 和 [範例]5D;(https://github.com/openai/openai-dotnet)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1348340131" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stream the output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[串流輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1374737853" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for Java supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於 Java 的 Azure SDK 支援 Azure 身分識別套件，可讓您輕鬆地從 Microsoft 身分識別平台取得認證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1488390251" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-java) and [samples]5D;(https://github.com/openai/openai-java).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure OpenAI SDK 的其他資訊，請參閱完整 [文件]5D;(https://github.com/openai/openai-java) 和 [範例]5D;(https://github.com/openai/openai-java)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1506840677" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're in powershell:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您在 PowerShell 中:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1527034862" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To edit an image, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要編輯影像，將下列項目貼到殼層中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1554734733" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Call the chat completion API and pass the chat history:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[呼叫聊天完成 API 並傳遞聊天記錄:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_157578493" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To create a client with the OpenAI SDK using an API key, initialize the client by passing your API key to the SDK's configuration. This allows you to authenticate and interact with OpenAI's services seamlessly:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用 API 金鑰來建立具有 OpenAI SDK 的用戶端，請將您的 API 金鑰傳遞至 SDK 的設定以初始化用戶端。這可讓您無縫地驗證並與 OpenAI 的服務進行互動:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1604730713" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure AI Inference SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 PIP 安裝 Azure AI 推斷 SDK (需要: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1632773573" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authorization is easiest using [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity). It finds the best credential to use in its running environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity) 授權最簡單。它會在其執行環境中找到最佳的認證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1657218110" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[It is leveraging the Azure AI model inference endpoint and your AAD token. The call is synchronous.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[其正在利用 Azure AI 模型推斷端點和您的 AAD 權杖。呼叫為同步的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1675574254" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[4. Explore more samples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[4. 探索更多範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1723953779" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-node) and [samples]5D;(https://github.com/openai/openai-node).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure OpenAI SDK 的其他資訊，請參閱完整 [文件]5D;(https://github.com/openai/openai-node) 和 [範例]5D;(https://github.com/openai/openai-node)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_176344427" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-python) and [samples]5D;(https://github.com/openai/openai-python).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure OpenAI SDK 的其他資訊，請參閱完整 [文件]5D;(https://github.com/openai/openai-python) 和 [範例]5D;(https://github.com/openai/openai-python)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1820008423" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To install, add this <dependency> in your maven pom.xml:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要安裝，請在 maven pom.xml 中新增此 <dependency>:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2018945115" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using Windows command prompt:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您正在使用 Windows 命令提示字元:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2092433665" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for C# supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於 C# 的 Azure SDK 支援 Azure 身分識別套件，可讓您輕鬆地從 Microsoft 身分識別平台取得認證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2123457486" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure inferencing package includes a client for Chat Completion, `ChatCompletionsClient`. A client can be authenticated using the API key. The code sample creates and authenticates a synchronous ChatCompletionsClient:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 推斷套件包含聊天完成的用戶端 `ChatCompletionsClient`。用戶端可以使用 API 金鑰進行驗證。程式碼範例會建立並驗證同步 ChatCompletionsClient:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2193611328" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is an example of calling the endpoint and streaming the response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是呼叫端點並串流回應的範例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_22025242" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[2. Run a basic code sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[2. 執行基本程式碼範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2375228290" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/java/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/java/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/java/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/java/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2396968480" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: Only base64 data is supported, document url or image url is not supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 僅支援 base64 資料，不支援文件 URL 或影像 URL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2457896902" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以使用 [Azure 身分識別庫]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 向 Azure Active Directory 驗證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2510457272" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a sample.java file and run as a package, for instance:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對下列每個程式碼片段，將內容複製到sample.java 檔案，並以套件執行，例如:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2568478869" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: @azure/core-sse is only needed when you stream the chat completions response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 只有當您串流聊天完成回應時，才需要@azure/core-sse。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2611584301" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[3. Explore more samples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[3. 探索更多範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2767097481" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [Node.js]5D;(https://nodejs.org/)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝 [Node.js]5D;(https://nodejs.org/)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2792274105" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To generate a video, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要產生視訊，將下列項目貼到殼層中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2883618492" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure Open AI SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 PIP 安裝 Azure Open AI SDK (需要: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2896283457" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For OpenAI API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對 OpenAI API 端點，請部署模型以產生端點 URL，並獲取 API 金鑰來驗證服務。在此範例中，端點和金鑰是持有端點 URL 和 API 金鑰的字串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_294659216" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure inferencing package includes a client for Embeddings, `EmbeddingsClient`. A client can be authenticated using the API key. The code sample creates and authenticates a synchronous EmbeddingsClient:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 推斷套件包含內嵌的用戶端 `EmbeddingsClient`。用戶端可以使用 API 金鑰進行驗證。程式碼範例會建立並驗證同步 EmbeddingsClient:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3030664098" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not need to give any permissions to the token. Note that the token will be sent to a Microsoft service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您不需要授與權杖任何權限。請注意，權杖將傳送至 Microsoft 服務。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3090420596" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the @azure/identity package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下方顯示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) 提供者，或 Azure SDK 所提供的其他認證提供者，請安裝@azure/identity 套件:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3158250671" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a basic call to the chat completion API. The call is synchronous.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此範例示範對聊天完成 API 的基本呼叫。呼叫為同步的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3163714926" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install dotnet runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝 dotnet 執行階段]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3164412691" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To generate an image, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要產生影像，將下列項目貼到殼層中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3201082139" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication with AAD requires adding azure identity package. To install, add this in your pom.xml:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 AAD 驗證需要新增 Azure 身分識別套件。若要安裝，請將此新增至您的 pom.xml:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3282207501" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using bash:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果您正在使用 bash:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3300085498" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open power shell at the desired project folder, restore the environment and run the program.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在所需的專案資料夾開啟電源殼層、還原環境並執行程式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3322922063" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open a terminal window in this folder and run npm install.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此資料夾中開啟終端機視窗，並執行 npm 安裝。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3352724043" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [dotnet runtime]5D;(https://dotnet.microsoft.com/en-us/download/dotnet/)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安裝 [dotnet 執行階段]5D;(https://dotnet.microsoft.com/en-us/download/dotnet/)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3507107346" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a call to embeddings API.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此範例示範內嵌 API 的呼叫。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3548031495" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將下列專案貼到殼層中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_355307576" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3609311341" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[1. Authentication using API Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[1. 使用 API 金鑰進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3686597116" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Started]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3824329197" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以使用 [Azure 身分識別庫]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity) 向 Azure Active Directory 驗證。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3825307696" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[1. Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[1. 使用 Azure Active Directory 認證進行驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3826507031" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run a multi-turn conversation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[執行多回合交談]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3962477442" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下是一些使用案例的範例程式碼片段。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_4026133267" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For Serverless API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對無伺服器 API 端點，請部署模型以產生端點 URL，並獲取 API 金鑰來驗證服務。在此範例中，端點和金鑰是持有端點 URL 和 API 金鑰的字串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_4037492491" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a better user experience, you will want to stream the response of the model so that the first token shows up early and you avoid waiting for long responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為了獲得更佳的使用者體驗，您會想要串流模型的回應，好讓第一個權杖提早顯示，並避免長時間等待回應。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_4118566957" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a file sample.js and run with node sample.js.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對下列每個程式碼片段，將內容複製到檔案 sample.js 並使用節點 sample.js 執行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_4137497717" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy the following lines of text and save them as a file package.json inside your folder.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[複製下列文字行，並將它們儲存為資料夾內的檔案 package.json。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_466750452" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Set the values of the client ID, tenant ID, and client secret of the AAD application as environment variables: `AZURE_CLIENT_ID`, `AZURE_TENANT_ID`, `AZURE_CLIENT_SECRET`.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將 AAD 應用程式的用戶端識別碼、租用戶識別碼和用戶端密碼值設定為環境變數: `AZURE_CLIENT_ID`，`AZURE_TENANT_ID`，`AZURE_CLIENT_SECRET`。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_589593347" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package). For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[NuGet 套件的連結和一些範例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package)。有關 Azure AI 推斷 SDK 的其他資訊，請參閱完整的[文件]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference)和[範例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_60304132" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a multi-turn conversation with the chat completion API. When using the model for a chat application, you'll need to manage the history of that conversation and send the latest messages to the model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此範例示範與聊天完成 API 的多回合交談。將該模型用於聊天應用程式時，您必須管理該交談的歷程記錄，並將最新的訊息傳送至模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_692681155" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[2. Install dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[2. 安裝相依性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_812828475" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[3. Run a basic code sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[3. 執行基本程式碼範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_863404337" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The API endpoint URL and API key can be found on the Deployments + Endpoint page once the model is deployed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型後，可以在 [部署 + 端點]5D; 頁面上找到 API 端點 URL 和 API 金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.Benchmarks.ActionText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare with benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與基準比較]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.Benchmarks.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model benchmarks are integrated into model catalog for easier navigation. Compare benchmarks across models and datasets available in the industry to assess which one meets your business scenario.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型基準已整合至模型目錄，以便於瀏覽。比較業界可用模型和資料集的基準，以評定符合您商務案例的基準。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.Benchmarks.LearnMoreText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How model benchmarks are scored]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型基準的評分方式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.Benchmarks.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New model benchmarks available now in model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型目錄中現已提供新的模型基準]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.MaaSPtuDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can now purchase, use and manage provisioned throughput units (PTUs) for your serverless API model deployments from Meta, Mistral, Hugging Face and others.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您現在可以從 Meta、Mistral、Hugging Face 及其他平台購買、使用和管理為您的無伺服器 API 模型部署所佈建的輸送量單位 (PTU)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.MaaSPtuTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Now available: Serverless endpoint PTUs for Model Catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[現已推出: 模型目錄的無伺服器端點 PTUs]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.SpeechPlayground.ActionText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check out Speech service models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看語音服務模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.SpeechPlayground.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try out powerful AI Service capabilities for free and build customized solutions using Speech, Language, Vision, Content Safety, Translator, and Document Intelligence APIs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[免費試用強大的 AI 服務功能，並使用語音、語言、視覺、內容安全、翻譯工具及檔智慧 API 建置自定義解決方案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.SpeechPlayground.LearnMoreText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about AI Services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 AI 服務]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.SpeechPlayground.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore all the Azure AI Services now available in AI Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 AI Foundry 中現已提供的所有 Azure AI 服務]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.AI21.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI21's Jamba-Instruct offers a massive 70K context window and the best value for price for long context use cases]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI21 的 Jamba-Instruct 提供 70,000 字的大型內容視窗，是長內容使用案例的最划算選擇]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.AOAI.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is powered by a diverse set of models with different capabilities, such as GPT-4o mini, GPT-4o, DALL-E 3 and Whisper.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 採用一系列功能不同的多個模型，例如 GPT-4o mini、GPT-4o、DALL-E 3 和 Whisper。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is powered by a diverse set of models with different capabilities, such as GPT-4o mini, GPT-4o, DALL-E 3 and Whisper.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.AOAI.description2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is powered by a diverse set of models with different capabilities, such as o1 series, gpt-4o series, DALL-E 3 and Whisper.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 是由具有不同功能的多樣化模型集所提供，例如 o1 系列、GPT-4o 系列、DALL-E 3 和 Whisper。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is powered by a diverse set of models with different capabilities, such as o1 series, gpt-4o series, DALL-E 3 and Whisper.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Cohere.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere's suite of Enterprise AI models is available now including Command R, Command R+, Embed, and Rerank]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere 的企業 AI 模型套件現已推出，包含 Command R、Command R+、Embed 及 Rerank]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Cohere’s suite of Enterprise AI models is available now including Command R, Command R+, Embed, and Rerank]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Gpt4.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is powered by a diverse set of models with different capabilities, such as GPT-4o, GPT-4, DALL-E 3, and Whisper.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 是由一組不同功能的多個模型所提供，例如 GPT-4o、GPT-4、DALL-E 3 和 Whisper。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is powered by a diverse set of models with different capabilities, such as GPT-4o, GPT-4, DALL-E 3, and Whisper.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Jais.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[JAIS is the best bi-lingual Arabic-English conversational language model, curated to understand multiple Arabic dialects and cultural nuances.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[JAIS 是最棒的阿拉伯語-英語雙語對話語言模型，專為理解多種阿拉伯方言和文化細微差異而策展。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Meta.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet Llama 3.1 405B Instruct, Meta's next generation model, used for synthetic data generation and distillation of smaller models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[認識 Meta 的下一代模型 Llama 3.1 405B Instruct，其用於產生和提取小型模型綜合資料。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Meta.description2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet Meta's latest Llama 3.2 models, available now for image reasoning and SLM edge-mobile scenarios]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[認識 Meta 的最新 Llama 3.2 模型，現在可用於影像推理和 SLM 邊緣-行動裝置案例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Meta.description3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet Meta's latest groundbreaking models; Llama 3.2 11B Vision Instruct and 90B Vision Instruct models for image reasoning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 Meta 最新的突破性模型; 用於影像推理的 Llama 3.2 11B Vision Instruct 和 90B Vision Instruct 模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Meet Meta's latest grounding breaking models; Llama 3.2 11B Vision Instruct and 90B Vision Instruct models for image reasoning.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Mistral.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral AI's latest collection of large-language models include Mistral Small, Large (2402), Large (2407), Mistral-7B, Nemo, and Mixtral-8x22B.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral AI 的最新大型語言模型集合包括 Mistral Small、Large (2402)、Large (2407)、Mistral-7B、Nemo 和 Mixtral-8x22B。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Mistral AI’s latest collection of large-language models include Mistral Small, Large (2402), Large (2407), Mistral-7B, Nemo, and Mixtral-8x22B.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Nixtla.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TimeGEN-1 is a generative pre-trained forecasting and anomaly detection model for time series data, like sales, finance, weather and more.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TimeGEN-1 是生成式預先訓練的預測和異常偵測模型，適用於例如銷售、財務、天氣等時間序列資料。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Phi3.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New Phi-3 series of models offers the powerful performance and flexibility of small language models for every computing use cases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新的 Phi-3 系列模型為每個計算使用案例提供了小語言模型的強大效與彈性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.DownloadDemo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Link to demo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[示範連結]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.OpenInGithub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open in Github]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Github 中開啟]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.ReadAnnouncements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read announcements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[讀取公告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.ReadPaper" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read paper]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[閱讀文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.WatchVideo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Watch video]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[觀看影片]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraBodyVideoAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Megan Stanley, Senior Researcher at Microsoft Research AI for Science, discusses Aurora, a groundbreaking model for weather forecasting that could revolutionize predictions and mitigation of extreme events, air pollution, and climate change.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Research AI for Science 的資深研究員 Megan Stanley 討論 Aurora，這是一種突破性的天氣預測模型，可以徹底改變極端事件、空氣污染和氣候變遷的預測和緩解方式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora is a large-scale foundation model developed for atmospheric forecasting. By leveraging extensive atmospheric data, this model enhances our capacity to predict and mitigate the impacts of extreme weather events.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 是一個專為大氣預測而開發的大型基礎模型。透過利用廣泛的大氣資料，該模型增強了我們預測和減輕極端天氣事件影響的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora is a foundation model, built on vast amounts of atmospheric data, that can significantly improve our ability to predict extreme weather events. Explore how this innovative model can enhance weather forecasting.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 是一個建立在大量大氣資料上的基礎模型，可以大幅提升我們預測極端天氣事件的能力。探索這個創新模型如何增強天氣預測。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Aurora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Aurora]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeVideoDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Built on vast amounts of atmospheric data, this foundation model can significantly improve our ability to predict extreme weather events.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這個基礎模型以大量的大氣資料為基礎，可以顯著改善我們預測極端天氣事件的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora emerged from the recognition that traditional weather prediction models often fall short in capturing the rapid intensification and peak wind speeds that characterize extreme storms. Aurora’s innovative architecture has been trained on over a million hours of diverse weather and climate simulations, enabling it to excel in a broad spectrum of predictive tasks while achieving an impressive spatial resolution of 0.1° - approximately 11 km at the equator. This level of granularity enhances the accuracy of operational forecasts and confers an estimated computational speed advantage of around 5,000 times over conventional numerical weather-prediction systems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 源於對傳統天氣預測模型的認識，這些模型在捕捉極端風暴的快速增強和峰值風速方面往往表現不佳。Aurora 的創新結構經過超過一百萬小時的各種天氣和氣候模擬訓練，使其能夠在廣泛的預測工作中脫穎而出，同時達到令人印象深刻的 0.1° 空間解析度 - 在赤道約為 11 公里。這種細微度提高了作業預測的正確性，與傳統數值天氣預測系統相比，其運算速度估計可提升約 5,000 倍。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora’s capabilities extend beyond accuracy and efficiency; it showcases versatility in forecasting a variety of atmospheric variables, including temperature, wind speed, and air pollution levels. Built using a flexible 3D Swin Transformer architecture and incorporating Perceiver-based encoders and decoders, Aurora effectively processes heterogeneous input data and generates predictions across multiple resolutions. Utilizing extensive pretraining on diverse datasets and fine-tuning for specific tasks, Aurora discerns complex patterns in atmospheric data, often yielding noteworthy results even with limited training data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 的功能不僅限於正確性和效率，還能預測各種大氣變數，包括溫度、風速和空氣污染水平。Aurora 使用靈活的 3D Swin Transformer 結構，並結合了以 Perceiver 為基礎的編碼器和解碼器，能有效處理異質輸入資料，並在多個解析度上產生預測。Aurora 在不同的資料集上進行廣泛的預先訓練，並針對特定工作進行微調，從大氣資料中找出複雜的模式，即使訓練資料有限，也經常能產生顯著的結果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The significance of Aurora transcends performance metrics; its robust architecture and diverse pretraining illustrate how scale and data variety enhance atmospheric forecasting. By incorporating data from climate simulations, reanalysis products, and operational forecasts, Aurora builds a nuanced and generalizable understanding of atmospheric dynamics. Compared to leading specialized deep learning models, Aurora demonstrates the ability to surpass existing benchmarks, establishing it as a crucial tool for future environmental predictions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 的重要性超越了效能指標；其穩健的結構和多樣化的預先訓練展示了規模和資料的多樣性如何增強大氣預測。通過整合來自氣候模擬、重新分析產品和作業預測的資料，Aurora 建立了對大氣動態細微而廣泛的了解。與領先的專業深度學習模型相比，Aurora 展示了超越現有基準的能力，將其確立為未來環境預測的重要工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Aurora model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 Aurora 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu samples functionally distinct protein conformations. a) Large-scale domain motions such as opening/closing, rotation, and repacking. b) Local unfolding or unbinding of parts of the protein. c) Formation of cryptic binding pockets that are not present in the apo ground state.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu 取樣功能上不同的蛋白質構象。a) 大規模的域運動，例如開啟/關閉、旋轉和重新打包。b) 蛋白質部分的局部展開或解除結合。c) 形成在無配體基態中不存在的隱秘結合口袋。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu multi-conformation benchmark of local unfolding. For each case, the PDB structure used as folded state reference is shown in red, with the part can unfold highlighted. Energy landscapes show the empirical free energy sampled by the pre-trained (black) and fine-tuned (blue) model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu 多符合性基準的局部展開。針對每個案例，以紅色顯示作為折疊狀態參考的 PDB 結構，並突出顯示可展開的部分。能量景觀顯示預訓練模型 (黑色) 和微調 (藍色) 模型所取樣的經驗自由能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu-1 is a deep learning model that can generate thousands of protein structures per hour on a single graphics processing unit. It provides orders of magnitude greater computational efficiency compared to classical MD simulations, thereby opening the door to insights that have, until now, been out of reach.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu-1 是一種深度學習模型，能在單一圖形處理器上每小時產生數千種蛋白質結構。與傳統的分子動力學模擬相比，它提供了數量級更高的計算效率，從而開啟了以往無法獲得的深入見解之門。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A deep learning model that can generate thousands of protein structures per hour on a single GPU.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一種深度學習模型，能在單一圖形處理器上每小時產生數千種蛋白質結構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover BioEmu]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 BioEmu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From forming muscle fibers to protecting us from disease, proteins play an essential role in almost all biological processes in humans and other life forms alike. There has been extraordinary progress in recent years toward better understanding protein structures using deep learning, enabling the accurate prediction of protein structures from their amino acid sequences. However, predicting a single protein structure from its amino acid sequence is like looking at a single frame of a movie—it offers only a snapshot of a highly flexible molecule. Biomolecular Emulator-1 (BioEmu-1) is a deep-learning model that provides scientists with a glimpse into the rich world of different structures each protein can adopt, or structural ensembles, bringing us a step closer to understanding how proteins work. A deeper understanding of proteins enables us to design more effective drugs, as many medications work by influencing protein structures to boost their function or prevent them from causing harm.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從形成肌肉纖維到保護我們免受疾病的侵害，蛋白質在幾乎所有人類及其他生命形式的生物過程中扮演著重要角色。近年來，在利用深度學習更好地理解蛋白質結構方面取得了非凡的進展，這使得我們能夠根據氨基酸序列準確預測蛋白質結構。然而，從氨基酸序列預測單一蛋白質結構就像是觀看電影中的一個畫面——它僅提供了這種高度靈活分子的快照。生物分子模擬器-1 (BioEmu-1) 是一種深度學習模型，為科學家提供了不同蛋白質可以採用的豐富結構或結構集的洞察，讓我們更接近理解蛋白質的運作方式。對蛋白質的更深入理解使我們能夠設計出更有效的藥物，因為許多藥物的作用是通過影響蛋白質結構來增強其功能或防止其造成傷害。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu-1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu-1]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get BioEmu model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 BioEmu 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTryItOutGitHub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open BioEmu repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 BioEmu 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A scatter plot graph showing the average score of 11 benchmarks on the y-axis and memory footprint in gigabytes (GB) on the x-axis for various open-weight large language models (LLMs). The Pareto Frontier of Open-weight LLMs is indicated by a blue dashed line. Data points include Qwen2-5.3B, BitNet b1.58 2B (marked with a red star as an outlier with low memory footprint), Qwen2-5.1-5B, SmolLM2-1.7B, MiniCPM-2B, LLaMa-2-13B, Gemma-3-13B, and Qwen2-0.5-8B. The image shows a comparison of different large language models based on their performance and memory usage, highlighting which models are more efficient or powerful relative to their memory footprint. This is relevant for understanding trade-offs in model design and deployment efficiency in machine learning applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[散佈圖圖表的 y 軸顯示了 11 個基準的平均分數，而 x 軸則是各種開放權數大型語言模型 (LLM) 的磁碟使用量 (以 GB 為單位)。開放權數 LLM 的 Pareto 先驅由藍色虛線表示。資料點包括 Qwen2-5.3B、BitNet b1.58 2B (以紅色星號標示為磁碟使用量較低的極端值)、Qwen2-5.1-5B、SmolLM2-1.7B、MiniCPM-2B、LLaMa-2-13B、Gemma-3-13B 和 Qwen2-0.5-8B。該圖像會顯示根據其效能和記憶體使用量對不同大型語言模型進行的比較，並突顯出相對於其磁碟使用量，哪些模型更有效率或功能更強大。這對於理解機器學習應用程式中的模型設計與部署效率的權衡非常重要。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research, BitNet b1.58 2B4T is the first open-source, native 1-bit large language model (LLM) in which every parameter is ternary (i.e., -1, 0, 1), at a 2-billion parameter scale. Trained on a corpus of 4 trillion tokens, this model demonstrates that native 1-bit LLMs can achieve performance comparable to leading open-weight, full-precision models of similar size, while offering substantial advantages in computational efficiency, including substantially reduced memory footprint, energy consumption, and decoding latency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 Microsoft Research 開發的 BitNet b1.58 2B4T 是第一個開放原始碼的原生 1 位元大型語言模型 (LLM)，其每個參數均為三元 (即 -1、0、1)，參數規模達到 20 億。該模型在 4 兆個權杖的主體上進行訓練，顯示出原生 1 位元 LLM 達到的效能可與類似規模的開放權數全精確度模型相媲美，同時在計算效率方面提供了顯著的優勢，包括大幅降低磁碟使用量、能源使用量和解碼延遲。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research, BitNet b1.58 2B4T is the first open-source, native 1-bit large language model (LLM) at a 2-billion parameter scale.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 Microsoft Research 開發的 BitNet b1.58 2B4T 是第一個開放原始碼的原生 1 位元大型語言模型 (LLM)，參數規模達到 20 億。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about BitNet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 BitNet]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft researchers have demonstrated that BitNet b1.58 2B4T achieves performance on par with leading open-weight, full-precision LLMs of similar size, while offering significant advantages in computational efficiency, including substantially reduced memory footprint, energy consumption, and decoding latency. To facilitate further research and adoption, the model weights have been released along with open-source inference implementations for both GPU and CPU architectures.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 研究人員已證明，BitNet b1.58 2B4T 達到的效能與相似規模的領先開放權數全精確度 LLM 旗鼓相當，同時在計算效率方面提供了顯著的優勢，包括大幅降低磁碟使用量、能源使用量和解碼延遲。為了促進進一步的研究和採用，模型權數已與針對 GPU 和 CPU 架構的開放原始碼推斷實作一起發布。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BitNet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open BitNet repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 BitNet 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BitNet has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet 供研究用途發行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Large Language Models (LLMs) are widely used for coding, but they primarily operate coding tasks in a "static" (i.e., single-turn) manner, reflecting the type of data they have been trained on. We propose that LLMs could perform better by interactively exploring codebases. To support this, we introduce debug-gym—a lightweight textual environment with tools like pdb—that enables LLM agents to debug and generate code more effectively. This approach can also extend to other tasks that benefit from active information gathering.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大型語言模型 (LLM) 廣泛用於編碼，但主要以「靜態」方式執行編碼工作 (例如單一轉換)，反映了它們所訓練的資料類型。我們建議透過互動方式探索程式碼基底，以提升 LLM 效能。為支援這一點，我們推出了 debug-gym——輕量級的文本環境，配備像 pdb 這樣的工具，使 LLM 代理程式能更有效地進行偵錯並產生程式碼。這種方法也可以擴展到其他受益於主動收集資訊的工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Debug-gym is an environment for training LLMs to debug software more like humans—interactively and by leveraging tools—taking a big step toward more capable, collaborative AI-assisted programming.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Debug-gym 是訓練 LLM 的環境，旨在讓以更像人類的方式進行軟體偵錯 (透過互動方式並利用各種工具)，朝向功能更強大的共同作業式 AI 輔助程式設計邁出重要的一步。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try Debug-gym]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[試用 Debug-gym]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI coding tools are rapidly evolving, but their biggest limitation remains debugging—an essential and time-consuming part of software development. Debug-gym, a new open-source research environment from Microsoft, is designed to change that. It helps AI agents learn to debug code the way human developers do: interactively, iteratively, and with the right tools.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 編碼工具正在快速進化，但其最大限制仍然是偵錯——這是軟體開發中必要且耗時的一部分。Debug-gym 是來自 Microsoft 的新開放原始碼研究環境，旨在改變這一情況。它可協助 AI 代理程式以人類開發人員的方式進行偵錯: 互動式、反覆進行，並使用正確的工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike current AI systems that passively suggest fixes based on error messages, debug-gym teaches agents to access tools. For example, access to Python's pdb debugger, enables AI coding agents to set breakpoints, inspect variable values, navigate large codebases, and even write custom tests.  Debug-gym includes three coding benchmarks to measure LLM-based agents' performance in interactive debugging: Aider for simple function-level code generation, Mini-nightmare for short, hand-crafted buggy code examples, and SWE-bench for real-world coding problems requiring a comprehensive understanding of a large codebase and a solution in the format of a GitHub pull request. Integration with swe-smith is coming soon. Early experiments show promising improvements in agents' performance when they are given access to tools, especially on complex, real-world coding tasks. Microsoft invites researchers and developers to explore this next frontier in AI-assisted programming.  Try debug-gym and build smarter, more useful AI tools that don't just generate code, but also improve it.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與目前根據錯誤訊息被動建議修正的 AI 系統不同，Debug-gym 教導代理程式存取工具的方式。例如，存取 Python 的 pdb 偵錯程式，可讓 AI 編碼代理程式設定斷點、檢查變數值、瀏覽大型程式碼基底，甚至撰寫自訂測試。 Debug-gym 包含三個編碼基準，以測量基於 LLM 的代理程式在互動式偵錯中的效能: Aider 用於簡單函數層級的程式碼產生，Mini-nightmare 用於短小的手工製作錯誤程式碼範例，而 SWE-bench 用於需要全面理解大型程式碼基底並以 GitHub 提取要求格式提供解決方案的真實世界編碼問題。與 swe-smith 的整合即將推出。早期實驗顯示，當代理程式獲得工具存取權時，其效能有顯著的改善，特別是在複雜的真實世界編碼工作上。Microsoft 邀請研究人員和開發人員探索這個 AI 協助程式設計的下一個前沿。 試試 Debug-gym，打造更聰明、更實用的 AI 工具，其不僅能產生程式碼，還能改進程式碼。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Debug-gym]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Debug-gym]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try debug-gym]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[試用 Debug-gym]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An animated sequence showing how a chain of amino acids folds into a protein’s three-dimensional structure using the natural sequences predicted by EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一個動畫序列，顯示胺基酸鏈如何使用 EvoDiff 預測的自然序列摺疊成蛋白質的三維結構]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff is a general-purpose diffusion framework that combines evolutionary-scale data with the distinct conditioning capabilities of diffusion models for controllable protein generation in sequence space. EvoDiff generates high-fidelity, diverse, and structurally-plausible proteins that cover natural sequence and functional space.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff 是一項一般用途擴散架構，其將進化規模的資料與擴散模型的相異調節功能結合，以在序列空間中獲得可控制的蛋白質產生。EvoDiff 會產生高逼真度、多樣化且結構上貌似真實的蛋白質，涵蓋自然序列和功能空間。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A general-purpose diffusion framework for controllable protein generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用於可控制蛋白質產生的一般用途擴散架構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 EvoDiff]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Critically, EvoDiff can generate proteins inaccessible to structure-based models, such as those with disordered regions, while maintaining the ability to design scaffolds for functional structural motifs, demonstrating the universality of our sequence-based formulation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關鍵來說，EvoDiff 可以產生無法供結構型模型模型 (例如有失序區域的模型) 使用的蛋白質，同時維持設計功能性結構模體支架的能力，展現我們以序列為基礎的配方的普遍性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff will expand capabilities in protein engineering beyond the structure-function paradigm toward programmable, sequence-first design. The sequence and MSA models – EvoDiff-Seq and EvoDiff-MSA, respectively – were evaluated across a range of generation tasks to demonstrate their power for controllable protein design.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff 將蛋白質工程中的功能擴大，超出結構-功能的典範，成為可程式化、序列優先的設計。序列和 MSA 模型 (分別為 EvoDiff-Seq 和 EvoDiff-MSA) 已跨一系列產生工作進行評估，以展現其對於可控制的蛋白質設計的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTryItOutAzureAI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get EvoDiff model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 EvoDiff 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTryItOutGithub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open EvoDiff repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 EvoDiff 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* A gradient blue to green background features a white flowchart with rectangular boxes connected by arrows, ending in a hexagonal “STOP” sign and a check mark on the right side.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*裝飾* 藍到綠色漸層背景上，展示了一個白色流程圖，矩形方塊由箭頭連接，結尾為六邊形的「停止」符號，右側有一個核取記號。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT 是一種教導 AI 代理程式更有效率地探索的方法，讓他們能夠智慧地瀏覽其環境、收集有價值的資訊、評估選項，並找出最佳的決策制定與規劃策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Teach AI agents to explore more effectively.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[教導 AI 代理程式更有效地探索。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT 是一種教導 AI 代理程式更有效率地探索的方法，讓他們能夠智慧地瀏覽其環境、收集有價值的資訊、評估選項，並找出最佳的決策制定與規劃策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover ExACT]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 ExACT]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Autonomous AI agents are transforming the way we approach multi-step decision-making processes, streamlining tasks like web browsing, video editing, and file management. By applying advanced machine learning, they automate workflows, optimize performance, and reduce the need for human input.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自主 AI 代理程式正在改變我們處理多步驟決策流程的方式，簡化網頁瀏覽、影片編輯和檔案管理等工作。透過應用進階的機器學習，它們可自動化工作流程、最佳化效能，並減少對人類輸入的需求。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[However, these systems struggle in complex, dynamic environments. A key challenge lies in balancing exploitation, using known strategies for immediate gains, with exploration, which involves seeking new strategies that could yield long-term benefits. Additionally, they often have difficulty adapting to unpredictable changes in conditions and objectives, as well as generalizing knowledge across contexts, limiting their ability to transfer learned strategies between domains.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[然而，這些系統在複雜的動態環境中會遭遇挑戰。一個關鍵的挑戰在於開發 (利用已知策略來獲取即時收益) 與探索 (尋求可以帶來長期利益的新策略) 的平衡。此外，它們通常難以適應條件和目標無法預測的變更，也難以在不同環境中概括知識，從而限制了他們在各個領域之間轉移所學策略的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In response, Microsoft researchers have developed ExACT, an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為此，Microsoft 的研究人員開發了 ExACT，一種教導 AI 代理程式更有效率地探索的方法，讓他們能夠智慧地瀏覽其環境、收集有價值的資訊、評估選項，並找出最佳的決策制定與規劃策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[精確]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open ExACT repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 ExACT 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The MCP Server for Azure AI Foundry Labs is designed to supercharge team velocity in adopting and evaluating breakthrough AI research. By equipping GitHub Copilot with custom tools for intelligent model discovery, tailored implementation guidance, and rapid prototyping, this can achieve exponential productivity gains and reduce the “idea-to-prototype” cycle to under 10 minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於 Azure AI Foundry Labs 的 MCP 伺服器的設計目的是要促進小組採用和評估創新 AI 研究的速度。透過為 GitHub Copilot 提供智慧型模型探索的自訂工具、量身打造的實作指導和快速的原型建立，這可達成指數型生產力提升，並將「構想至原型」週期縮短到 10 分鐘以內。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Integrate, Prototype, and Accelerate AI Model Experimentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[整合、建立原型並加速 AI 模型實驗]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about the MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 MCP 伺服器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Unified Model Discovery:</strong> Instantly list 45+ models (Microsoft Research, OpenAI, Meta, Mistral, and Azure Foundry Labs specialties) inside your coding environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>整合模型探索:</strong> 在您的程式碼編寫環境中快速列出超過 45 個模型 (Microsoft Research、OpenAI、Meta、Mistral 和 Azure Foundry Labs 專長)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Implementation Guidance On Demand:</strong> GitHub Copilot receives detailed integration documentation and usage hints for each model—reducing hallucination and speeding up “from idea to working code."]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>隨選實作指導:</strong> GitHub Copilot 會收到每個模型的詳細整合文件和使用提示，藉以減少幻覺並加速實現「從構想到工作程式碼」。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Seamless Copilot Integration:</strong> GitHub Copilot is enhanced via MCP servers to understand model endpoints, available tools, and recommend best-fit models for your use case.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>無縫 Copilot 整合:</strong> GitHub Copilot 透過 MCP 伺服器增強，可了解模型端點、可用的工具，並建議最適合您的使用案例的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Generative Coding Without the Chaos:</strong> “Prototyping without the rabbit holes.” The MCP Server constrains and guides the AI, avoiding runaway file generation, dead ends, or coding spirals typical of other agentic tools.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>沒有混亂的生成式程式碼編寫:</strong>「建立原型而不遇到複雜的情況」。MCP 伺服器會約束和引導 AI，避免檔案產生失控、困境，或其他代理程式工具的程式碼編寫螺旋典型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Prototyping at Lightning Speed:</strong> Build evaluators, dashboards, analyzers, and bespoke AI apps in minutes. Typical initial working apps are generated in <10 minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>光速建立原型:</strong> 在數分鐘內建置評估工具、儀表板、分析程式，以及客製化 AI 應用程式。一般的初始工作應用程式會在 10 分鐘內產生。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MCP 伺服器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get started with the MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始使用 MCP 伺服器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Skala functional will enable more accurate, scalable predictions in computational chemistry. It starts with the largest high-accuracy dataset ever built for training deep-learning-based density functional theory (DFT) models. This dataset underpins Skala—coming soon to the Azure AI Foundry catalog—a new machine-learned exchange-correlation functional that reaches experimental accuracy for atomization energies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skala 泛函將在計算化學中實現更精確且可擴展的預測。它始於有史以來最大規模、用於訓練深度學習密度泛函理論 (DFT) 模型的高準確度資料集。這個資料集是 Skala 的基礎，這是一個即將在 Azure AI Foundry 目錄推出的新型機器學習交換相互關聯泛函，能在原子化能量的預測上達到實驗級準確度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the largest high-accuracy dataset for training deep-learning-based models for density functional theory (DFT). It enables a leap forward in predictive chemistry and supports the development of Skala, a new DFT functional from Microsoft Research that has achieved a breakthrough in accuracy for this workhorse method that thousands of scientists use every year to simulate matter at the atomistic level.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是目前規模最大、專為訓練基於深度學習的密度泛函理論 (DFT) 模型而建立的高準確度資料集。它推動了預測化學的重大進展，並支援 Skala 的開發，這是 Microsoft Research 推出的新型 DFT 泛函，在這項每年有數千位科學家用於原子層級物質模擬的核心方法中，實現了前所未有的準確度突破。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore MSR-ACC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 MSR-ACC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCShortenedTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MSR-ACC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MSR-ACC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This research marks a major advance in computational chemistry by dramatically improving the accuracy of <em>density functional theory</em> (DFT)—the most widely used method for simulating materials and molecules. The core breakthrough is a new deep-learning-based exchange-correlation (XC) functional, called Skala, which achieves <em>experimental-level accuracy</em> in predicting molecular properties like atomization energy—something previously thought out of reach for DFT. Skala will be available in the Azure AI Foundry catalog in the future. Researchers have released a large part of this dataset to the scientific community.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這項研究在計算化學領域取得重大突破，大幅提升了<em>密度泛函理論</em> (DFT) 的準確度，這是目前最廣泛用於模擬材料與分子的計算方法。這項核心突破是名為 Skala 的新型深度學習交換相互關聯 (XC) 泛函，它在預測原子化能等分子性質方面達到<em>實驗級準確度</em>，這在過去被認為是 DFT 難以達到的目標。Skala 將於未來在 Azure AI Foundry 目錄中推出。研究人員已將這個資料集的大部分內容公開釋出給科學社群。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DFT is fast but limited by manual approximations of the XC functional. The research team addressed that limitation by generating the largest high-accuracy dataset of molecular energies to date, leveraging first principles methods and cloud-scale computation. They then trained Skala to learn directly from electron densities, bypassing hand-crafted feature engineering that has stalled progress for decades.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DFT 雖然速度快，但受到 XC 泛函手動近似的限制。研究團隊利用第一原理方法和雲端計算產生了迄今為止最大的分子能量高精度資料集，解決了這個限制。他們隨後訓練 Skala 直接從電子密度中學習，跳過了數十年來阻礙進展的手工特徵工程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This achievement removes a long-standing barrier in computational chemistry, enabling DFT to shift from interpreting experimental results to predicting them reliably. That unlocks enormous potential across domains—from drug design to battery development—where accurate, affordable simulations can replace costly lab work.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這項成就打破了計算化學領域長久以來的障礙，讓 DFT 得以從解釋實驗結果，轉變為可靠地預測實驗結果。這將釋放橫跨多個領域的巨大潛力 (從藥物設計到電池開發)，在這些領域中精確且低成本的模擬有望取代昂貴的實驗室工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Research Accurate Chemistry Collection (MSR-ACC)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Research Accurate Chemistry Collection (MSR-ACC)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Diagram with five items (‘Orchestrator,’ ‘Coder,’ ‘FileSurfer,’ ‘WebSurfer,’ ‘ComputerTerminal’) connected to a single point.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[圖表中有五個項目(‘Orchestrator’，‘Coder’，‘FileSurfer’，‘WebSurfer’，‘ComputerTerminal’) 連接到單一點。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One is a generalist multi-agent system created to address intricate web and file-based tasks. By utilizing an intelligent Orchestrator alongside specialized agents, it facilitates the automation of complex, multi-step activities across various environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 是一個通用的多代理程式系統，旨在處理複雜的網頁和檔案工作。透過利用智慧型 Orchestrator 和專門的代理程式，它能夠自動化各種環境中的複雜多步驟活動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One is a multi-agent system designed to navigate complex tasks across diverse domains. Discover how intelligent agents could operate autonomously to enhance workflow efficiency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 是一個多代理程式系統，旨在跨越不同領域瀏覽複雜工作。探索智慧型代理程式如何自主運作以提升工作流程效率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One introduces advancements in agentic AI through its modular architecture, which features a lead agent termed the Orchestrator. This component manages a network of specialized agents, enabling each to concentrate on specific tasks, such as web navigation, code execution, or local file management. This structure supports the efficient pursuit of complex objectives.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 透過其模組化架構引入了代理 AI 的進步，其特點是具有稱為 Orchestrator 的主導代理程式。這個元件會管理專門代理程式的網絡，使每個代理程式能夠專注於特定工作，例如網頁瀏覽、程式碼執行或本機檔案管理。此結構支援有效率地實現複雜的目標。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Central to Magentic-One’s operation are its dual planning mechanisms: the Task Ledger and the Progress Ledger. The Task Ledger empowers the Orchestrator to formulate strategic approaches, while the Progress Ledger provides real-time updates on task statuses. This interconnected system allows for ongoing evaluation and adjustment, optimizing overall efficiency. In situations where obstacles arise, the Orchestrator can adapt plans and reallocate tasks, ensuring effective workflow management under varying conditions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 作業的核心是雙規劃機制：工作總帳和進度總帳。工作總帳使 Orchestrator 能夠制定策略性方法，而進度總賬則提供工作狀態的即時更新。這種互聯系統可進行持續評估和調整，從而最佳化整體效率。在遇到障礙的情況下，Orchestrator 可以調整計劃並重新分配工作，確保在不同條件下進行有效的工作流程管理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The image shows a bar chart comparing accuracy percentages across different systems on the medium subset of the GAIA benchmark.: Magentic-One (about 33%), Webby autonomous (about 38%), Webby + Simulated Human (about 58%), and Human (about 90%).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像顯示橫條圖，比較 GAIA 效能評定的中子集上不同系統的正確性百分比。: Magentic-One (約 33%)、Webby 自主 (約 38%)、Webby + 模擬人類 (約 58%) 和人類 (約 90%)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlocking the full potential of AI requires the development of effective mechanisms for human-AI collaboration. By reducing cognitive load while ensuring users remain in control, AI can significantly enhance human capabilities and streamline complex workflows. Magentic-UI was designed with this goal in mind, serving as a research platform aimed at advancing research on human-in-the-loop experiences.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要充分發掘 AI 的潛力，需要開發人類與 AI 共同作業的有效機制。透過降低認知負載，同時確保使用者保持在控制，AI 能大幅增強人類能力並簡化複雜的工作流程。Magentic-UI 的設計正是基於此目標，作為研究平台，旨在推動有關「人類參與循環」(HITL) 體驗的研究。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI is an open-source experimental platform to accelerate progress in human-agent collaboration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 是一項開放原始碼實驗平台，可加速人類與代理程式共同作業的進度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about Magentic-UI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 Magentic-UI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI builds on  Magentic-One, a generalist multi-agent system that specializes in complex web and file-based tasks, and is powered by {AutoGenLink}, our leading agent framework.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 以 Magentic-One 為建置基礎，這是一種跨領域的多代理程式系統，專精於複雜的 Web 和檔案型工作，並且由我們領先的代理程式架構 {AutoGenLink} 提供。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key features of the Magentic-UI research include:<ol><li><strong>Co-planning.</strong> Magentic-UI allows users to directly modify its plan through a plan editor or by providing textual feedback before Magentic-UI executes any actions.</li><li><strong>Co-tasking.</strong> Users can pause the system and give feedback in natural language or demonstrate it by directly taking control of the browser.</li><li><strong>Action guards.</strong> Magentic-UI seeks user approval before executing potentially irreversible actions, and the user can specify how often Magentic-UI needs approvals. Furthermore, Magentic-UI is sandboxed for the safe operation of tools such as browsers and code executors.</li><li><strong>Task learning.</strong> Magentic-UI can learn and save plans from previous interactions to improve task completion for future tasks.</li></ol>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 研究的主要功能包括:<ol><li><strong>共同規劃。</strong>Magentic-UI 可讓使用者在 Magentic-UI 執行任何動作之前，透過計劃編輯器直接修改其計劃或提供文字意見反應。</li><li><strong>共同作業。</strong>使用者可以暫停系統，並以自然語言提供意見反應，或直接控制瀏覽器來示範。</li><li><strong>動作保護。</strong>Magentic-UI 會先尋求使用者核准，再執行可能無法復原的動作，且使用者可以指定 Magentic-UI 需要核准的頻率。此外，Magentic-UI 已沙盒化，可進行瀏覽器和程式碼執行程式等工具的安全作業。</li><li><strong>工作學習。</strong>Magentic-UI 可以從先前的互動中學習並儲存計劃，以改善未來工作的工作完成。</li></ol>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUITitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUITryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Magentic-UI repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 Magentic-UI 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A mechanical employee operating a large green machine in an industrial setting. He’s using a control panel with his right hand and holding a tablet in the other.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機械員工在工業環境中操作一台大型綠色機器。他用右手操作控制台，而左手則持有平板電腦。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Internal wiring and components of a device with green check marks and orange circles indicating areas of interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有綠色複選標記和橙色圓形之裝置的內部線路和元件，指出感興趣的區域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in digital and physical environments. Magma builds on the foundation models paradigm that pretraining on a larger amount of more diverse datasets allows these models to generalize better to new tasks and environments. Magma can perceive visual and textual inputs and generate actions, whether it’s clicking a button in a user interface or grabbing a tool in the real world. This new model represents a significant step towards AI agents that can serve as general-purpose assistants.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma 是一個多模態的基礎模型，可在數位與實體環境中理解與行動。Magma 建立在基礎模型範式之上，在大量更多樣化的資料集上進行預先訓練可以使這些模型更好地推廣到新工作和新環境。Magma 能夠感知視覺和文本輸入並生成動作，無論是點擊使用者介面中的按鈕，還是抓取現實世界中的工具。這個新模型代表了 AI 代理程式朝著可作為通用助理的方向邁出了重要一步。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A multimodal AI foundation model designed to both understand and act in digital and physical environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多模態 AI 基礎模型，可在數位與實體環境中理解與行動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in digital and physical environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma 是一個多模態的基礎模型，可在數位與實體環境中理解與行動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Magma]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Magma]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Imagine an AI assistant that can book a meeting online and also set up the room for it – navigating software menus as effortlessly as it moves physical objects. Such seamless integration of digital and physical tasks has long been a sci-fi vision. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[想像一下，AI 助理可以線上預約會議，還能為會議安排房間 - 輕鬆瀏覽軟體功能表，就像移動實體物件一樣。這種數位和實體工作的無縫整合，長久以來一直是科幻的願景。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft researchers are bringing it closer to reality with Magma, a multimodal AI foundation model designed to both understand and act in digital and physical environments. Magma builds on the foundation models paradigm, that pretraining on a larger amount of more diverse datasets allows these models to generalize better to new tasks and environments. Magma can perceive visual and textual inputs and generate actions, whether it’s clicking a button in a user interface or grabbing a tool in the real world. This new model represents a significant step towards AI agents that can serve as general-purpose assistants. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 研究人員正藉由 Magma 將其帶入現實中，Magma 是一種多模式 AI 基礎模型，旨在數位與實體環境中理解與行動。Magma 建立在基礎模型範式之上，在大量更多樣化的資料集上進行預先訓練可以使這些模型更好地推廣到新工作和新環境。Magma 能夠感知視覺和文本輸入並生成動作，無論是點擊使用者介面中的按鈕，還是抓取現實世界中的工具。這個新模型代表了 AI 代理程式朝著可作為通用助理的方向邁出了重要一步。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Vision-Language-Action (VLA) models are typically pretrained on large amounts of vision-language-action datasets to obtain the vision-language understanding ability (verbal intelligence) and the ability to perceive and interact with the visual spatial world to perform a wide range of tasks (spatial intelligence). However, due to the dramatic difference among various digital and physical environments, separate VLA models are trained and used for different environments. These models cannot easily generalize to new tasks and new environments that are unseen in training data. Moreover, most of these models do not leverage pretrained vision-language (VL) models or diverse vision-language datasets. As a result, their vision language understanding ability is often inferior to state-of-the-art VL models, which further limits model generalizability.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視覺-語言-行動 (VLA) 模型通常在大量的視覺-語言-行動資料集上進行預先訓練，以獲得視覺-語言理解能力 (語言情報)，和感知及與視覺空間世界互動的能力 (空間情報)，以執行各種工作。然而，由於各種數位和實體環境之間的顯著差異，會針對不同環境會訓練和使用不同的 VLA 模型。這些模型無法輕鬆推廣到訓練資料中未見的新工作和新環境。此外，這些模型大多不會利用預先訓練的視覺-語言 (VL) 模型或多樣的視覺-語言資料集。因此，它們的視覺語言理解能力通常不如最先進的 VL 模型，這進一步限制了模型的通用性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma, is a VLA foundation model that can adapt to downstream (unseen) agentic tasks in both the digital and physical environments. With Magma, researchers showed that it is beneficial to pretrain a single VLA model for AI agents across these environments while still achieving state-of-the-art results on UI navigation and robotic manipulation tasks, outperforming previous models that are tailored specifically to these tasks. On VL tasks, Magma also compares favorably to popular VL models that are trained on much larger datasets.  ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma 是一個 VLA 基礎模型，能夠適應數位和實體環境中的下游 (未見) 代理程式工作。研究人員利用 Magma 證明了，在這些環境中為 AI 代理預先訓練單個 VLA 模型是有益的，同時仍然能夠在 UI 瀏覽和機器人操作工作上取得最先進的結果，超越專為這些工作量身打造的舊模型。在 VL 工作上，Magma 也比在更大的資料集上訓練的流行 VL 模型更具優勢。  ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Magma model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 Magma 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Various molecular structures and crystal lattices displayed in a grid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[網格中顯示的各種分子結構和晶格。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim is a deep learning model for accurate and efficient materials simulation and property prediction over a broad range of elements, temperatures and pressures to enable in silico materials design.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 是一種深度學習模型，能夠在廣泛的元素、溫度和壓力範圍內進行精確且高效的材料模擬和屬性預測，從而實現矽庫材料設計。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An AI-driven innovation transforming how we create and understand new materials, starting with accurate and efficient simulations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 驅動的創新正在改變我們創造和理解新材料的方式，從精確且高效的模擬開始。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore MatterSim]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 MatterSim]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim employs deep learning to understand atomic interactions from the very fundamental principles of quantum mechanics, across a comprehensive spectrum of elements and conditions—from 0 to 5,000 Kelvin (K), and from standard atmospheric pressure to 10,000,000 atmospheres.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 運用深度學習，從量子力學的基本原則理解原子互動，涵蓋從 0 到 5,000 開爾文 (K) 以及從標準大氣壓力到 10,000,000 大氣壓力範圍的各種元素和條件。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim efficiently handles simulations for a variety of materials, including metals, oxides, sulfides, halides, and their various states such as crystals, amorphous solids, and liquids. Additionally, it offers customization options for intricate prediction tasks by incorporating user-provided data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 有效率地處理各種材料的模擬，包括金屬、氧化物、硫化物、鹵化物及其各種狀態，如晶體、非晶固體和液體。此外，它還通過整合使用者提供的資料，為複雜的預測工作提供自訂選項。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get MatterSim model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 MatterSim 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research in collaboration with game studio Ninja Theory, Muse is a World and Human Action Model (WHAM) - a generative AI model of a video game that can generate game visuals, controller actions, or both. Trained exclusively on the game Bleeding Edge, researchers and game creatives can explore how these model capabilities will have potential to accelerate their creativity in the future.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Muse 由 Microsoft Research 與遊戲工作室 Ninja Theory 合作開發，是世界與人類行爲模型 (WHAM) - 一種可以生成遊戲視覺效果、控制器動作或兩者兼而有之的影片遊戲生成式 AI 模型。該模型專門在遊戲 Bleeding Edge 上進行訓練，研究人員和遊戲創意者可以探索這些模型的能力未來如何加速他們的創意。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseDownloadDemo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download demonstrator app]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載示範應用程式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A generative AI model that can generate visuals and controller actions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一個生成式 AI 模型，能夠生成視覺效果和控制器動作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empowering game creatives with generative AI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過生成式 AI 增強遊戲創作能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Muse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Muse]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse was trained on more than 1 billion images and controller actions, from the game Bleeding Edge, corresponding to over 7 years of continuous human gameplay.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Muse 已經在遊戲 Bleeding Edge 上進行了超過 10 億個影像和控制器動作的訓練，這相當於超過 7 年的連續人類遊戲。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The WHAM demonstrator app provides a visual interface for interacting with a deployment of the Muse model instance on Azure AI Foundry. Creators can load a screenshot from Bleeding Edge as an initial prompt, then use the model to generate multiple potential continuations of gameplay from this starting point. They can then explore the generated sequences and tweak them, such as changing the controller inputs or pasting game elements into the scene and predicting what will happen as a result. These features demonstrate how Muse’s capabilities could someday enable AI-supported iteration and brainstorming as part of the creative process.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[WHAM 示範應用程式提供了一個視覺化介面，用於與 Azure AI Foundry 上 Muse 模型執行個體的部署互動。建立者可以從 Bleeding Edge 載入螢幕擷取畫面作為初始提示，然後再使用模型從這個起點產生多個潛在的遊戲延續。然後，他們可以探索生成的序列並進行調整，例如變更控制器輸入或將遊戲元素貼到場景中，並預測結果。這些功能展示了 Muse 的能力未來如何能夠讓 AI 支援的反覆運算和腦力激蕩，成為創意流程的一部分。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Muse]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Muse model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 Muse 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Focus on the grid of 9 AI generated gaming video frames by the Muse model set in the same scene. On the left, showing 3 ground truth (original) scenes that are compared to the AI generated frames.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*裝飾* 將焦點放在由在相同場景中設定的 Muse 模型產生的 9 個 AI 生成遊戲影片畫面方格。左邊顯示 3 個基準真相 (原始) 場景，與 AI 生成的畫面進行比較。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by the Muse model set in the same scene. The frames highlight differences when the game player takes a left, center, or right path.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*裝飾* 由在相同場景中設定的 Muse 模型產生的 9 個 AI 生成遊戲影片畫面方格。當玩家選擇左、中或右路徑時，畫面會突出顯示差異。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by the Muse model crafted in the creator tool. This game creator surface showcases and Xbox controller that can be used to impact the AI generate frames.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*裝飾* 由在建立者工具中製作的 Muse 模型產生的 9 個 AI 生成遊戲影片畫面方格。此遊戲建立者界面展示了 Xbox 控制器，可用來影響 AI 生成的框架。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Navigation.NextArticleText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下一個]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Navigation.PreviousArticleText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Previous]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上一個]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhancing the ability of coding models to handle diverse editing requirements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增強編碼模型處理各種編輯需求的能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhancing the ability of coding models to handle diverse editing requirements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增強編碼模型處理各種編輯需求的能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover NextCoder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Software engineering activities frequently involve edits to existing code. However, contemporary code language models lack the ability to handle diverse types of code-edit requirements. In this work, Microsoft researchers attempt to overcome this shortcoming through a novel synthetic data generation pipeline and a robust model adaptation algorithm. Starting with seed code examples and diverse editing criteria, their pipeline generates high-quality samples comprising original and modified code, along with natural language instructions in different styles and verbosity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[軟體工程活動經常涉及現有程式碼的編輯。不過，當代的程式碼語言模型無法處理各種類型的程式碼編輯需求。在此工作中，Microsoft 研究人員嘗試透過新穎的綜合資料產生管線和強大的模型調適演算法來克服這個缺點。從種子程式碼範例和各種編輯準則開始，其管線會產生高品質的範例，包含原始和修改過的程式碼，以及不同樣式和詳細程度的自然語言指示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Today’s code LMs come bundled with strong abilities, such as code generation and instruction following, which should not be lost due to fine-tuning. To ensure this, researchers proposed a novel adaptation algorithm, SeleKT, that (a) leverages a dense gradient-based step to identify the weights that are most important for code editing, and (b) does a sparse projection onto the base model to avoid overfitting.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[現今的程式碼 LM 結合了強大的能力 (例如程式碼產生和遵循指示)，這不應該因為微調而遺失。為了確保這點，研究人員提議了新穎的調適演算法 (SeleKT)，該演算法 (a) 利用密集梯度型步驟來識別程式碼編輯最重要的權數，以及 (b) 對基本模型進行稀疏投影以避免過度學習。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Using this approach, researchers obtained a new series of models called NextCoder (adapted from QwenCoder-2.5) that achieves strong results on five code-editing benchmarks, outperforming comparable size models and even several larger ones. In their research paper, they demonstrate the generality of their approach on two model families (DeepSeekCoder and QwenCoder), compare against other fine-tuning approaches, and demonstrate robustness by showing retention of code generation and general problem-solving abilities post adaptation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用這個方法，研究人員取得了一系列名為 NextCoder (從 QwenCoder-2.5 調適) 的新模型，在五個程式碼編輯基準上達成豐碩的結果，其表現優於同等規模的模型，甚至優於數個更大的模型。在其研究論文中，他們透過兩個模型系列 (DeepSeekCoder 和 QwenCoder) 展現其方法的普遍性、與其他微調方法比較，以及藉由顯示調適後程式碼產生的保留和一般問題解決能力來展現實力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are now available for experimental purposes on Azure AI Foundry.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這些模型現在可在 Azure AI Foundry 上用於實驗用途。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NextCoder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try NextCoder in Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Foundry 中試用 NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Parsed Teams screenshot image by OmniParser.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 OmniParser 剖析的 Teams 螢幕擷取畫面影像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is an advanced vision-based screen parsing module that converts user interface (UI) screenshots into structured elements, allowing agents to execute actions across various applications using visual data . By harnessing large vision-language model capabilities, OmniParser improves both efficiency and accuracy in UI interactions. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 是一個進階的視覺型螢幕解析模組，可將使用者介面 (UI) 螢幕擷取畫面轉換為結構化元素，讓代理程式可使用視覺效果資料在各種應用程式中執行動作。透過利用大型視覺-語言模型的能力，OmniParser 提高了 UI 互動的效率和正確性。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Turn any LLM into a computer use agent ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將任何 LLM 轉換為電腦使用代理程式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is a pioneering screen parsing module that transforms user interfaces into actionable elements through visual input. Discover how this innovative approach can enhance automated UI interactions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 是一個開創性的螢幕解析模組，通過視覺輸入將使用者介面轉換為可採取動作的元素。探索這種創新方法如何增強自動化的使用者介面互動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore OmniParser V2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 OmniParser V2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recent developments in large vision-language models (VLMs), such as GPT-4V and GPT-4o, showcase their potential in creating agent systems that integrate smoothly within user interfaces. However, the practical application of these multimodal models, especially as general agents across different operating systems, faces challenges. A significant barrier to progress has been the absence of reliable screen parsing techniques that can effectively identify interactable icons and link intended actions to specific screen regions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大型視覺-語言模型 (VLM) 的最新發展，例如 GPT-4V 和 GPT-4o，展示了其在建立可順利整合至使用者介面的代理程式系統方面的潛力。然而，這些多模態模型的實際應用，特別是作為不同作業系統中的通用代理程式，則面臨許多挑戰。進展的一大障礙是缺乏可靠的螢幕解析技術，無法有效識別可互動的圖示，並將預期的動作連結到特定螢幕區域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser addresses this limitation through its compact and powerful architecture. It transforms UI screenshots into structured output elements, enabling the design of agents that can perform precise actions across various applications. When combined with models like GPT-4V, OmniParser markedly improves the agent's capability to engage accurately with user interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 透過其緊湊且強大的結構來解決此限制。它將使用者介面螢幕擷取畫面轉換為結構化輸出元素，使得設計能夠在各種應用程式中執行精確動作的代理程式成為可能。當與 GPT-4V 等模型結合使用時，OmniParser 顯著提高了代理程式準確與使用者介面互動的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser V2 takes this capability to the next level. Compared to its predecessor, It achieves higher accuracy in detecting smaller interactable elements and faster inference, making it a useful tool for GUI automation. In particular, OmniParser V2 is trained with larger size of interactive element detection data and icon functional caption data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser V2 將此能力提升到新的層次。與其前身相比，它在偵測較小的可互動元素和更快的推斷方面達到更高的正確性，使其成為 GUI 自動化的實用工具。特別是，OmniParser V2 已使用更大規模的互動式元素偵測資料和圖示功能標題資料進行訓練。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The creation of OmniParser involved the development of specialized datasets, including an interactable icon detection dataset that identifies actionable regions within popular web pages, and an icon description dataset that correlates UI elements with their functions. These resources are crucial for training the detection and captioning models utilized by OmniParser. The detection model, specifically fine-tuned on the interactable icon dataset, reliably locates actionable screen regions, while the captioning model provides contextually relevant descriptions for the detected elements.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 的建立涉及特殊資料集的開發，包括可互動圖示偵測資料集，能夠識別常用網頁中的可採取動作區域，以及將 UI 元素與其功能相關聯的圖示描述資料集。這些資源對於訓練 OmniParser 所使用的偵測和標題模型至關重要。偵測模型特別針對可互動圖示資料集進行微調，能可靠地找出可採取動作的螢幕區域，而標題模型則為偵測到的元素提供與内容相關的描述。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is designed to be modular and adaptable, enhancing interactions across both PC and mobile platforms.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 的設計為模組化且可調整，增強電腦和行動裝置平台之間的互動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser V2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser V2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open OmniParser repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 OmniParser 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A colorful geological map with various regions marked in different colors and a detailed legend in Chinese on the right side.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一幅彩色地質圖，不同區域以多種顏色標示，右側附有中文圖例詳細說明。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE enhances multimodal large language models (MLLMs) with geologic expertise, enabling accurate interpretation of complex, high-resolution maps. By integrating structured extraction, domain knowledge, and reasoning, it supports critical tasks in disaster risk, resource discovery, and infrastructure planning—turning general AI into a specialized tool for geoscience.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 利用地質專業知識來增強多模式大型語言模型 (MLLM)，進而能正確解譯複雜的高解析度地圖。它透過整合結構化擷取、領域知識和推理，支援災害風險、資源探勘和基礎結構規劃的關鍵任務：將一般 AI 轉變為地球科學的專用工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE paves the way for advanced AI applications in geology, powering more efficient and accurate disaster detection, resource exploration, and civil engineering. Learn how PEACE transforms general-purpose multimodal LLMs into powerful domain-specific agents.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 為地質學進階 AI 應用程式鋪路，讓災害偵測、資源探索和土木工程更有效率且精確。了解 PEACE 如何將一般用途的多模式 LLM 轉換為強大的特定領域代理程式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore PEACE]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 PEACE]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE (emPowering gEologic mAp holistiC undErstanding) enhances multimodal large language models (MLLMs) for expert-level geologic map understanding. Geologic maps, which provide critical insights into the structure and composition of Earth’s subsurface and surface, are vital tools in disaster detection, resource exploration, and civil engineering. But their complexity—featuring high-resolution visuals, symbolic representations, and domain-specific knowledge—poses significant challenges for current AI models. General-purpose MLLMs often fall short when interpreting such data due to the intricacies of cartographic generalization and geoscientific reasoning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE (emPowering gEologic mAp holistiC undErstanding) 增強多模式大型語言模型 (MLLM)，以達到專家級地質圖理解。地質圖提供地表下和地表結構與組合的重要深入解析，是災害偵測、資源探勘和土木工程的重要工具。但是其複雜度 (以高解析度視覺效果、符號表示法和特定領域知識為特徵) 對目前的 AI 模型構成了重大挑戰。由於製圖概括化和地球科學推理的錯綜複雜，通用型 MLLM 在解譯這類資料時往往會達不到要求。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To bridge this gap, Microsoft researchers and collaborators introduced GeoMap-Bench, the first benchmark specifically designed to evaluate MLLMs across five capabilities essential to geologic map interpretation: extracting, referring, grounding, reasoning, and analyzing. They also developed GeoMap-Agent, an AI system tailored to these challenges.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為了銜接這個差距，Microsoft 研究人員和共同合作者引進了 GeoMap-Bench，這是第一個特別針對地質圖解譯的五項必備功能評估 MLLM 所設計的基準：擷取、參考、建基、推理和分析。他們也開發了 GeoMap-Agent，這是專為這些挑戰量身打造的 AI 系統。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GeoMap-Agent is composed of three key modules:]A;<ul><li><strong>Hierarchical Information Extraction (HIE)</strong> for parsing structured content from complex maps,</li><li><strong>Domain Knowledge Injection (DKI)</strong> for embedding geological expertise, and</li><li><strong>Prompt-enhanced Question Answering (PEQA)</strong> for improved interpretive and reasoning capabilities.</li></ul>Together, these modules enable GeoMap-Agent to outperform existing models with superior accuracy and depth in geologic tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GeoMap-Agent 由三個主要模組組成：]A;<ul><li><strong>階層式資訊擷取 (HIE)</strong> 用於剖析複雜地圖中的結構化內容、</li><li><strong>領域知識注入 (DKI)</strong> 用於內嵌地質專業知識，以及</li><li><strong>提示增強式問題解答 (PEXML)</strong> 用於改善解譯和推理能力。</li></ul>這些模組共同使 GeoMap-Agent 在地質工作中以更高的正確性和深度超越現有的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rather than modifying MLLMs themselves, PEACE builds intelligent, domain-specific layers on top of them, turning general models into specialized agents capable of handling real-world geoscientific problems. This advancement marks a critical step toward applying AI in Earth science, empowering faster, more accurate geological assessments for both researchers and practitioners.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[除了修改 MLLM 本身之外，PEACE 還在其上建置智慧、特定領域層，將一般模型轉變為能夠處理真實世界地球科學問題的專門代理程式。這項進展在地球科學領域應用 AI 邁出了關鍵一步，使研究人員和從業人員能夠更快速、更準確地進行地質評估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open PEACE repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 PEACE 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 供研究用途發行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4BodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The text 'Phi-4' in glowing white letters on a purple and blue gradient background]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[紫色和藍色漸層背景上發光的白色字母 'Phi-4']]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4BodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Table showing Phi-4 benchmarks against comparable models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示 Phi-4 與可比較模型的基準資料表]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore the capabilities of Phi-4, the latest model in Microsoft's Phi family of advanced AI technologies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Phi-4 的功能，這是 Microsoft Phi 系列中最新的進階 AI 技術模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4HomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore the capabilities of Phi-4, the latest model in Microsoft's Phi family of advanced AI technologies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Phi-4 的功能，這是 Microsoft Phi 系列中最新的進階 AI 技術模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4HomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Microsoft Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Microsoft Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal and Phi-4-mini, the newest models in Microsoft’s Phi family of small language models (SLMs) are now available. These models are designed to empower developers with advanced AI capabilities. Phi-4-multimodal, with its ability to process speech, vision, and text simultaneously, opens new possibilities for creating innovative and context-aware applications. Phi-4-mini, on the other hand, excels in text-based tasks, providing high accuracy and scalability in a compact form.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的 Phi 系列中最新的小型語言模型 (SLM) Phi-4-multimodal 和 Phi-4-mini 現已可供使用。這些模型旨在為開發人員提供進階的 AI 能力。Phi-4-multimodal 能夠同時處理語音、視覺和文字，為創建創新且具上下文感知的應用程式開啟了新的可能性。另一方面，Phi-4-mini 在基於文字的工作中表現出色，以緊湊的形式提供高正確性和可擴縮性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal marks a new milestone in Microsoft’s AI development as our first multimodal language model. At the core of innovation lies continuous improvement, and that starts with listening to our customers. In direct response to customer feedback, we’ve developed Phi-4-multimodal, a 5.6B parameter model, that seamlessly integrates speech, vision, and text processing into a single, unified architecture. By leveraging advanced cross-modal learning techniques, this model enables more natural and context-aware interactions, allowing devices to understand and reason across multiple input modalities simultaneously. Whether interpreting spoken language, analyzing images, or processing textual information, it delivers highly efficient, low-latency inference—all while optimizing for on-device execution and reduced computational overhead. Natively built for multimodal experiences Phi-4-multimodal is a single model with mixture-of-LoRAs that includes speech, vision, and language, all processed simultaneously within the same representation space. The result is a single, unified model capable of handling text, audio, and visual inputs seamlessly—no need for complex pipelines or separate models for different modalities. The Phi-4-multimodal is built on a new architecture that enhances efficiency and scalability. It incorporates a larger vocabulary for improved processing, supports multilingual capabilities, and integrates language reasoning with multimodal inputs. All of this is achieved within a powerful, compact, highly efficient model that’s perfectly suited for deployment on devices and edge computing platforms. This breakthrough model represents a major leap forward in AI technology, offering unprecedented performance in a small package. Whether you’re looking for advanced AI capabilities on mobile devices or edge systems, Phi-4-multimodal provides a high-capability option that’s both efficient and versatile. With its impressive range of capabilities and flexibility, Phi-4-multimodal opens exciting new possibilities for app developers, businesses, and industries looking to harness the power of AI in innovative ways. The future of multimodal AI is here, and it’s ready to transform your applications. Phi-4-multimodal is capable of processing both visual and audio together. The following table shows the model quality when the input query for vision content is synthetic speech on chart/table understanding and document reasoning tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-multimodal 標誌了 Microsoft AI 開發的新里程碑，成為我們首個多模態語言模型。創新的核心在於持續改進，而這一切始於傾聽客戶的聲音。為了直接回應客戶的反饋，我們開發了 Phi-4-multimodal，這是一個擁有 5.6B 參數的模型，能夠將語音、視覺和文本處理無縫整合到一個統一的架構中。通過利用進階的跨模態學習技術，該模型實現了更自然且具上下文感知的互動，使裝置能夠同時理解和推理多種輸入模態。無論是解譯口語語言、分析影像還是處理文本資訊，它都能提供高效、低延遲的推斷，並針對裝置執行進行最佳化，降低計算負擔。Phi-4-multimodal 是為多模態體驗而原生構建的單一模型，具備混合 LoRAs，能夠同時處理語音、視覺和語言，所有這些都在相同的表示空間內進行處理。最終結果是一個單一的統一模型，能夠無縫處理文本、音訊和視覺輸入，無需為不同模態設置複雜的管道或分開模型。Phi-4-multimodal 建立在新架構上，提升了效率和可擴縮性。它包含更大的詞彙以改善處理，支援多語言能力，並將語言推理與多模態輸入整合。所有這些都在一個強大、精簡且高效的模型中實現，該模型非常適合在裝置和邊緣計算平台上部署。這一突破性模型代表了 AI 技術的一次重大進展，並透過小模型提供了前所未有的效能。無論您是在行動裝置還是邊緣系統上尋找進階 AI 功能，Phi-4-multimodal 都提供了一個高效且多功能的選擇。憑藉其令人印象深刻的功能範圍和靈活性，Phi-4-multimodal 為應用程式開發人員、企業和行業開啟了令人興奮的新可能性，讓他們能夠以創新的方式利用 AI 的力量。多模態 AI 的未來已經來臨，並準備好改變您的應用程式。Phi-4 多模態能夠同時處理視覺和音訊。下表顯示當視覺內容的輸入查詢為針對圖表/資料表理解和文件推理工作的合成語音時，模型的品質。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal is capable of processing both visual and audio together. The following table shows the model quality when the input query for vision content is synthetic speech on chart/table understanding and document reasoning tasks. Compared to other existing state-of-the-art omni models that can enable audio and visual signals as input, Phi-4-multimodal achieves much stronger performance on multiple benchmarks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4 多模態能夠同時處理視覺和音訊。下表顯示當視覺內容的輸入查詢為針對圖表/資料表理解和文件推理工作的合成語音時，模型的品質。與其他現有的最先進全能模型 (能夠將音訊和視覺信號作為輸入) 相比，Phi-4 多模態在多項基準測試中展示了更强的效能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-mini is a 3.8B parameter model and a dense, decoder-only transformer featuring grouped-query attention, 200,000 vocabulary, and shared input-output embeddings, designed for speed and efficiency. Despite its compact size, it continues outperforming larger models in text-based tasks, including reasoning, math, coding, instruction-following, and function-calling. Supporting sequences up to 128,000 tokens, it delivers high accuracy and scalability, making it a powerful solution for advanced AI applications. Function calling, instruction following, long context, and reasoning are powerful capabilities that enable small language models like Phi-4-mini to access external knowledge and functionality despite their limited capacity. Through a standardized protocol, function calling allows the model to seamlessly integrate with structured programming interfaces. When a user makes a request, Phi-4-Mini can reason through the query, identify and call relevant functions with appropriate parameters, receive the function outputs, and incorporate those results into its responses. This creates an extensible agentic-based system where the model’s capabilities can be enhanced by connecting it to external tools, application program interfaces (APIs), and data sources through well-defined function interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-mini 是一個擁有 3.8B 參數的模型，為密集型、僅限解碼器的轉換器，具備群組查詢注意、200,000 個詞彙和共用的輸入輸出嵌入，專為速度和效率而設計。儘管其體積小巧，但針對基於文字的工作，其表現仍持續超越更大型的模型，包括推理、數學、編碼、遵從指令和函數呼叫。Phi-4-mini 支援最多 128,000 個權杖的序列，提供高正確性和可擴縮性，使其成為進階 AI 應用的強大解決方案。由於有函數呼叫、遵從指令、長背景内容和推理等強大功能，因此即便像 Phi-4-mini 的小型語言模型，也能夠在容量有限的情況下，存取外部的知識和功能。透過標準化的通訊協議，函數呼叫使模型能夠與結構化的程式介面無縫整合。當使用者提出請求時，Phi-4-mini 能夠透過查詢進行推理，並使用適當的參數識別和呼叫相關的函數，接收函數輸出，並將這些結果整合到其回應中。這會建立可擴展的代理型系統，透過定義明確的功能介面，將模型連接至外部工具、應用程式介面 (API) 和資料來源，以強化其功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are designed to handle complex tasks efficiently, making them ideal for edge case scenarios and compute-constrained environments. Given the new capabilities Phi-4-multimodal and Phi-4-mini bring, the uses of Phi are only expanding. Phi models are being embedded into AI ecosystems and used to explore various use cases across industries.]A;]A;<strong>Embedded directly to your smart device:</strong> Integrating Phi-4-multimodal directly into a smartphone could enable smartphones to process and understand voice commands, recognize images, and interpret text seamlessly. Users could benefit from advanced features like real-time language translation, enhanced photo and video analysis, and intelligent personal assistants that understand and respond to complex queries. This would elevate the user experience by providing powerful AI capabilities directly on the device, ensuring low latency and high efficiency.]A;]A;<strong>On the road:</strong> Imagine an automotive company integrating Phi-4-multimodal into their in-car assistant systems. The model could enable vehicles to understand and respond to voice commands, recognize driver gestures, and analyze visual inputs from cameras. For instance, it could enhance driver safety by detecting drowsiness through facial recognition and providing real-time alerts. Additionally, it could offer seamless navigation assistance, interpret road signs, and provide contextual information, creating a more intuitive and safer driving experience while connected to the cloud and offline when connectivity isn't available.]A;]A;<strong>Multilingual financial services:</strong> Imagine a financial services company integrating Phi-4-mini to automate complex financial calculations, generate detailed reports, and translate financial documents into multiple languages. For instance, the model can assist analysts by performing intricate mathematical computations required for risk assessments, portfolio management, and financial forecasting. Additionally, it can translate financial statements, regulatory documents, and client communications into various languages and could improve client relations globally.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這些模型的設計旨在有效率地處理複雜的工作，因此非常適合用於邊緣案例情境和計算受限的環境。隨著 Phi-4-multimodal 和 Phi-4-mini 帶來的新功能，Phi 的應用範圍不斷擴大。Phi 模型正被嵌入到 AI 生態系統中，並用於探索各行各業的各種使用案例。]A;]A;<strong>直接嵌入到您的智慧型裝置中:</strong> 將 Phi-4-multimodal 直接整合到智慧型手機中，可以使手機處理和理解語音命令、辨識影像，並順暢解譯文字。使用者可從即時語言翻譯、增強的相片和影片分析，以及能理解和回應複雜查詢的智慧型個人助理等進階功能中受益。透過直接在裝置上提供強大的 AI 功能，這將能提升使用者體驗，確保低延遲和高效率。]A;]A;<strong>在路上:</strong> 想像一家汽車公司將 Phi-4-multimodal 整合到其車内助理系統中。該模型可以使車輛理解並回應語音命令、辨識駕駛者手勢，並分析來自相機的視覺輸入。例如，它可以通過臉部辨識檢測駕駛者的疲勞，並提供即時警示，以增強駕駛安全。此外，它還可以提供流暢的導航協助、解譯路標，並提供上下文資訊，創造更直觀和安全的駕駛體驗 (無論是連接到雲端或是離線)。]A;]A;<strong>多語言財經服務:</strong> 想像一家財經服務公司整合了 Phi-4-mini，以自動化複雜的財務計算、生成詳細報告，並將財務文件翻譯成多種語言。例如，該模型可以協助分析師執行風險評估、投資組合管理和財務預測所需的複雜數學計算。此外，它還可以將財務報表、法規文件和客戶通訊翻譯成多種語言，並改善全球客戶關係。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4TryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bar chart comparing agent performance by ML task complexity between AIDE and Project Amelie (powered by R&D-Agent). The chart shows performance metrics (%) across different complexity levels (Low/Lite, Medium, High) with Project Amelie achieving 22.4% overall performance compared to AIDE's 16.9%. Project Amelie shows improved performance across all complexity categories, with the most significant improvement in the Low/Lite category represented by a light blue section at the bottom of each bar.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較 AIDE 與 Project Amelie (由 R&D-Agent 提供) 之間，ML 工作複雜度的代理程式效能的橫條圖。圖表顯示不同複雜度層級 (低/輕量、中、高) 的效能計量 (%)，相較於 AIDE 的 16.9%，Project Amelie 的整體效能達到 22.4%。Project Amelie 在所有複雜度類別均顯示了效能改善，而低/輕量類別最顯著的改善，則由每個橫條底部的淺藍色區段表示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A demo of using the Project Amelie agent to predict accommodation rental prices in Seattle. The Agent builds a regression model, answers questions about it, and provides code to run the model. The user then opens the code in VS Code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Project Amelie 代理程式來預測西雅圖住宿租賃價格的示範。代理程式會建置迴歸模型、回答其相關問題，並提供執行模型的程式碼。使用者接著會在 VS Code 中開啟程式碼。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[With Project Amelie, we are unveiling our first Foundry autonomous agent that can perform machine learning engineering tasks. ML teams can use the agent to initiate complex machine learning tasks using prompts —such as, “Help me create a model to predict customer churn"—and receive fully validated ML pipelines, detailed evaluation metrics, trained model, and ready-to-use, reproducible Python code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過 Project Amelie，我們將開展我們的第一個可執行機器學習工程工作的 Foundry 自主代理程式。ML 小組可以使用代理程式，使用提示來初始化複雜的機器學習工作 (例如「協助我建立模型來預測客戶流失」)，並接收經完整驗證的 ML 管線、詳細的評估計量、訓練的模型，以及可供使用的可重現 Python 程式碼。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie is our first Foundry autonomous agent built in collaboration with Microsoft Research that can perform machine learning engineering tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 是我們與 Microsoft Research 共同作業建置的第一個 Foundry 自主代理程式，可執行機器學習工程工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Project Amelie]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Project Amelie]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieSignUpLink1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign up for Private Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊個人預覽版]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieSignUpLink2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[sign up here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在這裡註冊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{SignUpLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{SignUpLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<em>Private Preview Coming Soon! Sign up to get early access and opportunity to share feedback. Approved users can explore and experiment with Project Amelie.</em>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<em>私人預覽即將推出!註冊以提早存取並有機會分享意見反應。經核准的使用者可以探索和實驗 Project Amelie。</em>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[At its core, Project Amelie is powered by innovation from Microsoft Research designed specifically to automate and optimize research and development processes in machine learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 的核心是由 Microsoft Research 創新所提供，專為自動化和最佳化機器學習中的研究與開發程序所設計。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie outperforms the current state of the art benchmarks on MLE-Bench by OpenAI, which measures MLE agent’s effectiveness on real world ML engineering tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 強於 MLE-Bench by OpenAI 上目前最先進的效能評定，後者可測量 MLE 代理程式在真實世界 ML 工程工作上的有效性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are currently in early beta and plan to launch Private Preview soon! If you are interested in getting early access to Project Amelie and sharing your insights to help shape the product, please {SignUpLink}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們目前處於早期 Beta，並計劃儘快推出私人預覽!如果您有興趣提早存取 Project Amelie 並分享您的深入解析來協助塑造產品，請 {SignUpLink}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A healthcare professional in scrubs sits with an older adult in a care facility, holding a tablet displaying a meal. In the foreground, a smartphone screen shows the ReMe app interface with options for game training, user feedback, and starting a conversation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一位身著醫護服的醫護人員在安養機構中與一位長者對話，手持平板電腦，螢幕上顯示著一份餐點。在前景中，一部智慧型手機螢幕顯示了 ReMe 應用程式介面，包含「遊戲訓練」、「使用者回饋」和「開始對話」等選項。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe is a web-based framework that helps researchers create AI chatbots for personalized training and interventions aimed at strengthening memory and cognitive functions. Early evaluations show its potential to contribute to digital health innovation and advance non-pharmacological approaches to cognitive health.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 是一種網頁架構，可協助研究人員建立 AI 聊天機器人來進行個人化訓練和介入，以期強化記憶和認知功能。早期評估顯示它有潛力促進數位健康創新並推動非藥物認知健康方法的發展。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe enables scalable, personalized approaches to cognitive health—a growing need that affects millions. Explore this web-based framework, which puts powerful AI-enabled research tools in the hands of scientists and clinicians.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 針對認知健康提供可調整的個人化方法，影響數百萬人且日益增長的需求。探索此網頁架構，其將強大的 AI 研究工具交到科學家和臨床醫生手中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore ReMe]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 ReMe]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe is a web-based framework designed to accelerate research into personalized cognitive training using AI chatbots. As cognitive decline becomes a growing public health concern, ReMe supports researchers, clinicians, and caregivers in developing interactive training tasks focused on episodic memory and open-ended cognitive challenges.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 是 Web 架構，旨在加速使用 AI 聊天機器人進行個人化認知訓練的研究。隨著認知下降成為日益嚴重的公共衛生疑慮，ReMe 支援研究人員、臨床醫生和照護人員發展著重於情節記憶和開放式認知挑戰的互動式訓練工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The framework integrates a puzzle engine, a life-logging module for personal memory recall, and a multimodal training interface featuring text, image, and voice capabilities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此架構整合了謎題引擎、用於個人記憶回憶的生命週期記錄模組，以及具備文字、影像和語音功能的多模式訓練介面。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cognitive training is one of the few non-pharmacological methods shown to help delay decline, but existing programs are often generic and not very engaging. ReMe aims to make cognitive training more personalized and adaptable while reaching more people at lower cost.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[認知訓練是少數被證實有助於延緩衰退的非藥物方法之一，但現有的程式往往比較籠統，吸引力不大。ReMe 旨在讓認知訓練更加個人化且更具調適性，同時以更低的成本觸及更多人。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instead of building every new cognitive training chatbot from scratch, researchers can use ReMe to prototype, test, and improve interventions more quickly, speeding up discovery of what works.  ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[研究人員可使用 ReMe 更快速地建立原型、測試及改善介入，而不需從頭開始建置每個新的認知訓練聊天機器人，進而加速探索可行的方法。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In initial evaluations, ReMe was well received by users citing strong conversational fluency and moderate difficulty. While not intended for clinical treatment, ReMe provides a valuable tool for exploring AI’s role in supporting cognitive health, paving the way for future innovations in personalized digital therapies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在初步評估中，ReMe 深受使用者好評，他們聲稱交談極為流暢且難度適中。雖然 ReMe 並非用於臨床治療，但提供了一個寶貴的工具，以便探索 AI 在支援認知健康方面的角色，進而為個人化數位療法的未來創新鋪路。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open ReMe repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 ReMe 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 供研究用途發行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chemical structures and colored markers on a scatter plot.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[散佈圖上的化學結構和彩色標記。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen is a transformer-based chemical language model for developing target-specific drug compounds. Research shows that TamGen can also optimize existing molecules by designing target-aware molecule fragments, potentially enabling the discovery of novel compounds that build on a known molecular core structure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen 是一種基於變壓器的化學語言模型，用於開發目標特定的藥物化合物。研究顯示，TamGen 也可以通過設計目標感知的分子片段來最佳化現有的分子，從而有可能發現建立在已知分子核心結構上的新型化合物。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover how AI could accelerate the process of pharmaceutical discovery, leading to faster medical breakthroughs and improved treatment options.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 AI 如何加速藥物研發的流程，從而促進更快速的醫學突破及改善的治療選項。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover TamGen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 TamGen]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generative AI is opening new avenues for scientific exploration by allowing computers to autonomously learn and produce original content. TamGen offers a new approach to drug discovery by applying the principles of generative AI to molecular design. Unlike traditional methods, which depend on systematically screening known compounds—a process that is long, complex, and costly due to its reliance on empirical knowledge and the time-consuming task of exploring a vast chemical library—generative AI provides opportunities for designing entirely new chemical structures.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成式 AI 正為科學探索開啟新的途徑，讓電腦能夠自主學習和產生原創內容。TamGen 將生成式 AI 的原則應用於分子設計，提供了一種新的藥物研發方法。傳統方法依賴系統性地篩選已知化合物，由於依賴經驗知識以及探索龐大化學庫的耗時工作，因此過程冗長、複雜且成本高昂，而生成式 AI 則不同，它提供了設計全新化學結構的機會。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen showcases the transformative potential of generative AI in drug design, combining advanced molecular modeling with researcher-AI collaboration. Tasks that once took years could now be accomplished in a fraction of the time. This research underscores AI’s expanding role in drug discovery and its promise for developing effective treatments against persistent infectious diseases like tuberculosis.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen 結合了進階分子建模與研究人員-AI 共同作業，展示了生成式 AI 在藥物設計方面的轉型潛力。曾經需要數年才完成的工作，現在可以在短時間內完成。這項研究強調了 AI 在藥物研發中日益擴大的角色，以及其對於持久性傳染病 (如肺結核病) 開發有效治療的承諾。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get TamGen model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 TamGen 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis creates  high-quality 3D assets from simple text or image inputs. Using a unified latent space (SLAT), it delivers detailed, textured 3D models in formats like meshes,radiance fields, and 3D Gaussians. Its flexibility, editing capabilities, and superior quality enable faster, more adaptable workflows in gaming, virtual worlds, industrial design, and beyond.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 可從簡單的文字或影像輸入建立高品質的 3D 資產。它利用統一的延遲空間 (SLAT) 輸出具有細節與紋理的 3D 模型，格式包括網格、輻射場與 3D 高斯表示。它的彈性、編輯能力與卓越品質，使其在遊戲、虛擬世界、工業設計等領域實現更快速且具適應性的工作流程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis is an AI model that generates high-quality 3D assets from text or image prompts in formats like meshes, radiance fields, and 3D Gaussians. Discover how it uses Structured LATents (SLAT) to fuse sparse 3D grids with dense visual features—powering easy and creative 3D content creation in gaming, AR/VR, design, and simulation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 是一款 AI 模型，可根據文字或影像提示產生高品質的 3D 資產，格式包括網格、輻射場與 3D 高斯表示。探索它如何利用結構化 LATents (SLAT) 將稀疏的 3D 格點與密集視覺特徵融合，推動遊戲、AR/VR、設計與模擬中的輕鬆且富創意的 3D 內容創作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Trellis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Trellis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis provides a powerful foundation for scalable, AI-driven content creation. Built to meet rapidly growing industrial demand, Trellis creates high-quality, editable 3D assets from simple text or image prompts in lieu of manual modeling. This saves time, lowers barriers, and unlocks new possibilities for developers, designers, and digital content creators.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 為可擴展的 AI 驅動內容建立提供了強大的基礎。為滿足快速增長的產業需求，Trellis 可透過簡單的文字或影像提示產生高品質且可編輯的 3D 資產，而無需手動建模。這不僅可節省時間，降低門檻，還為開發人員、設計師與數位內容創作者開啟了全新可能性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis is built on a novel Structured LATent (SLat) representation that captures both geometric structure and visual detail in a compact, editable form. Trained on 500,000 diverse 3D objects using rectified flow transformers with up to 2 billion parameters, Trellis significantly improves both quality and flexibility compared to existing models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 建構於創新的結構化 LaTent (SLat) 表示法上，能以精簡且可編輯的形式，同時捕捉幾何結構與視覺細節。Trellis 以 500,000 個多樣化 3D 物件訓練，並採用高達 20 億參數的校正式流變換器，與現有模型相比，大幅提升了品質與靈活性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike traditional methods that target a single output type or require labor-intensive setup, Trellis can generate a 3D asset in multiple formats, including meshes, 3D gaussians, and radiance fields. This makes it compatible with different rendering pipelines and applications. The generated models feature detailed structure and rich texture, enabling their direct use in games, AR/VR experiences, digital twins, simulation environments, and product visualization.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不同於僅針對單一輸出格式或需要大量人工設定的傳統方法，Trellis 能生成多種格式的 3D 資產，包括網格、3D 高斯表示及輻射場。這使得它能相容於各種呈現管線與應用程式。所生成的模型具備精細結構與豐富紋理，可直接應用於遊戲、AR/VR 體驗、數位分身、模擬環境與產品視覺效果等領域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis also allows for prompt-guided local edits—such as removing, replacing, or adding parts of a 3D model—without retraining or manual sculpting, which dramatically accelerates iteration and customization. Its design eliminates the need for costly 3D fitting and leverages pretrained vision models for high-fidelity results, even when working with sparse 3D data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 也允許由提示引導的局部編輯——例如移除、更換或新增 3D 模型的某部分——而無需重新訓練或手動雕刻，大幅加快迭代與自訂流程。它的設計省去了高成本的 3D 擬合流程，並利用預訓練視覺模型，即使在處理稀疏的 3D 數據時也能產出高保真度的成果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[細網紋]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Trellis repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 Trellis 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TryOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{modelName} has been released for research purposes. Users can learn, explore and experiment with {modelName}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{modelName} 已發行供研究之用。使用者可以使用 {modelName} 學習、探索及實驗。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A demonstration of using the TypeAgent shell navigating and interacting with a visually rich Paleobiology Database (paleodb) website, highlighting its ability to process and act on complex web interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[示範使用 TypeAgent 殼層瀏覽並與視覺效果豐富的古生物學資料庫 (paleodb) 網站互動，強調其處理複雜 Web 介面並在其上採取行動的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An demonstration of the TypeAgent Shell where a user converses with the agent about events and entities extracted months earlier. The gif shows entities being retrieved from long-term memory into the conversation, enabling the user to take actions on them.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 殼層的示範，其中的使用者與代理程式討論數月前所擷取的事件和實體。gif 顯示將實體從長期記憶擷取到交談中，讓使用者能夠對它們採取動作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent is sample code that explores an architecture for building a single personal agent with natural language interfaces leveraging current advances in LLM technology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 是一項範例程式碼，可探索運用 LLM 技術目前的進展，以自然語言介面建置單一個人代理程式的結構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent is sample code that explores an architecture for building a single personal agent with natural language interfaces leveraging current advances in LLM technology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 是一項範例程式碼，可探索運用 LLM 技術目前的進展，以自然語言介面建置單一個人代理程式的結構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover TypeAgent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 TypeAgent]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The goal of the TypeAgent team is to explore how to get work done by safely and efficiently combining stochastic systems like language models with traditional software components. Three principles have emerged during this investigation. They are listed below along with examples of how the principles apply to actions, memory and plans.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 小組的目標是要探索如何安全且有效率地結合推測系統 (例如語言模型) 與傳統軟體元件，以完成工作。此調查期間呈現了三個原則。以下列出這些原則，以及原則如何套用至動作、記憶和計劃的範例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<ul><li><strong>Principle:</strong> distilling models into logical structures</li><ul><li>Actions: find translation patterns and replace some model calls by applying patterns</li><li>Memory: build ontologies from text</li><li>Plans: people, programs and models collaborate using “tree of thought”</li></ul><br><li><strong>Principle:</strong> control information density</li><ul><li>Actions: applications define discrete categories with dense descriptions of action sets</li><li>Memory: tight semantic structures fit into attention budget</li><li>Plans: each search tree node defines a focused sub-problem</li></ul><br><li><strong>Principle:</strong> use logical structures to enable collaboration</li><ul><li>Actions: humans decide how to disambiguate action requests</li><li>Memory: simple models extract logical structure from text</li><li>Plans: quality models, advantage models, language models, humans and programs collaborate to expand each best-first-search node</li></ul>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<ul><li><strong>原則:</strong> 將模型放入邏輯結構</li><ul><li>動作: 尋找翻譯模式並透過套用模式來取代一些模型呼叫</li><li>記憶: 從文字建置本體</li><li>計劃: 使用「思維樹」進行人員、程式和模型共同作業</li></ul><br><li><strong>原則:</strong> 控制資訊密度</li><ul><li>動作: 應用程式使用動作集的密集描述來定義相異類別</li><li>記憶: 緊密的語意結構符合注意預算</li><li>計劃: 每個搜尋樹狀目錄節點都定義聚焦的子問題</li></ul><br><li><strong>原則:</strong> 使用邏輯結構來啟用共同作業</li><ul><li>動作: 人員決定如何消除動作要求的歧義</li><li>記憶: 從文字擷取邏輯結構的簡單模型</li><li>計劃: 品質模型、優點模型、語言模型、人類和程式共同作業，以擴大每個最佳搜尋節點</li></ul>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are trying to build a single personal agent that can apply to any application.  To apply agent interfaces to all applications, we need to map user requests to actions at much lower cost and latency than current systems. To make this possible, we have created a system that can distill language models into logical systems that can handle most user requests.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們正在嘗試建置可套用至任何應用程式的單一個人代理程式。 若要將代理程式介面套用至所有應用程式，我們需要以較目前系統低很多的成本和延遲，將使用者要求對應至動作。為了讓這點可行，我們建立了一個系統，可以將語言模型放入可處理大部分使用者要求的邏輯系統中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Figure 1: The TypeAgent shell example navigating a visually rich {paleodbLink} website.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[圖 1: 瀏覽視覺效果豐富的 {paleodbLink} 網站的 TypeAgent 殼層範例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We use LLMS with structured prompts to extract a logical representation of actions on a page (e.g. buy product). This logical schema is the same across multiple sites, even if the sites have different HTML and JS implementations. We demonstrate the power of this approach by building automation to interact with multiple crossword sites and multiple e-commerce sites using consistent logical schema.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們使用 LLMS 搭配結構化提示，以擷取頁面上動作 (例如購買產品) 的邏輯表示法。此邏輯架構在多個網站間是相同的，即使網站有不同的 HTML 和 JS 實作亦然。我們透過建置自動化，以使用一致的邏輯架構與多個填字遊戲網站和多個電子商務網站互動，來示範此方法的強大能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are trying to create human-like memory with super-human precision and recall for agent conversations. We are using a new indexing and query processing approach called <strong>Structured RAG</strong> as the basis for agent memory. Structured RAG does substantially better than Classic RAG at answering questions about past conversations such as "what were the books we talked about?" and "what step were we on in building the photo montage?"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們正嘗試建立具有超級人類精確度與代理程式交談回顧的類人類記憶。我們使用名為<strong>結構化 RAG</strong> 的新索引編製和查詢處理方法，做為代理程式記憶的基礎。結構化 RAG 在回答有關過去交談的問題 (例如「我們討論了哪些書籍?」和「我們在建立相片拼集 (蒙太奇) 時到了什麼步驟?」) 方面，比傳統 RAG 更好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Figure 2: Here using the experimental TypeAgent Shell a user can have conversations with the agent about events and entities that were extracted months ago. Entities are pulled from long-term memory into the conversation memory and user can then take actions on the entities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[圖 2: 在這裡，使用實驗性 TypeAgent 命令殼層，使用者可以與代理程式進行有關數月前所擷取事件和實體的交談。實體會從長期記憶提取到交談記憶，然後使用者可以對實體採取動作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText8" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Actions and memories flow together. Actions like "add to my calendar pickleball game 2-3pm on Friday" yield memories that can become parameters of future actions like "put in an hour of recovery time after my pickleball game." We are working on an architecture, <strong>AMP</strong>, that enables this natural information flow by integrating actions, memories, and plans. We are applying AMP to the web by creating a browser that enables web sites to register actions through a JavaScript interface.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[動作和記憶會合流。像是「將星期五下午 2:00 到下午 3:00 匹克球遊戲新增到我的行事曆」這樣的動作，會產生記憶，其可能會成為未來動作的參數，例如「在我的匹克球遊戲後放入一小時的休息時間」。我們正在努力開發架構 <strong>AMP</strong>，其會整合動作、記憶和計劃，以實現這種自然資訊流程。我們正在透過建立可讓網站透過 JavaScript 介面登錄動作的瀏覽器，將 AMP 套用至 Web。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open TypeAgent repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 TypeAgent 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A diagram showing the process of generating a 3D talking head animation from a single image using VASA-3D. The steps include creating VASA-1 videos from the image, building a VASA-3D model, and combining it with an audio clip and optional control signals to produce various animated outputs of the subject.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一個圖表顯示了如何使用 VASA-3D 從單一影像產生 3D 頭像動畫的流程。這些步驟包括: 從影像產生 VASA-1 影片，以這些影片構建 VASA-3D 模型，並將模型與音訊剪輯及選用的控制訊號結合，以產生主題的各種動畫輸出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D exemplifies how generative AI can enhance human-computer interaction by making expressive, customizable 3D avatars accessible from minimal input. Extending VASA-1's motion latent into 3D and optimizing with synthetic multiview data, it opens new frontiers in communication, immersion, and assistive technology—setting a new standard for realism and responsiveness in avatar generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 展示了生成式 AI 如何讓您經由最少的輸入來存取表達力強的可自訂 3D 虛擬人偶，進而增強人機互動。將 VASA-1 的動作潛在特徵延伸至 3D 並使用合成多視角資料進行最佳化，在通訊、沉浸式及輔助技術方面開啟了新的領域，進而為虛擬人偶生成的真實技術和回應能力樹立新標準。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D turns a single portrait and speech audio into a lifelike 3D talking head using a novel motion-latent-driven model. Discover how it enables real-time, expressive, and multiview-consistent avatars for education, collaboration, and immersive experiences.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 使用新穎的動作潛在特徵驅動模型，將單一肖像和語音音訊轉換成逼真的 3D 說話頭像。探索它如何為教育、共同作業和沉浸式體驗提供即時、表達力強和多視角一致的虛擬人偶。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore VASA-3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 VASA-3D]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D is a major step forward in 3D avatar generation, producing realistic, expressive, and multiview-consistent 3D talking heads from just a single image and speech audio. The system builds on VASA-1, which introduced high-fidelity 2D talking head synthesis through a richly expressive motion latent. VASA-3D extends this capability into 3D by conditioning a neural 3D head model on the motion latent, capturing nuanced facial expressions and natural head motion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 是 3D 虛擬人偶生成的一大進步，從單一影像和語音音訊產生逼真、表達力強和多視角一致的 3D 說話頭像。此系統以 VASA-1 為基礎，透過富有表達力的動作潛在特徵引進了高度逼真 2D 說話頭像合成。VASA-3D 藉由調節動作潛在特徵的神經 3D 頭像模型，將此功能延伸至 3D，以捕捉細微的面部表情和自然頭像動作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To personalize the 3D head from a single portrait, VASA-3D creates additional views of the face from different angles and uses them to fine-tune the 3D model, even if some of those views have visual flaws or limited variety. The result is a model capable of real-time generation at 75 frames per second with just 65 milliseconds latency, supporting free-view rendering, emotional control, and high-quality animation, even from stylized or artistic portraits.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為了從單一肖像個人化 3D 頭像，VASA-3D 會從不同角度建立臉部的額外視圖，並使用它們來微調 3D 模型，即使其中某些視圖有視覺瑕疵或變化有限。結果是一個模型，能夠即時產生每秒 75 個畫面格且延遲只有 65 毫秒，並支援自由視角呈現、情緒控制，以及高品質的動畫，即使是從非寫實或藝術肖像產生。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D significantly outperforms prior methods on realism and expressiveness in both audio- and video-driven talking head tasks. Its broad potential spans virtual collaboration (AI coworkers), education (AI tutors), entertainment, and neuroscience research. Early applications include VR-based social interaction, memory activation studies, and adaptive learning tools. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 在音訊和視訊驅動說話頭像工作的真實技術和表達能力方面，明顯超越先前的方法。其廣泛的潛力涵蓋虛擬共同作業 (AI 同事)、教育 (AI 導師)、娛樂和神經科學研究。早期應用程式包括 VR 型社交互動、記憶體啟用研究，以及調適型學習工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Responsible AI is integral to VASA-3D’s development. To prevent misuse, the model and APIs are not publicly released. Face forgery detection systems trained on VASA-3D outputs can reliably distinguish synthetic content, enhancing safety and model robustness.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[負責的 AI 是 VASA-3D 開發不可或缺的一部分。為了防止濫用，不會公開發行該模型和 API。在 VASA-3D 輸出上訓練的臉部偽造偵測系統能可靠地區分合成內容，並增強安全和模型穩健性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D exemplifies how advanced generative AI can enhance human-computer interaction. By making expressive, customizable 3D avatars accessible from minimal input, it opens new frontiers in communication, immersion, and assistive technology—setting a new standard for realism and responsiveness in avatar generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 展示了進階生成式 AI 如何增強人機互動。讓您經由最少的輸入來存取表達力強的可自訂 3D 虛擬人偶，在通訊、沉浸式和輔助技術方面開啟了新的領域，進而為虛擬人偶生成的真實技術和回應能力樹立新標準。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Vasa-3D repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 Vasa-3D 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA3D has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA3D 供研究用途發行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a framework for generating realistic, long-form, multi-speaker dialogue from transcripts, making it ideal for podcasts, voiceovers, and narrative audio. Unlike typical TTS tools, VibePod handles up to four speakers across 30-minute sessions with strong speaker consistency, natural pacing, and turn-taking.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一個可從文字記錄產生真實感十足、長篇多角色對話的架構，非常適合用於播客、配音及敘事型音訊內容。不同於一般的文字轉換語音工具，VibePod 可在長達 30 分鐘的對話中處理多達四位演講者，並維持高度一致性、自然的節奏與流暢的輪流發言。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a multi-speaker audio generation framework for creating long-form, realistic dialogue from transcripts. It’s ideal for podcasts and voiceovers, with strong performance in pacing, coherence, and speaker dynamics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一個多演講者音訊產生架構，能從文字記錄中建立長篇、逼真的對話。它非常適合用於播客與配音，並在節奏控制、語意連貫性與演講者變化方面表現優異。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore VibePod]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 VibePod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a new framework designed to generate realistic, multi-speaker audio content from transcripts. Ideal for producing podcasts, voiceovers, and other narrative formats, it outperforms conventional text-to-speech (TTS) tools, which are unable to produce long-form, coherent, and interactive multi-speaker dialogue. VibePod supports up to four distinct voices in 30-minute segments. With its improved pacing, turn-taking, and speaker consistency, VibePod rated highly in user testing for spontaneity and realism.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是新的框架，旨在從文字記錄產生真實的多演講者音訊內容。它非常適合製作播客、配音及其他敘事形式，其表現超越傳統的文字轉換語音 (TTS) 工具，後者無法生成長篇、連貫且具互動性的多角色對話。VibePod 在 30 分鐘的段落中最多支援四個不同的語音。憑藉其節奏控制、輪流發言和說話者一致性的提升，VibePod 在使用者測試中因其自然流暢與真實感獲得高度評價。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike systems focused on voice cloning, VibePod emphasizes dialogue quality over personalization. It was trained on publicly available and synthetically generated datasets, with safeguards built in to prevent misuse. It does not allow custom voice uploads, reflecting {ResponsibleAIPrinciples}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與專注於語音複製的系統不同，VibePod 更加重視對話的品質而非個人化。它是以公開可取得和合成生成的資料集進行訓練，並內建防濫用機制以避免不當使用。它不允許上傳自訂語音，以體現 {ResponsibleAIPrinciples}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText2Link" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft’s Responsible AI principles]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的負責任 AI 原則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To promote research, transparency, and responsible use, VibePod is being released on Hugging Face under the MIT License. Open-sourcing the technology invites collaboration from the broader speech synthesis community. Future enhancements will include multilingual support and controls for emotional tone, expanding its creative potential.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為了促進研究、透明度及負責任的使用，VibePod 將在 MIT 授權下於 Hugging Face 上發行。開源技術邀請來自更廣泛的語音合成社群進行合作。未來的增強功能將包括多語言支援及情感語調控制項，以擴展其創意潛力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.[object Object]" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops foundational image generation and editing models which are both fast and highly performant. These include FLUX.1 Kontext [pro]5D; and FLUX1.1 [pro]5D;.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開發既快速又高效能的基礎影像產生與編輯模型。這些包括 FLUX.1 Kontext [pro]5D; 和 FLUX1.1 [pro]5D;。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.ai21" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops advanced language models like Jurassic-2 for complex text generation and understanding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開發進階語言模型，例如 Assiassic-2，用於複雜文字產生和理解工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.aoai" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft-hosted OpenAI models, including GPT-4 and Codex, offering enterprise-grade security and compliance.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 裝載的 OpenAI 模型 (包括 GPT-4 和 Codex) 提供企業級安全性與合規性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.bayer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Leverages AI for advancements in healthcare and agriculture, focusing on research and development.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[運用 AI 在醫療保健和農業方面獲得進展，並專注於研究與開發。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.blackforestlabs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Amazing AI models from the Black Forest]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自 Black Forest 令人驚嘆的 AI 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.bria" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops generative AI models for visual content creation, including images and videos.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開發用於建立視覺內容 (包括影像和影片) 的生成式 AI 模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.cerence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides AI-powered voice and speech recognition solutions for automotive and mobility applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為汽車和行動應用程式提供 AI 支援的聲音和語音辨識解決方案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.cohere" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Offers language models optimized for retrieval-augmented generation and enterprise applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供針對增檢索增強生成和企業應用程式最佳化的語言模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.core42" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides multilingual language models tailored for Arabic and English, facilitating diverse linguistic applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供專為阿拉伯文和英文量身打造的多語系語言模型，可促進多樣化的語言應用程式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.databricks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides a unified data analytics platform integrating AI model development and deployment capabilities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供用於整合 AI 模型開發與部署功能的整合式資料分析平台。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.deci" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specializes in optimizing deep learning models for faster inference and deployment efficiency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專精於最佳化深度學習模型，以加快推斷和部署效率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.deepseek" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops cost-effective large language models like R1, optimized for performance and efficiency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開發具成本效益的大型語言模型，例如 R1，這些模型已針對效能與效率進行最佳化。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.gretel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides synthetic data generation tools to enhance privacy and augment datasets for machine learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供綜合資料產生工具，以增強隱私權並強化機器學習的資料集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.histai" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HistAI provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HistAI 提供者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.huggingface" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hosts a vast repository of open-source models and tools for natural language processing tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為自然語言處理工作裝載大型的開放原始碼模型和工具存放庫。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.meta" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open-source models like Llama 2, built for versatile language tasks and research applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如 Llama 2 等開放原始碼模型，專為多功能語言工作與研究應用程式所建置。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.mimsHarvard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.mistral" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[French AI startup offering efficient and cost-effective language models, including Mistral 7B and Mixtral.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[法國 AI 新創公司提供有效率且符合成本效益的語言模型，包括 Mistral 7B 和 Mixtral。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.nixtla" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Focuses on time-series forecasting models, offering tools like TimeGEN-1 for predictive analytics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[著重於時間序列預測模型，提供如 TimeGEN-1 等工具進行預測分析。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.nttdata" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides AI solutions across various industries, focusing on digital transformation and innovation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供跨各種產業的 AI 解決方案，著重於數位轉型與創新。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.nvidia" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Offers GPU-optimized models and tools for high-performance AI applications across various domains.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對跨不同網域的高效能 AI 應用程式提供 GPU 最佳化的模型和工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.paige" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specializes in AI-powered pathology solutions to enhance cancer diagnosis and treatment planning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專精於 AI 支援的病理解決方案，以增強癌症診斷與治療規劃。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.phi" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Proprietary AI models developed by Microsoft, tailored for various enterprise applications and integrated within Azure services.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 Microsoft 開發的專屬 AI 模型，專為各種企業應用程式量身打造並整合在 Azure 服務中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.rockwell" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops AI-driven industrial automation solutions to optimize manufacturing processes and operations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開發 AI 導向的工業自動化解決方案，以最佳化製造流程與作業。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.saifr" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Offers AI tools for content compliance and risk management in regulated industries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供 AI 工具，用於受監管產業的內容合規性和風險管理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.sdaia" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Saudi Data and AI Authority developing AI solutions to drive national digital transformation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Saudi Data and AI Authority 開發 AI 解決方案，以推動國家數位轉型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.sightmachine" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Offers AI-driven manufacturing analytics to improve operational efficiency and product quality.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供 AI 導向的製造分析，以改善營運效率與產品品質。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.snowflake" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cloud-based data platform that integrates with AI models for enhanced data processing and analysis.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與 AI 模型整合的雲端式資料平台，用於增強資料處理和分析。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.stabilityai" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specializes in open-source generative AI models for image and text generation, including Stable Diffusion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專精於開放原始碼生成式 AI 模型，以供影像和文字產生，包括 Stable Diffusion。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.wanglab" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.xai" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ChatCompletions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat completions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Finance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Finance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[財務]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.agents" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Agent supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援的代理程式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.agriculture" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Agriculture]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[農業]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.assistants" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assistant supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支援小幫手]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.audio-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Audio classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[音訊分類]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Audio Classification]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.audio-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Audio generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[音訊產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.audio-text-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Audio text To text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[音訊文字轉換文字]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Audio Text To Text]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.automatic-speech-recognition" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic speech recognition]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自動語音辨識]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.batch-paygo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批次]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.bitext_mining" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bitext mining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[雙文字採礦]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.chat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.chat-completion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat completion]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.clustering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[叢集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.completions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Completions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.consumer-goods" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Consumer goods]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[消費性商品]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Consumer Goods]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.conversational" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Conversational]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[交談]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.data-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.drug-discovery" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drug discovery]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[藥物發現]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.embeddings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Embeddings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[內嵌]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.environmental-forecasting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environmental forecasting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[環境預測]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Environmental Forecasting]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.feature-extraction" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Feature extraction]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[特徵擷取]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Feature Extraction]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.fill-mask" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fill mask]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[填寫遮罩]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.financial-services" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Financial services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金融服務業]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Financial Services]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.forecasting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Forecasting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預測]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.health-and-life-sciences" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Health and life sciences]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[醫療保健與生命科學]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Health and Life Sciences]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-feature-extraction" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image feature extraction]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像特徵擷取]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Image Feature Extraction]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-instance-segmentation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image instance segmentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像執行個體分割]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-object-detection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image object detection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像物件偵測]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-segmentation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image segmentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像分割]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-text-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image text To text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像文字轉換文字]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Image Text To Text]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-to-image" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image to image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像轉換影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image to text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像轉換文字]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.maap-inference" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受控計算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.manufacturing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Manufacturing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[製造業]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.materials-design" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Materials design]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[材料設計]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.mobility" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mobility]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移動產業]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.multi-object-tracking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multi-Object tracking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多個物件追蹤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.multimodal-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multimodal classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多模式分類]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Multimodal Classification]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.object-detection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Object detection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[對象偵測]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.pair_classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pair classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配對分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.protein-binder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Protein binder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[蛋白質結合劑]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Protein Binder]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.protein-design" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Protein design]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[蛋白質設計]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Protein Design]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.protein-structure-prediction" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Protein structure prediction]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[蛋白質結構預測]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Protein Structure Prediction]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ptu" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioned throughput (PTU)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[佈建的輸送量 (PTU)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Provisioned Throughput (PTU)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.question-answering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[問題解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.reasoning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.reranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新評等]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.responses" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Responses]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[回應]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.restrictedaccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restricted access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受限制的存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.retrieval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.sentence-similarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sentence similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[句子相似性]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sentence Similarity]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.serverless-inference" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless API]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無伺服器 API]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.speech-recognition" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Speech recognition]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語音辨識]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.speech-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Speech to text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語音轉換文字]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.standard-paygo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay per use]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按使用次數付費]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.streaming" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Streaming]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[串流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.sts" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[STS]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[STS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.table-question-answering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Table question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料表問題解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.table-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Table to text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料表轉換文字]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Table To Text]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-ranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text ranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字排名]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Text Ranking]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-to-3d" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to 3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字轉換 3D]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Text To 3D]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-to-image" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字轉換影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-to-speech" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to speech]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字轉換語音]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-translation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text translation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字翻譯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text2text-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to text generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字到文字產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.time-series-forecasting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Time series forecasting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[時間序列預測]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Time Series Forecasting]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.token-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語彙基元分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.tool-calling" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tool calling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工具呼叫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.translation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Translation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[翻譯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.video-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影片分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.video-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影片產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.video-multi-object-tracking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video multi-Object tracking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視訊多物件追蹤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.video-text-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video text to text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視訊文字轉換文字]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Video Text To Text]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.visual-question-answering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visual question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視覺效果問題解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.vm" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Virtual machine]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[虛擬機器]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Virtual Machine]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.vm-withsurcharge" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Optimized virtual machine]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已最佳化的虛擬機器]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Optimized Virtual Machine]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.zero-shot-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zero-shot classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零拍攝分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.zero-shot-image-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zero-shot image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零拍攝影像分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.chatHistory_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select the number of past messages to include in each new API request. This helps give the model context for new user queries. Setting this number to 10 will include 5 user queries and 5 system responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取要包含在每個新 API 要求中的過去訊息數目。這有助於為新使用者查詢提供模型內容。將此數字設定為 10 將會包含 5 個使用者查詢和 5 個系統回應。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.do_sample_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Whether or not to use sampling. If False, uses greedy decoding.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否要使用取樣。若為 False，則會使用窮盡解碼。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.file_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload the audio file to be transcribed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上傳要轉錄的音訊檔案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.formatting_reenabled_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enables response in Markdown format.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用 Markdown 格式的回應。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.frequency_penalty_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discourages the model from generating the same words or phrases too frequently by applying a penalty (between -2.0 and 2.0) based on their existing frequency in the text.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據在文字中的目前頻率，透過套用懲罰 (介於 -2.0 和 2.0 之間)，避免模型太頻繁地產生相同的單字或片語。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.frequency_penalty_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reduce the chance of repeating a token proportionally based on how often it has appeared in the text so far. This decreases the likelihood of repeating the exact same text in a response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據目前文字中出現的頻率，減少按比例重複權杖的機會。這會降低回應中重複完全相同文字的可能性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.generate_summary_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A summary of the reasoning performed by the model. This can be useful for debugging and understanding the model's reasoning process.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型推理過程的摘要。這對於偵錯和理解模型的推理過程非常實用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.guidance_scale_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls how closely the image follows the text prompt. Higher values produce images that more closely follow the prompt.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制影像跟隨文字提示的緊密程度。較高的值會產生更密切遵循提示的影像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.image_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The image to edit. Must be a valid PNG file, less than 4MB, and square. If mask is not provided, image must have transparency, which will be used as the mask.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要編輯的影像。必須是有效的 PNG 檔案、小於 4MB 且為正方形。如果未提供遮罩，影像必須具有透明度，以做為遮罩。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.image_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The base image to modify when using image-to-image (i.e. text + image = image) generation mode.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用影像到影像 (即文字 + 影像 = 影像) 產生模式時要修改的基本影像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.image_format_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The file format for the generated output image (PNG or JPG).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所產生輸出影像的檔案格式 (PNG 或 JPG)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.instructions_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instructions for the model to follow when generating audio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生音訊時要遵循的模型指示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.mask_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An additional image whose fully transparent areas (e.g. where alpha is zero) indicate where image should be edited. Must be a valid PNG file, less than 4MB, and have the same dimensions as image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[有個額外影像的完全透明區域 (例如 Alpha 為零的位置) 指出應編輯影像的位置。必須是有效的 PNG 檔案、小於 4MB，且維度必須與影像相同]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.max_tokens_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Limit the maximum output tokens for the model response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[限制模型回應的輸出權杖上限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.n_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of images to generate. Currently limited to 1 per request.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要產生的影像數目。目前每個請求限制為 1。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.n_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of images to generate. Currently limited to 10 per request.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要產生的影像數目。目前每個要求限制為 10。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.negative_prompt_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text describing what you want to avoid in the generated image. Use this to exclude unwanted elements, styles, or themes from your results.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述您希望在產生的影像中避免的內容之文字。使用此功能從結果中排除不想要的元素、風格或主題。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.num_inference_steps_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of denoising steps (higher = more detail but slower)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[去噪步驟數目 (較高 = 更加詳細但速度較慢)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.output_compression_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Control the compression level for the generated image. Higher values (100) mean less compression, while lower values reduce file size with some quality loss.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制所產生影像的壓縮層級。值越高 (100)，壓縮層級越低，而值越低，檔案大小越小，但品質會有些下降。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.output_format_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose the format for the generated image. PNG supports transparency and lossless quality, while JPEG offers smaller file sizes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選擇所產生影像的格式。PNG 支援透明度與無損失品質，而 JPEG 則提供較小的檔案大小。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.presence_penalty_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discourages the model from repeating the same words or phrases too frequently by applying a penalty (between -2.0 and 2.0) based on their presence in the text.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據在文字中的目前狀態，透過套用懲罰 (介於 -2.0 和 2.0 之間)，避免模型太頻繁地重複相同的單字或片語。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.presence_penalty_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reduce the chance of repeating any token that has appeared in the text at all so far. This increases the likelihood of introducing new topics in a response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[減少目前文字中出現的任何權杖重複的機會。這會提高回應中推出新主題的可能性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.prompt_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Improve transcription accuracy by providing a list of known phrases, such as names of people or specific locations. Use commas or semicolons to separate each value in the phrase list.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供已知片語清單，例如人員名稱或特定位置，以改善謄寫正確性。使用逗號或分號分隔片語清單中的每個值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.quality_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the quality and level of detail in the generated image. 'high' produces more detailed images but takes longer to generate.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制生成影像的質量和細節水平。「高」會產生更詳細的影像，但生成所需的時間較長。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.quality_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the quality and level of detail in the generated image. 'HD' produces more detailed images but takes longer to generate.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制生成影像的質量和細節水平。「HD」會產生更詳細的影像，但生成所需的時間較長。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.quality_description_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the quality and level of detail in the generated image. 'High' produces more detailed images but takes longer to generate.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制生成影像的質量和細節水平。「高」會產生更詳細的影像，但生成所需的時間較長。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.reasoning_effort_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Adjust the model's cognitive load with options for low, medium, and high reasoning levels.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用低、中和高推理層級的選項來調整模型的認知負載。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.repetition_penalty_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The weight of penalty for repeated phrases. Higher values will suppress repeating similar phrases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重複片語的懲罰權數。較高的值會抑制重複的類似片語。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.response_format_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The output format of the generated audio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所產生音訊的輸出格式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.return_full_text_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Whether or not to return the full text including the original query or just the completion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否要傳回包含原始查詢的全文，或僅傳回完成內容。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.rows_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of rows to return]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要傳回的資料列數目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.seed_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the randomness of generation. Using the same seed value will produce similar results for identical prompts. Set to 0 for random results each time.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制產生的隨機性。使用相同的種子值會為相同的提示產生類似的結果。每次隨機的結果設為 0。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.size_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The dimensions of the generated image in pixels (width × height).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所產生影像的尺寸，以像素為單位 (寬度 x 高度)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.size_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The dimensions of the generated image in pixels (width × height). Default is 1024 x 1024.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所產生影像的尺寸，以像素為單位 (寬度 x 高度)。預設值為 1024 x 1024。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.speed_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The speed of the generated audio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所產生音訊的速度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.stop_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Force cutting the output when this string occurs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[發生此字串時，強制截取輸出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.strength_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Often referred to as denoising, Strength controls how much the input image has on the generated output image. Values closer to 0 generate an output image closer to the input image. Values closer to 1 generate an output image more influenced by the model and text prompt applied to the input image.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[強度]5D; 通常稱為去噪，[强度]5D; 會控制輸入影像對所生成輸出影像影響的程度。接近 0 的值會產生與輸入影像較爲相似的輸出影像。接近 1 的值則會使輸出影像更受套用在輸入影像的模型和文字提示所影響。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.style_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the visual style of the image. 'Vivid' creates hyper-real and dramatic images, while 'Natural' creates more realistic images with less exaggeration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制影像的視覺風格。「生動」會創造出超真實且戲劇性的影像，而「自然」則會生成更真實且不過於誇張的影像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.temperature_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls randomness in the response, use lower to be more deterministic.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制回應中的隨機性，使用較低的值來提高確定性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.text_guidance_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls how closely the image follows the text prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制影像跟隨文字提示的緊密程度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.top_k_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of highest probability vocabulary tokens to keep for top-k-filtering, defaults to null.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對 top-k 篩選所保留，可能性最高的詞彙基元數，預設為 null。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.top_k_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of highest probability vocabulary tokens to keep for top-k-filtering.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[針對 top-k 篩選所保留，可能性最高的詞彙基元數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.top_p_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls text diversity by selecting the most probable words until a set probability is reached.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過選取可能性最高的字組，直到達到設定的可能性，以控制文字多樣性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.top_p_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls data diversity by selecting the most probable words until a set probability is reached.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過選取可能性最高的單字，直到達到設定的可能性，以控制資料多樣性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.video_duration_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The duration of the generated video.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所產生視訊的持續時間。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.video_height_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The height of the generated video.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所產生視訊的高度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.video_variations_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of variations to generate.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要產生的變化數目。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.video_width_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The width of the generated video.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所產生視訊的寬度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.voice_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The voice to use for the generated audio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要用於所產生音訊的聲音。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.answer_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Answer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[回答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.audio_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Speech to Text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語音轉換文字]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.chatHistory_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat History]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[交談記錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.chatHistory_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Past messages included]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包含過去的訊息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.context_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Context]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[內容]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.do_sample_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do Sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[執行樣本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.document_url_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Document]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.file_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.formatting_reenabled_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Formatting re-enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已重新啟用格式化]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.frequency_penalty_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Frequency Penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[頻率懲罰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.frequency_penalty_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Frequency penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[頻率懲罰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.generate_summary_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate Summary]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.generatedImage_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生的影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.generated_image_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Result]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.generated_video_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated video result]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生的視訊結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.guidance_scale_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Guidance Scale]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指導級別]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_features_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image Features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_format_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸出格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zero shot image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零樣本影像分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Original image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[原始影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Frontal Image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正面影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.include_image_base64_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Include Image Base64]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包含影像 Base64]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.instructions_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instructions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.jsonInput_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.labels_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Labels]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.language_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Language]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.mask_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mask]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[遮罩]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.mask_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mask image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[遮罩影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.max_completion_tokens_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max Completion Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成語彙基元數目上限]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.max_new_tokens_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max New Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新語彙基元數上限]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.max_tokens_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權杖數上限]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.model_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.n_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of Images]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像數量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.n_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of variations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[變化數目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.negative_prompt_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Negative Prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[負面提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.negative_prompt_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Negative prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[負面提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.num_inference_steps_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Diffusion Steps]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擴散步驟]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.output_compression_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compression Level]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[壓縮層級]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.output_format_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image Format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.output_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Findings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.presence_penalty_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Presence Penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目前狀態懲罰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.presence_penalty_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Presence penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存在懲罰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.prompt_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phrase list]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[片語清單]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.prompt_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.quality_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[品質]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.question_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字轉換影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.question_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[問題]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.question_friendlyName_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Query]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查詢]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.reasoning_effort_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning Effort]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推理能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.repetition_penalty_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Repetition Penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重複懲罰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.response_format_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Response format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[回應格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.return_full_text_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Return Full Text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[傳回全文]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.revised_prompt_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Revised Prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已修訂的提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.rows_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料列]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.seed_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Seed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[種子]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.size_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.size_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image Size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.speed_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Speed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[速度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.stop_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.strength_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Strength]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[強度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.style_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Style]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[樣式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.temperature_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Temperature]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[溫度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.text_features_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text Features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.text_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Transcription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[謄寫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.text_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產生的文字]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.text_friendlyName_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Indication]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.top_k_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Top K]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Top K]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.top_p_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Top P]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Top P]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_duration_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Duration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[持續時間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_format_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸出格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_height_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Height]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_variations_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video to text classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視訊轉換文字分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_width_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Width]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[寬度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.voice_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Voice]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語音]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.options.quality_high_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[High]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.options.quality_low_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[低]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.options.quality_medium_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Medium]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
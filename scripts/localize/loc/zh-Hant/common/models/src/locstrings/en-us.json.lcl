﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="d:\a\1\s\common\models\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-TW" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";AOAILearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Azure OpenAI availability]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Azure OpenAI 可用性]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Learn more about Azure OpenAI Service availability]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAINotSignedUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This subscription is not enabled for Azure OpenAI yet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[尚未針對 Azure OpenAI 啟用此訂用帳戶]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This subscription is not enabled for Azure OpenAI Service yet]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAIRequest" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request access to Azure OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要求存取 Azure OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Request access to Azure OpenAI Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAISignUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use of Azure OpenAI models in Azure Machine Learning requires Azure OpenAI resources. This subscription or region does not have access to this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure Machine Learning 中使用 Azure OpenAI 模型需要 Azure OpenAI 資源。此訂用帳戶或區域沒有此模型的存取權。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use of Azure OpenAI Service models in Azure Machine Learning requires Azure OpenAI Service resources. This subscription or region does not have access to this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAISignUpInvalidRegion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is not available in your workspace's region. To use Azure OpenAI from a different region, you will need to create an Azure OpenAI connection manually. {learnMore}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 不適用於您工作區的區域。若要使用不同區域的 Azure OpenAI，您必須手動建立 Azure OpenAI 連線。{learnMore}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is not available in your workspace's region. To use Azure OpenAI Service from a different region, you will need to create an Azure OpenAI Service connection manually. {learnMore}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAISignUpInvalidRegionWithLocation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is not available in your workspace's region ({location}). To use Azure OpenAI from a different region, you will need to create an Azure OpenAI connection manually. {learnMore}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 不適用於您工作區的區域 ({location})。若要使用不同區域的 Azure OpenAI，您必須手動建立 Azure OpenAI 連線。{learnMore}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is not available in your workspace's region ({location}). To use Azure OpenAI Service from a different region, you will need to create an Azure OpenAI Service connection manually. {learnMore}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAISignUpWithSub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use of Azure OpenAI models in Azure Machine Learning requires Azure OpenAI resources. This subscription ({subscription}) or region ({location}) does not have access to this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure Machine Learning 中使用 Azure OpenAI 模型需要 Azure OpenAI 資源。此訂用帳戶 ({subscription}) 或區域 ({location}) 沒有此模型的存取權。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use of Azure OpenAI Service models in Azure Machine Learning requires Azure OpenAI Service resources. This subscription ({subscription}) or region ({location}) does not have access to this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AnnouncementButtonAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Announcements carousel button ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[宣告浮動切換按鈕]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AnnouncementCarouselNext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to next announcement]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移至下一個公告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AnnouncementCarouselPrevious" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to previous announcement]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[前往上一個公告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Exclusively available on {Azure}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 {Azure} 上專屬提供]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{GPT4o}, the most advanced multimodal model from OpenAI, now available on Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{GPT4o} 是 OpenAI 最進階的多模態模型，現已在 Azure 上推出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{AzureOpenAI} language models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{AzureOpenAI} 語言模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{GPT4o} is now available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{GPT4o} 現已可供使用!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere's Enterprise AI models include Command R, Command R+, and Embed v3.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere 的企業 AI 模型包括 Command R、Command R+和 Embed v3。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Cohere's Enterprise AI models including Command R/R+ and Embed.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Cohere} models are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Cohere} 模型在這裡!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Core42.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Jais} from {Core42}, available first on Azure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Core42} 的 {Jais}，可先在 Azure 上使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Core42.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Jais} from {Core42} now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Core42} 的 {Jais} 現在已可使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Deci.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing {DeciLM}, {DeciCoder} and {DeciDiffusion}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{DeciLM}、{DeciCoder} 和 {DeciDiffusion} 簡介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Deci.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{DeciAI} Generative AI models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{DeciAI} 生成式 AI 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Falcon.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trained by {tii}, hosted by {HuggingFace}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 {tii} 訓練，{HuggingFace} 裝載]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Falcon.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trained by {tii}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 {tii} 訓練]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Falcon.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing {Falcon} on {Azure}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Falcon} on {Azure} 簡介]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Introducing {Falcon} on Azure]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trained by {Meta}, hosted by {AzureAI}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 {Meta} 定型、由 {AzureAI} 裝載]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Trained by {Meta}, hosted by {AML}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go inference APIs and hosted fine-tuning for {LLaMA} models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於 {LLaMA} 模型的隨用隨付推斷 API 和託管式微調]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless APIs for Meta-Llama-3-8B-Instruct and Meta-Llama-3-70B-Instruct models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Meta-Llama-3-8B-Instruct 和 Meta-Llama-3-70B-Instruct 模型的無伺服器 API]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Serverless APIs for {LlaMA3} 8b and 70b text generation and chat completion models]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing {LLaMA}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推出 {LLaMA}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Say hello to {LLaMA}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{LLaMA} APIs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{LLaMA} API]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build the future of AI with {Meta} {LlaMA3}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 {Meta} {LlaMA3} 建置 AI 的未來]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Main.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore and test large AI models from Microsoft, Azure OpenAI, Meta, and Hugging Face to find the right one for your use case.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索及測試來自 Microsoft、Azure OpenAI、Meta 和 Hugging Face 的大型 AI 模型，以尋找適合您使用案例的模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Explore and test large AI models from Microsoft, Azure OpenAI Service, Meta, and Hugging Face to find the right one for your use case.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Main.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Welcome to model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[歡迎使用模型目錄]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Welcome to model library]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{MistralLarge}, the best Mistral model yet, available first on {Azure}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{MistralLarge} 是目前最佳的 Mistral 模型，搶先在 {Azure} 上線]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral AI's smallest yet highly efficient model, now available on {Azure}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral AI 最小但高效率的模型現在已於 {Azure} 推出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{MistralLarge} is now available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{MistralLarge} 已可使用!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{MistralSmall} is now available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{MistralSmall} 現已推出!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nixtla.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TimeGEN-1 the timeseries forecasting model, available first on Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TimeGEN-1 時間序列預測模型，首先在 Azure 上提供]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[{Nixtla} models available first on {Azure}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nixtla.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla's TimeGEN-1 is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla's TimeGEN-1 推出了!]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[{Nixtla} models are here!]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Production-ready models, optimized for performance, hosted by {AzureAI}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生產環境就緒模型，針對效能的最佳化，由 {AzureAI} 裝載]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{NVIDIA} AI foundation models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{NVIDIA} AI 基礎模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft's Phi-3-mini SLMs offer groundbreaking performance at a small size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的 Phi-3-mini SLM 尺寸雖小卻能提供突破性的效能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft's latest {Phi3} SLMs offer groundbreaking performance at a small size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 最新 {Phi3} SLM 以小尺寸提供突破性的效能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Phi3} is now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Phi3} 現已可供使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Phi3} Serverless APIs are now available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Phi3} 無伺服器 API 現已可供使用!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{StabilityAI} models available first on {Azure}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{StabilityAI} 模型可先在 {Azure} 上使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{StabilityAI} models are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{StabilityAI} 模型在這裡!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Vision.CollectionName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OSS vision]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OSS 視覺]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Vision.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Popular OSS vision models hosted by {AzureAI}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 {AzureAI} 裝載的熱門 OSS 視覺模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Popular OSS vision models hosted by {AML}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Vision.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover vision models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索視覺模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AoaiModelAvailability.CreateANewAoaiConnectionLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connecting Azure OpenAI resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在連線 Azure OpenAI 資源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Connecting Azure OpenAI Service resources]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AoaiModelAvailability.DeployDisabledFeatureFlagMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registration is required to use this model. {RequestAccess}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要註冊才能使用此模型。{RequestAccess}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AoaiModelAvailability.DeployDisabledMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model is not yet available for deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型尚無法用於部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AoaiModelAvailability.LearnHowToConnectAResource" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn how to connect a resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解如何連接資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AoaiModelAvailability.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AoaiModelAvailability.NoModelAccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This project doesn't have access to {ModelName}. {LearnMore}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此專案無法存取 {ModelName}。{LearnMore}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This project doesn't have access to {ModelName}. {RequestAccess}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AoaiModelAvailability.NoModelAccessMessageGeneric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You currently do not have a connected Azure OpenAI resource, which is required to access this model. {LearnMore}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您目前沒有連線的 Azure OpenAI 資源，存取此模型需要此資源。{LearnMore}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[You currently do not have a connected Azure OpenAI Service resource, which is required to access this model. {LearnMore}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AoaiModelAvailability.NotAvailableMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This subscription does not have access to Azure OpenAI. {RequestAccess}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這個訂用帳戶沒有 Azure OpenAI 的存取權。{RequestAccess}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This subscription does not have access to Azure OpenAI Service. {RequestAccess}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AoaiModelAvailability.RequestAccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要求存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Artifacts.ErrorResponseMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unknown error downloading file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載檔案時發生未知的錯誤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Artifacts.ErrorResponseTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AzureDirectIconAlt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Direct from Azure icon]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[直接來自 Azure 圖示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AzureDirectTooltipTemplate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Direct from Azure are models hosted and sold by Microsoft under {microsoftProductTerms}. {learnMore}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[直接來自 Azure 是 Microsoft 依據 {microsoftProductTerms} 所託管和銷售的模型。{learnMore}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AzureOpenAiService" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BillingTypesTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Billing types]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[帳單類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CapabilityFilters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Capabilities]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogFilterGroupHeaderLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{group} (choose one)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{group} (選擇一個)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelFilterColumns.Deployable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployable]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelFilterColumns.ModelFineTuneRetirementDate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Finetune retirement date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微調淘汰日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelFilterColumns.ModelId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelFilterColumns.ModelInferenceRetirementDate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inference retirement date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推斷淘汰日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelFilterColumns.ModelLifecycleStatus" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Lifecycle]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生命週期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelFilterColumns.ModelMaxContextTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max Context Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大內容權杖。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelFilterColumns.ModelMaxOutputToken" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max Output Token]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大輸出權杖]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.AOAIHover" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customers of Azure OpenAI can access prompt engineering, fine-tuning, evaluation, and deployment capabilities for OpenAI large language models available in Azure OpenAI. {learnMore}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 的客戶可以存取 Azure OpenAI 中提供的 OpenAI 大型語言模型的提示工程、微調、評估和部署功能。{learnMore}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Customers of Azure OpenAI Service can access prompt engineering, fine-tuning, evaluation, and deployment capabilities for OpenAI large language models available in Azure OpenAI Service. {learnMore}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.AOAILearnMoreAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Azure OpenAI catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Azure OpenAI 目錄]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Learn more about Azure OpenAI Service catalog]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.AllFilters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All filters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所有篩選]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.AzureMLHover" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open source third-party models packaged in MLflow format and optimized for AzureML. Guided wizards and composable pipelines to evaluate, fine-tune, and deploy models with your data. {learnMore}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以 MLflow 格式封裝並針對 AzureML 最佳化的開放原始碼第三方模型。引導式精靈和可組合管線，以使用您的資料進行評估、微調和部署模型。{learnMore}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Open source third-party models packaged in MLflow format and optimized for AzureML. Guided wizards and composable pipelines to evaluate, finetune, and deploy models with your data. {learnMore}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.AzureMLLearnMoreAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about AzureML catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 AzureML 目錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.BenchmarkResults" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Benchmark results]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基準結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.Benchmarks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基準]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.ClearAllFilters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clear all]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[全部清除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.CommunityPartner" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Community partner]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[社群合作夥伴]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.DirectFromAzure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Direct from Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[直接來自 Azure]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.ExclusivelyOnAzure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Exclusively on Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[專屬於 Azure]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.HideFilters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hide]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隱藏]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.HuggingFaceHover" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover and deploy models from Hugging Face to AzureML online endpoints for real time inferencing. {learnMore}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索並部署模型，從 Hugging Face 到 AzureML 線上端點，以進行即時推斷。{learnMore}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy models from HuggingFace hub to AzureML online endpoints for real time inference. Supported by HuggingFace: {discussion}. {learnMore}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.HuggingFaceLearnMoreAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Hugging Face catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Hugging Face 目錄]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Learn more about HuggingFace catalog]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.IconAltTemplate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Icon for {iconName}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{iconName} 的圖示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.LessFilters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Less]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[較少]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.MoreFilters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[其他]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Catalogs.OpenSourceModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Source Models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開放原始碼模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.AoaiSDK" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI SDK]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI SDK]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.AuthenticationType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[驗證類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.CopiedApiKey" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Api Key Copied]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已複製 Api 金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.CopiedEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint Copied]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已複製端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.CopyApiKey" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy Api Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[複製 Api 金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.CopyEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[複製端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.DeploymentType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment option]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署選項]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.Inferencing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Inference SDK]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 推斷 SDK]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.Language" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Language]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.NoCodeSamplesDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please check out the Azure inference documentation for additional reference.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請參閱 Azure 推斷文件以進一步參考。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.NoCodeSamplesTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No code samples found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到任何程式碼範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.NoCodeSamplesViewDocumentation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View Documentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSamples.SDK" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SDK]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SDK]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Collection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Collection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[集合]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Collections" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Collections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[集合]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Columns.Author" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Author]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Columns.InferenceTasks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inference tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推斷工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Columns.Library" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Library]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[程式庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Columns.License" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[License]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[授權]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Columns.Publisher" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Publisher]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[發行者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Columns.Source" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Source]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Columns.Task" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Columns.Type" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CuratedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Curated by {collection}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 {collection} 策展]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployBlockedOnModelDeprecation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model cannot be deployed because it has been deprecated.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法部署此模型，因為它已被取代。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel.Confirm" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[確認]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel.ModelCatalog" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to model catalog.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移至模型目錄。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel.Next" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下一頁]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel.ReviewSelection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下一頁]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Review selection]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel.Subtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model that you want to deploy. This can be consumed as a base model or used inside a flow.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取要部署的模型。這可以作為基本模型使用，也可以在流程中使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel.Subtitle2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose a model to create a new deployment. For flows and other resources, create a deployment from their respective list.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選擇模型以建立新的部署。針對流程和其他資源，從其個別清單建立部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel.Subtitle3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose a model to create a new deployment. For flows and other resources, create a deployment from their respective list. {fullCatalog}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選擇模型以建立新的部署。針對流程和其他資源，從其個別清單建立部署。{fullCatalog}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.ACIComputeType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Container Instance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 容器執行個體]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ACI]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.AKSComputeType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AksCompute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AksCompute]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure Kubernetes Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.AddFileText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add File]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.CloseButtonAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關閉]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.DefaultPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[default]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預設]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.DeploymentFailure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment failure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署失敗]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.DeploymentInProgressMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploying model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.DeploymentStartedSuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{deploymentName} deployment started successfully]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功啟動 {deploymentName} 部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.DupserviceName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Current workspace already contains a deployment with name {deploymentName}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目前的工作區已包含名稱為 {deploymentName} 的部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeployCpuReserveCapacityInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of CPU cores to allocate for this web service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要為此 Web 服務配置的 CPU 核心數目。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeployCpuReserveCapacityLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CPU reserve capacity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CPU 保留容量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeployCpuReserveCapacityTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding CPU reserve capacity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下可顯示有關於 CPU 保留容量的資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeployKeyBasedAuthInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Keys can be found on the endpoint details page.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[端點詳細資料頁面上可以找到金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeployMemoryReserveCapacityInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The amount of memory (in GB) to allocate for this web service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要為此 Web 服務配置的記憶體 (GB) 數量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeployMemoryReserveCapacityLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Memory reserve capacity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[記憶體保留容量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeployMemoryReserveCapacityTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Memory reserve capacity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下可顯示有關於記憶體保留容量的資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploySSLCNAME" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SSL Domain Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SSL 網域名稱]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[SSL CNAME]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploySSLCertificatePlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select ssl certificate pem file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 SSL 憑證 pem 檔案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[select ssl certificate pem file..]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploySSLKeyPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select ssl key pem file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 SSL 金鑰 pem 檔案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[select ssl key pem file..]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploySSLLeafDomainLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Leaf Domain Label]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分葉網域標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploySSLLeafDomainTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The DNS name label for the scoring endpoint. If not specified a unique DNS name label will be generated for the scoring endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評分端點的 DNS 名稱標籤。若未指定，就會為評分端點產生唯一的 DNS 名稱標籤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploySSLLeafDomainTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding leaf domain label.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於分葉網域標籤的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAciEnableSSL" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable SSL]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用 SSL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAciEnableSSLLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read more about using SSL with Azure Machine Learning ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解如何在 Azure Machine Learning 使用 SSL ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAciEnableSSLTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding enabling SSL.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於如何啟用 SSL 的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAdvancedSettingsLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[進階]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAppsInsightEnabledLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable Application Insights diagnostics and data collection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用 Application Insights 診斷及資料收集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAppsInsightsText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read more about Application Insights ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Application Insights ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAppsInsightsTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Application Insights.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於 Application Insights 的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAssetsFileCustom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use assets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用資產]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAssetsFileEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用環境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAutoScaleEnabledInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Whether to enable autoscaling for the web service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否為 Web 服務啟用自動調整。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAutoScaleEnabledLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Auto scale enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已啟用自動調整規模]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentAutoScaleEnabledTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Auto scale enabled.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下可顯示有關於已啟用自動調整規模的資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentCancelButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentComputeSelectionNameInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose an Inferencing Cluster for deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選擇要部署的推斷叢集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentComputeSelectionNameLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算名稱]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Name]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentComputeSelectionNameTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding compute selection.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於計算選項的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentComputeTypeLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[計算類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentComputeTypeLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read more about choosing a compute target.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解如何選擇計算目標。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentComputeTypeTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding compute type.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於計算類型的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentCondaDependenciesInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specifies which pip and conda packages your webservice depends on. {condaLinkText}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指定網路服務仰賴的 PIP 與 Conda 套件。{condaLinkText}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentCondaDependenciesLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Conda dependencies file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Conda 相依性檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentCondaDependenciesPlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select conda dependencies file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取 Conda 相依性檔案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[select conda dependencies file..]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentCondaDependenciesText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read more about environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解環境。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentCondaDependenciesTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding conda dependencies file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於 conda 相依性的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentDataCollectionLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable Model Data Collector (legacy)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用模型資料收集器 (舊版)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentDependenciesLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相依性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentDeployButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentDescriptionInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to help other users of your workspace understand what your model does.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[協助工作區其他使用者了解您模型用途的文字。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentDescriptionLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentDescriptionTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Description.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於描述的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentEnableAuthenticationLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable authentication]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[啟用驗證]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentEntryScriptFilePlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select entry script file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取輸入腳本檔案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[select entry script file..]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentEntryScriptInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{entryScriptLinkText} accepts requests, scores the requests by using the model, and returns the results.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{entryScriptLinkText} 接受要求，使用模型為要求評分，並傳回結果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentEntryScriptLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Entry script file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入腳本檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentEntryScriptText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Entry script]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入腳本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentEntryScriptTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Entry Script File.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下可顯示有關於輸入腳本檔案的資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentEnvironmentsInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Make sure your environment includes azureml-defaults package]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請確定您的環境包含 azureml-defaults 套件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentExistingEnvironments" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Existing environments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[現有環境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentExistingRealTimeEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Existing real-time endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[現有的即時端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentKeyInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provide your own key for users to authenticate to your endpoint. Keys do not expire and are transferable. If you don't provide a key, we will generate one for you.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請提供您自己的金鑰，好讓使用者向您的端點驗證。金鑰必須未過期且可移轉。若您未提供金鑰，我們會為您產生一個金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentLoadingDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在載入部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentMaxConcurrentRequestsLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max concurrent requests per container]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每個容器的最大並行要求]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentMaxReplicasInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The maximum number of containers to use when autoscaling this web service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自動調整此 Web 服務時，要使用的容器數目上限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentMaxReplicasLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max replicas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大複本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentMaxReplicasTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Max replicas.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下可顯示有關於最大複本數的資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentMinReplicasInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The minimum number of containers to use when autoscaling this web service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自動調整此 Web 服務時，要使用的容器數目下限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentMinReplicasLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Min replicas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最小複本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentMinReplicasTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Min replicas.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下可顯示有關於最小複本數的資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentModelsLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentNCDText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[no-code deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[非程式碼部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentNameInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This will show up under the “Endpoints” tab once your model is deployed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這會在模型部署完成後，顯示在 [端點]5D; 索引標籤下。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentNameLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentNameTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding name.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於名稱的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentNewRealTimeEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy new real-time endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署新的即時端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentNoCodeDeploymentInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model supports {noCodeDeploymentLinkText}. You may {optionallyBoldText} override the default environment and driver file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型支援 {noCodeDeploymentLinkText}。您可以 {optionallyBoldText} 覆寫預設環境與驅動程式檔案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentNumberOfReplicasInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of replicas of your webservice. Make this a higher number for better availability. If all replicas go down, your service will be temporarily unavailable while we spin up a new replica. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的網路服務複本數。請提高數量，以獲得更佳的可用性。若所有複本都無法使用，則在我們產生新複本時，您的服務將暫時無法使用。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentNumberOfReplicasLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of replicas]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[複本數目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentNumberOfReplicasTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding number of replicas.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於複本數目的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentOptionally" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[optionally]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選擇性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentOverrideNCDLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use custom deployment assets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用自訂部署資產]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentPrimaryKeyLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Primary key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[主要金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentPrimaryKeyTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding primary key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於主要金鑰的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentRefreshPeriodInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How often (in seconds) the autoscaler attempts to scale this web service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自動調整器嘗試調整此 Web 服務的頻率 (秒)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentRefreshPeriodLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh period]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理間隔]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentRefreshPeriodTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Refresh period.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下可顯示有關於重新整理間隔的資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentReplaceRealTimeEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Replace an existing real-time endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取代現有的即時端點]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentSSLCNAMELinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read more about getting a domain name.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解如何取得網域名稱。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentSSLCNAMETooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding getting a domain name.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於網域名稱的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentSSLCertificateLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SSL Certificate PEM file:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SSL 憑證 PEM 檔案:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentSSLCertificateLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read more about getting a certificate and key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解如何取得憑證與金鑰。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentSSLCertificateTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding getting a certificate and key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於憑證與金鑰的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentSSLKey" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SSL Key PEM File:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SSL 金鑰 PEM 檔案:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentScoringTimeoutInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A timeout in milliseconds to enforce for scoring calls to the web service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要對 Web 服務評分呼叫強制套用的逾時秒數 (毫秒)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentScoringTimeoutLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scoring timeout]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[評分逾時]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentScoringTimeoutTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Scoring timeout.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下可顯示有關於評分逾時的資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentSecondaryKeyLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Secondary key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[次要金鑰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentSecondaryKeyTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding secondary key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於次要金鑰的按一下以取得資訊按鈕。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentServiceNameValidationFailure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error validating service name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[驗證服務名稱時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentServiceResourceValidationFailure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error validating service resource requirements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[驗證服務資源需求時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentTargetUtilizationInfoText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The target utilization (in percent out of 100) that the autoscaler should attempt to maintain for this web service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自動調整器應嘗試為此 Web 服務維持的目標使用率 (以百分比為單位，最高 100)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentTargetUtilizationLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Target utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目標使用率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentTargetUtilizationTooltipAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click for info button regarding Target utilization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按一下可顯示有關於目標使用率的資訊按鈕]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentTypeText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentUnknownError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FormDeploymentUnknownError]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FormDeploymentUnknownError]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentUnknownErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unknown error occurred for deployment {deploymentName}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署 {deploymentName} 發生未知的錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentUserError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[UserError]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[UserError]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.FormDeploymentYouMay" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You may]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.KeyBasedAuthenticationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key-based authentication]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金鑰式驗證]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Key-based Authentication]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.LoadingComputesGeneralError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while fetching computes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取計算時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.LoadingDeploymentGeneralError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while fetching deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取部署時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.ModelDeployPanelHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.QuickDeployPrivateLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This feature is unavailable if the workspace is private link enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果工作區已啟用私人連結，就無法使用此功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.RemoveButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Remove]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.ServiceDeploymentGeneralError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while deploying service]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署服務時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.SuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Success]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.TokenBasedAuthenticationText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token-based authentication]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權杖式驗證]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Token-based Authentication]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.ComputeNameMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute name is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要計算名稱]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Required]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.ComputeTypePermissionError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You lack sufficient permissions to create this resource type. Please ensure you have Contributor or Owner access to the workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您的權限不足，無法建立此資源類型。請確定您有工作區的參與者或擁有者存取權。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.ComputeTypeRequired" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ Compute type required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ 需要計算類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.CondaDependenciesMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Conda dependencies file is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要 Conda 相依性檔案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Required]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.DriverFileMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Entry script file is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要輸入腳本檔案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Required]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.EnvironmentReferenceMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environment is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要環境]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Required]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.ExistingDeploymentMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Existing deployment required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要現有的部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.KeyValidation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Must be between 10 and 63 characters and must begin with alphanumerics. May include dashes, underscores, dots, and alphanumerics after.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[必須介於 10 到 63 個字元之間，且必須以英數字元開頭。可在之後包含虛線、底線、點及英數字元。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.MaximumScoringValue" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Maximum allowed value is 300000]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[允許的最大值為 300000]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.MinimumScoringValue" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Minimum allowed value is 5]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[允許的最小值為 5]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.PrimaryKeyAndSecondaryKeyMatch" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Primary key should not be the same as Secondary key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[主要金鑰不應與次要金鑰相同]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.SSLCNameMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SSL domain name is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要 SSL 網域名稱]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Required]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.SSLCertificateMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SSL certificate file is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要 SSL 憑證檔案]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Required]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.SSLKeyMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SSL key is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要 SSL 金鑰]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Required]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.ServiceNameInvalidCharacters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must only consist of lowercase letters, numbers, or dashes, start with a letter, end with a letter or number]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱只可包含小寫字母、數字或虛線，且以字母開頭，以字母或數字結尾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.ServiceNameInvalidLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name must be between 3 and 32 characters long.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此名稱長度必須介於 3 到 32 個字元之間。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.ServiceNameMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要名稱]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Required]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployment.Create.Validation.ValidNumber" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Value must a valid number.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[值必須是有效數字。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentBlockedByAdminPolicy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model cannot be deployed due to a policy in your subscription. Contact your admin to request access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您訂用帳戶中的原則不允許部署此模型。連絡您的系統管理員以要求存取權。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.AzureDirectCardBody" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This option is offered by Microsoft and does not require you to host or manage infrastructure. You can choose to use your PTU quota for this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此選項由 Microsoft 提供，不需要您託管或管理基礎結構。您可以選擇使用此模型的 PTU 配額。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.AzureDirectCardHeading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Direct from Azure models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[直接來自 Azure 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.FDPSupportedCardBody" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy flagship models from the model catalog, including Azure OpenAI, Cohere, DeepSeek, Mistral AI, Meta Llama, Phi, and A21 labs models to connected Azure AI Foundry resources.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將模型目錄中的旗艦模型 (包括 Azure OpenAI、Cohere、DeepSeek、Mistral AI、Meta Llama、Phi 和 A21 實驗室模型) 部署至已連線的 Azure AI Foundry 資源。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.FDPSupportedCardHeading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Foundry models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Foundry 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ManagedComputeCardBody" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This option offers customer-managed hosting and model inferencing on Azure compute. Customer must manually configure Azure AI Content Safety to mitigate the risk of exposing users to harmful content.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此選項提供在 Azure 計算上的客戶自控裝載和模型推斷。客戶必須手動設定 Azure AI 內容安全，以降低使用者暴露於有害內容的風險。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ManagedComputeCardHeading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed Compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受控計算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ManagedComputeWithoutSafetyCardBody" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This option offers user-managed hosting and model inferencing on Azure infrastructure. It doesn't use Azure AI Content Safety filters and you may be at higher risk of exposing users to harmful content.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此選項提供在 Azure 基礎結構上的使用者管理裝載和模型推斷。它不會使用 Azure AI 內容安全篩選，而且您可能會處於較高的風險，讓使用者暴露在有害內容中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ManagedComputeWithoutSafetyCardHeading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed Compute without Azure AI Content Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不需要 Azure AI 內容安全的受控計算]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy without Azure AI Content Safety]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ServerlessAPI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless API]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無伺服器 API]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ServerlessAPIBody" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This option offers model through managed API service that does not require you to host or manage infrastructure. You can choose to include standard Azure AI Content Safety filters with this option.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此選項透過不需要您裝載或管理基礎結構的受控 API 服務提供模型。您可以選擇使用此選項，將標準 Azure AI 內容安全篩選條件包含在內。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ServerlessAPICardBody" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This option offers managed API service that does not require you to host or manage Azure infrastructure. Customers can choose to include built-in Azure AI Content Safety filters.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此選項提供不需要您裝載或管理 Azure 基礎結構的受控 API 服務。客戶可選擇包含內建 Azure AI 內容安全篩選。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This option offers managed API service that does not require you to host or manage infrastructure. You can choose to include standard Azure AI Content Safety filters with this option.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ServerlessAPICardHeading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless API]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無伺服器 API]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Serverless API with Azure AI Content Safety]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ServerlessProvisionedThroughput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioned throughput (PTU) with Azure AI Content Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 內容安全的佈建的輸送量 (PTU)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ServerlessProvisionedThroughputCardBody" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy with hourly or long-term commitment plan at a discount for predictable performance. You can choose to include standard Azure AI Content Safety filters with this option.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以每小時或長期承諾方案進行部署，以享有可預測效能的折扣。您可以選擇使用此選項，將標準 Azure AI 內容安全篩選條件包含在內。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ServerlessStandard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard (Pay-go) with Azure AI Content Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI 內容安全的標準 (隨用隨付)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.ServerlessStandardCardBody" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy with pay-as-you-go tokens. No need to host or manage infrastructure. You can choose to include standard Azure AI Content Safety filters with this option.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用隨用隨付權杖部署。不需要裝載或管理基礎結構。您可以選擇使用此選項，將標準 Azure AI 內容安全篩選條件包含在內。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentOptionsDialog.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Purchase options]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[購買選項]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deployment options]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentTypes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment options]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署選項]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentTypesTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless API is a managed API service with pay-as-you-go billing. It does not require you to host or manage Azure infrastructure. The Managed compute option offers more control via user-managed hosting and inferencing on Azure infrastructure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無伺服器 API 是具有隨用隨付計費的受管理 API 服務。您不需要裝載或管理 Azure 基礎結構。受控計算選項可透過 Azure 基礎結構上的使用者自控裝載和推斷，提供更多控制權。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DescriptionCard.EditButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit button]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[編輯按鈕]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DescriptionCard.NoData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有任何資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Ellipsis" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[…]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EmptyModelState.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To see more results, try modifying your search. Can’t find the model you’re looking for?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要查看更多結果，請嘗試修改您的搜尋。找不到您要找的模型嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EmptyModelState.ImportModelNotebook" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Can't wait? Try {importModelNotebook}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[等不及？嘗試 {importModelNotebook}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EmptyModelState.ImportModelNotebookButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Import model notebook]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[匯入模型筆記本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EmptyModelState.RequestModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要求模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EmptyModelState.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No results found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到結果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ExploreModelsCollection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore the models collection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索模型集合]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeaturedContributors" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Featured contributors]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[精選參與者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeaturedModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Featured models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[精選模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Filters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Filter by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[篩選依據]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Filters]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FineTuningTasks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tuning tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微調工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FineTuningTasksSearchBoxAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search fine-tuning task types]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜尋微調工作類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";GoToBlog" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read blog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[閱讀部落格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Acknowledgement" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[I understand the license of the model being imported and acknowledge {Microsoft} is not responsible for breach of license terms and conditions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我了解要匯入之模型的授權，並確認 {Microsoft} 對於違反授權條款及條件恕不負責。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.AcknowledgementAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check {Microsoft} import acknowledgment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢查 {Microsoft} 匯入確認]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.ClosePanel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close import model panel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關閉匯入模型面板]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.CurrentlySupport" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Currently we only support importing from {HuggingFace}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目前我們只支援從 {HuggingFace} 匯入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download and register a model from the popular model hubs to deploy or fine-tune.​]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從熱門模型中心下載並登錄模型，以進行部署或微調。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Download and register a model from the popular model hubs to deploy or finetune.​]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Finish" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Finish]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Import" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Import]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[匯入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Location.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型位置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Location.Registry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Location.Workspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Location.WorkspaceSubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use this option to prototype and test. You can promote to registry later.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用此選項來建立原型及測試。您可以稍後升級為登錄。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Message" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Can't wait? Try the Import Model notebook]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[等不及? 嘗試匯入模型筆記本]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Can't find what you are looking for? Click here to import]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.ModelId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.ModelVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.ModelVersionPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Leave empty for latest]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[保留空白以取得最新版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Options.HuggingFace.AriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{HuggingFace} logo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{HuggingFace} 標誌]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Options.HuggingFace.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hugging Face]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擁抱的臉]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Options.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select model source]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型來源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.SelectModelSource" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select model source]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型來源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.SelectModelSourceHelpText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model hub from where you want to import a model from]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從您要匯入模型的來源選取模型中樞]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Import model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[匯入模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.WhereIsModelId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Where is the ID?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[識別碼在哪裡?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.WhereToFindModelId.AriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Example for where to find model ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[尋找模型識別碼的範例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import.WhereToFindModelId.HuggingFace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can copy the model ID's for {HuggingFace} models on the page title]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以在頁面標題上複製模型識別碼供 {HuggingFace} 模型使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";IndustryFilters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Industry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[產業]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";InferenceTasks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inference tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推斷工作]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Inference task]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";InferenceTasksSearchBoxAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search inference task types]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜尋推斷工作類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";License" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[License]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[授權]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Licenses" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Licenses]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[授權]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.FirstPartyLicenseInfo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[License information can be found on the model catalog model details page]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您可以在模型目錄模型詳細資料頁面上找到授權資訊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.Legal" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Legal]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[法律]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.LicenseAgreement" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[License agreement]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[授權合約]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.NoPriceToDisplay" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing cannot be fetched for private offers.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法針對私人供應項目擷取價格。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.PTUPriceDisclaimer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The price of the model deployment = Number of provisioned units * Hourly provisioned unit price. The number of provisioned units differs by model and is specified on the next step.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型部署的價格 = 佈建單位數目 * 每小時佈建的單價。佈建單位數目因模型而異，且會在下一個步驟中指定。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.Pricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[價格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.PrivacyPolicy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Privacy policy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隱私權原則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.SelectProjectToLoad" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a project in the overview tab to load marketplace information.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 [概觀]5D; 索引標籤中選取要載入市集資訊的專案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.ShowLess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show less]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示較少]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSDeploymentDialog.ShowMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read more…]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[閱讀更多...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSNotSupported" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless deployments are not enabled for this model in this Azure Cloud region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型在此 Azure Cloud 區域中未啟用無伺服器部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSTermsOfService.MarketplaceOfferNotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This offer is not made available by the provider in the country where your account and Azure Subscription are registered. This region can be different from the region of your project or workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您帳戶和 Azure 訂用帳戶所註冊之國家/地區中的提供者未提供此供應項目。此區域可能與您專案或工作區的區域不同。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSTermsOfService.PricingDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See the pricing details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看價格詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSTermsOfService.PricingInfo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{ModelName} use is currently priced at $0, and use is subject to rate limits which may change at any time. Pricing may change, and your continued use will be subject to the new price. The model is in preview; a new deployment may be required for continued use.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{ModelName} 的使用目前定價為 $0 美元，使用受費率限制，隨時都可能變更。定價可能會變更，您的持續使用將以新的價格為準。該模型處於預覽階段；可能需要新的部署才能繼續使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MicrosoftProductTerms" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft product terms]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 產品條款]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.CreateModelButtonName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.CreateModelButtonNameV1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.CreateModelGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while registering model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊模型時發生錯誤]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[An error occurred while creating model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.CreateModelNoPermission" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have the proper role based access to register a model. Missing permission: {permission}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您沒有適當的角色型存取權限來註冊模型。缺少權限: {permission}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.CreateModelTypes.FromDatastore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From datastore]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自資料存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.CreateModelTypes.FromJobOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From a job output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從作業輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.CreateModelTypes.FromLocalFiles" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From local files]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自本機檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.CreateModelTypes.FromLocalFilesLegacy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From local files (based on framework)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來自本機檔案 (依據架構)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[From local files (Legacy)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelCustomFrameworkName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Framework name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[架構名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFileLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model file or folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型檔案或資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFileName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFilePlaceHolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select model file…]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型檔案…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFolderPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select model folder…]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型資料夾…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFramework" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model framework]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型架構]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeAccelOnnx" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AccelOnnx]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AccelOnnx]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeAutoML" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AutoML]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AutoML]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeChainer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chainer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Chainer]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeCustom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Other]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[其他]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeH2O" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[H2O]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[H2O]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeLightGMB" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LightGBM]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LightGBM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeMulti" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multi]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多重]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeOnnx" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ONNX]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ONNX]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypePySpark" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PySpark]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PySpark]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypePyTorch" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PyTorch]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PyTorch]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeR" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[R]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[R]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeScikitLearn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[scikitlearn]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[scikitlearn]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeTFKeras" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TFKeras]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TFKeras]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeTensorFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TensorFlow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TensorFlow]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkTypeXGBoost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[XGBoost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[XGBoost]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelFrameworkVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Framework version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Framework 版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelPropertiesEditorHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelPropertyEditorButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add property]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelTagEditorButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add tag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelTagEditorHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormModelUnpack" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unpack for deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[解除封裝以部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormPrimaryButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormSubmittingText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registering model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在註冊模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.FormUploadProgressText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Uploading file {fileIndex} of {fileCount}: {filePercentage} ...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在上傳第 {fileIndex} 個檔案 (共 {fileCount} 個): {filePercentage} ...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.ModelCreatePanelHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.ModelSubmitSuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model {modelName} registered successfully]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型 {modelName} 註冊成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.ModelSubmitSuccessMessageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Success]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.ModelSubmitUnloadWarning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Leaving the page will interrupt the model registration process.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[離開此頁面將會中斷模型註冊程序。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.Validation.ModelDescriptionMaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description must be under 201 characters.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述不得超過 201 個字元。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.Validation.ModelFileMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model file is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要模型檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.Validation.ModelFrameworkMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model framework is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要模型架構]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.Validation.ModelFrameworkVersionMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model framework version is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要模型架構版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.Validation.ModelNameInvalidCharacters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name of a model must be only letters, numbers, dashes, periods, or underscores, and start with a letter or number]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型的名稱只可包含字母、數字、虛線、點 (.) 或底線，且以字母或數字開頭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.Validation.ModelNameInvalidLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name of a model must be under 30 characters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型名稱長度必須短於 30 個字元]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Create.Validation.ModelNameMissing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model name is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要模型名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.CreatedByColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.CreatedTimeColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Errors.ArtifactContentFetchError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while loading artifacts]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[載入成品時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Errors.AssetFetchError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while loading model assets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[載入模型資產時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Errors.DataDriftFetchError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while loading model data drift information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[載入模型資料漂移資訊時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Errors.FailedToParse" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to parse artifact data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法剖析成品資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Errors.FailedToPromote" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to share model "{modelName}".]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法共用模型 "{modelName}"。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Failed to promote model: {modelName}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Errors.ModelFetchError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Could not fetch the specified model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法擷取指定的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Errors.RegisterExistingError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while registering the model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊模型時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.ExperimentNameColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experiment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[實驗]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.FavoriteColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Favorite]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我的最愛]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.JobRunIdColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Job (Run ID)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作業 (執行識別碼)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.CreatedByColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.CreatedTimeColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.ExperimentNameColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experiment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[實驗]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.ModelListPageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model List]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型清單]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.NameColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.PropertiesPanelHeaderFormat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{modelName} properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{modelName} 屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.RefreshModelListButtonName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.TagsColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.TagsPanelHeaderFormat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{modelName} tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{modelName} 標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.List.VersionColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.ListLoadingGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while loading the list]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[載入清單時發生錯誤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Loading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在載入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.NameColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.AcceptedMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sharing the model to the registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將模型共用至登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.AcceptedTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sharing in progress]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共用進行中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.Add" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose the registry where you want this model made available. The registry must be deployed to the same region as this workspace: {location}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選擇您希望此模型可用的登錄。登錄必須部署到與此工作區相同的區域: {location}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Choose the registry where you want this model made available.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.NoRegistries" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are no registries available to share to]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有可共用的登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.Registry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.RegistryNoPermission" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have permission to share to this registry. Choose another registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您沒有共用此登錄的權限。請選擇其他登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Share to registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分享至登錄]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Share in registry]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.ValidatingRegistry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating registry …]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在驗證登錄...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PromotionPanel.ValidatingVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating version …]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在驗證版本...]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Validating version...]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PropertiesColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.PropertiesPanelHeaderFormat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{modelName} properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{modelName} 屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterExistingPanel.RegisterExistingTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterExistingPanel.SeeDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看詳細資料]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterExistingPanel.SuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{modelId} has been successfully registered.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{modelId} 已成功註冊。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterExistingPanel.SuccessMessageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successfully registered new model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功註冊新模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.CreatingMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registering model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在註冊模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Description.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Description.MaxLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description must be under 201 characters.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述不得超過 201 個字元。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.FileOrFolder.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model file or folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型檔案或資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.FileOrFolder.Required" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File or folder is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要檔案或資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Framework.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model framework]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型架構]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.FrameworkName.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Framework name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[架構名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.FrameworkName.Required" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Framework name is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要架構名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.FrameworkVersion.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Framework version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Framework 版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.FrameworkVersion.Required" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Framework version is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要架構版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Header" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.ModelFlavors.Errors.JsonParse" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not valid JSON]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不是有效的 JSON]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.ModelFlavors.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flavors]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[變體]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.ModelFlavors.TagTypePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[flavors]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[口味]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.ModelFlavors.TagTypeSingular" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[flavor]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[變體]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.ModelProperties.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新增屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.ModelProperties.TagTypePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.ModelProperties.TagTypeSingular" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[property]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[屬性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.ModelTags.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Add tags]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.ModelVersion.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Name.InvalidCharacters" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name of a model must be only letters, numbers, dashes, periods, or underscores, and start with a letter or number]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型的名稱只可包含字母、數字、虛線、點 (.) 或底線，且以字母或數字開頭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Name.InvalidLength" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The name of a model must be under 255 characters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型名稱長度必須短於 255 個字元]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The name of a model must be under 30 characters]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Name.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Name.Required" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Register" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterForm.Unpackable.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unpack for deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[解除封裝以部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.Errors.MLmodelFileNotFound.JobOutputFolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output must be a folder containing an mlflow 'MLmodel' file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸出必須是包含 mlflow 'MLmodel' 檔案的資料夾。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.Errors.MLmodelFileNotFound.LocalFolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Folder must contain an mlflow 'MLmodel' file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料夾必須包含 mlflow 'MLmodel' 檔案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.Messages.ModelCreateSuccessMessage.ClickHereToJumpTo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click here to go to this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請按這裡以前往此模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.Messages.ModelCreateSuccessMessage.Message" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model '{name}' registered successfully.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功登錄模型 '{name}'。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.Messages.ModelCreateSuccessMessage.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Success]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelFilesDisplay.Errors.Files" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[files]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelFilesDisplay.FileEditorFileSummary" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{numOfFiles} file selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已選取 {numOfFiles} 個檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelFilesDisplay.FileEditorFilesSummary" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{numOfFiles} files selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已選取 {numOfFiles} 個檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelFilesDisplay.FileSummary" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{numOfFiles} file selected. Total size {sizeOfFiles}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已選取 {numOfFiles} 個檔案。大小總計為 {sizeOfFiles}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelFilesDisplay.FilesSummary" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{numOfFiles} files selected. Total size {sizeOfFiles}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已選取 {numOfFiles} 個檔案。大小總計為 {sizeOfFiles}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelFilesDisplay.TableColumnHeaders.FileName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelFilesDisplay.TableColumnHeaders.FileSize" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelTypeDisplayStrings.CustomModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unspecified type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未指定的類型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Custom]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelTypeDisplayStrings.MLFlowModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLflow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MLflow]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[MLFlow]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Common.ModelTypeDisplayStrings.TritonModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Triton]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Triton]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Triton (preview)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure the settings for your model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[設定模型的設定。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Configure the settings for your model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelTypePicker.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelTypePicker.Tooltip.Custom.Text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA['Unspecified' is a type that refers to a model file or folder logged or saved with a standard other than MLflow or Triton.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA['Unspecified' 是指以 MLflow 或 Triton 以外的標準記錄或儲存的模型檔案或資料夾的類型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelTypePicker.Tooltip.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about model registration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解模型註冊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelTypePicker.Tooltip.MLflow.Text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLflow is a type that refers to a model logged or saved with {MLflow}. MLflow logged or saved models are in a folder that contains the 'MLmodel' file, the 'model' file, the conda dependencies file, and the 'requirements.txt' file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MLflow 是參照以 {MLflow} 記錄或儲存之模型的類型。MLflow 記錄或儲存的模型位於包含 'MLmodel' 檔案、'model' 檔案、Conda 相依性檔案以及 'requirements.txt' 檔案的資料夾中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelTypePicker.Tooltip.Triton.Header" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Triton]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Triton]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Triton (preview)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelTypePicker.Tooltip.Triton.Text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Triton is a type that refers to a model logged or saved with {Triton}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Triton 是指以 {Triton} 記錄或儲存之模型的類型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelVersion.AlphanumericVersionTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version of the model. This must be a positive integer or an alphanumeric string. If no version is provided, it's automatically incremented to the next integer.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型的版本。這必須是正整數或英數字元字串。若未提供版本，則會自動遞增至下一個整數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelVersion.Errors.PositiveInteger" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version must be a positive integer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本必須是正整數]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelVersion.Errors.VersionAlreadyExists" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A model with this name and version already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有這個名稱和版本的模型已經存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelVersion.Errors.VersionRequired" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本為必要項]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelVersion.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelVersion.NextAvailableVersionSuggestion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next available version for this model name : {nextVersion}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型名稱的下一個可用版本 : {nextVersion}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.ModelVersion.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version of the model. This must be a positive integer. If no version is provided, it's automatically incremented to the next integer.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型的版本。必須是正整數。若未提供版本，則會自動遞增至下一個整數。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ModelSettingsStep.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型設定]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Panel.CreateModelGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while registering model. Click 'More details' for more information.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊模型時發生錯誤。按一下 [詳細資訊]5D; 以了解更多資訊。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[An error occurred while creating model. Click 'More details' for more information.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Panel.PanelButtons.SubmitButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Panel.PanelHeader.FromDatastore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register model from datastore]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從資料存儲登錄模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create model from datastore]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Panel.PanelHeader.FromJobOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register model from a job output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從作業輸出註冊模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create model from a job output]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Panel.PanelHeader.FromLocalFiles" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register model from local files]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從本機檔案註冊模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create model from local files]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Panel.SubmittingText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registering model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在註冊模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Creating model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.Panel.UploadProgressText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Uploading file {fileIndex} of {fileCount}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在上傳檔案 {fileIndex} / {fileCount}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ReviewStep.DatastoreCard.DatastoreLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Datastore]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ReviewStep.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review or make changes to your selections.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視或變更您的選取範圍。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Review the model settings.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ReviewStep.JobCard.JobLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Job]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作業]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ReviewStep.JobOutputCard.FilesLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Files]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ReviewStep.JobOutputCard.JobOutputLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Job output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作業輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ReviewStep.JobOutputCard.NamedOutputLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Named model output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具名模型輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ReviewStep.LocalFilesCard.FilesLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Files]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.ReviewStep.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢閱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreBrowse.BrowseButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[瀏覽]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreBrowse.FileOrFolderPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select model file or folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型檔案或資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreBrowse.FolderPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select model folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreBrowse.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Datastore path]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料存放區路徑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreBrowse.ManualEntry.FileOrFolderPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter model file or folder path]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入模型檔案或資料夾路徑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreBrowse.ManualEntry.FolderPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter model folder path]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[輸入模型資料夾路徑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreBrowse.RequiredError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Datastore path is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要資料存放區路徑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreSelector.DatastoresLoading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading..]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在載入...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreSelector.FailedToLoad" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to load datastores]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法載入資料存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreSelector.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select datastore]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取資料存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreSelector.RefreshTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh datastores]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理資料存放區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.DatastoreSelector.Required" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Datastore is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料存放區是必要項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a datastore containing the model files.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取包含模型檔案的資料存放區。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Datastore selection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[資料存放區選取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectDatastoreStep.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a datastore containing the model files. We currently only support the datastore '{datastoreName}'.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取包含模型檔案的資料存放區。我們目前僅支援資料存放區 '{datastoreName}'。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.AutoSelectionBanner.MLflow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An MLflow folder has been detected in the job default outputs and auto-selected. You can select any other output and/or type if this is not what you intend to select.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在作業預設輸出中偵測到 MLflow 資料夾，並已自動選取。若這不是您要選取的輸出和/或類型，您可以選取任何其他輸出。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[An MLflow folder has been detected in the job output and auto-selected. You can select any other output and/or type if this is not what you intend to select.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.AutoSelectionBanner.NamedModelOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A named model output has been detected in the job outputs and auto-selected. You can select any other output and/or type if this is not what you intend to select.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在作業輸出中偵測到具名模型輸出，並已自動選取。若這不是您要選取的輸出和/或類型，您可以選取任何其他輸出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.AutoSelectionBanner.Triton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A Triton file/folder has been detected in the job default outputs and auto-selected. You can select any other output and/or type if this is not what you intend to select.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在作業預設輸出中偵測到 Triton 檔案/資料夾，並已自動選取。若這不是您要選取的輸出和/或類型，您可以選取任何其他輸出。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[A Triton file/folder has been detected in the job output and auto-selected. You can select any other output and/or type if this is not what you intend to select.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specify the corresponding output to register the model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指定要註冊模型的對應輸出。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Specify the corresponding output to create the model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.ErrorPage.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a job from the previous step to see the corresponding output.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從上一步中選取作業以查看對應輸出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.ErrorPage.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No job selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未選取任何作業]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[No job selected in previous step]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.ClearTextField" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clear job output text field]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[清除作業輸出文字欄位]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.DefaultOutputsNoResourceName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[default outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預設輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.FailedToLoadError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to load job outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法載入工作輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.FileDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only folder can be selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只能選取資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Job output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作業輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.LoadingPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在載入...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.NamedModelOutputsNoResourceName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[named model outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具名模型輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.NavGroupHeader.FileOrFolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select file or folder from default outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從預設輸出選取檔案或資料夾]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select a file or folder]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.NavGroupHeader.FolderOnly" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select folder from default outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從預設輸出選取資料夾]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select folder]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.NavGroupHeader.NamedModelOutputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select named model output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取具名模型輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.NavGroupHeader.SearchBoxPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜尋]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.NavGroupHeader.ShowAllOutputsToggle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show all default outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示所有預設輸出]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Show all job outputs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.OutputsResourceName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[job outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作業輸出]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[outputs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.Placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select named or default output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取已命名或預設輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.RefreshTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh job outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新整理作業輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.RequiredError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Job output is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要工作輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a named model output, or a model file or folder from the default job outputs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從預設工作輸出選取具名模型輸出或模型檔案或資料夾。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select the model file or folder from the list of job outputs.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.JobOutputBrowse.TooltipToggle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[By default, system generated outputs are hidden. To view all default job outputs turn on the 'Show all default outputs' toggle in the dropdown.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根據預設，將會隱藏系統所產生的輸出。若要檢視所有預設作業輸出，請開啟下拉式清單中的 [顯示所有預設輸出]5D; 切換。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[By default, system generated outputs are hidden. To view all job outputs turn on the 'Show all job outputs' toggle in the dropdown.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobOutputStep.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取輸出]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select job output]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.CreatedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.CreatedTime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建立日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.DisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Display name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示名稱]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.Duration" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Duration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[持續時間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.EndTime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[End time]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[結束時間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.Experiment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experiment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[實驗]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.RunType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[執行類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.StartTime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start time]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始時間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.Status" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[狀態]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.ColumnNames.Tags" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specify the job for registering the model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指定用於註冊模型的作業。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Specify the job for creating the model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.JobsEntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[jobs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作業]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.Required" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Job is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作業是必要項目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.SelectJobStep.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select job]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取作業]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.UploadLocalFilesStep.BrowseButton.BrowseFile" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[瀏覽檔案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.UploadLocalFilesStep.BrowseButton.BrowseFolder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[瀏覽資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.UploadLocalFilesStep.BrowseButton.ButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[瀏覽]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.UploadLocalFilesStep.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload the file or folder containing the model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上傳包含模型的檔案或資料夾。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.UploadLocalFilesStep.LocalFilesBrowse.Errors.FileOrFolderRequired" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File or folder is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要檔案或資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.UploadLocalFilesStep.LocalFilesBrowse.Errors.FolderRequired" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Folder is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.UploadLocalFilesStep.LocalFilesBrowse.Labels.FileOrFolderLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select model file or folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型檔案或資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.UploadLocalFilesStep.LocalFilesBrowse.Labels.FolderLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select model folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型資料夾]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegisterWizard.UploadLocalFilesStep.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上傳模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RegistryColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.ResourceColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Source]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[來源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Resource]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.RunIdColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[執行識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Stickers.FinetuningTask" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tuning task: {task}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微調工作: {task}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Stickers.Languages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Languages: {language}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語言: {language}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Stickers.License" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[License: {license}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[授權: {license}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Stickers.MinInferenceSku" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Minimum inferencing sku: {sku}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最小推斷 SKU: {sku}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Stickers.Task" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task: {task}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作: {task}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.TagsColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.TagsPanelHeaderFormat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{modelName} tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{modelName} 標籤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.TypeColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[類型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.VersionColumnHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Model.Workspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作區]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelAccordionTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What's new?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[有哪些新功能?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelAnnouncements_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Announcements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通知]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelAssetId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelAssetIdDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reference this model ID when deploying the model in code]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在程式碼中部署模型時參考此模型識別碼]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelFilter.All" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[All]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[全部]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelFilter.Search" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜尋]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.DeprecatedMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model is deprecated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型已被取代]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.DeprecatedMessageWithDate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model was deprecated on {DeprecatedDate}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型已於 {DeprecatedDate} 被取代]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.DeprecatedTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deprecated model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已取代的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.LegacyMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is a legacy model with an upcoming deprecation date.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是取代日期即將到來的舊版模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.LegacyMessageWithDates" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is a legacy model with an upcoming deprecation and retirement dates. You will not be able to create a new deployment after {DeprecationDate} local time. Your existing deployments will stop inferencing after the retirement date on {RetirementDate} local time.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是具有即將淘汰和淘汰日期的舊版模型。在當地時間 {DeprecationDate} 之後，您將無法建立新的部署。您現有的部署將在 {RetirementDate} 當地時間的淘汰日期之後停止推斷。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This is a legacy model with an upcoming deprecation date on {DeprecationDate}. You will not be able to create a new deployment after {RetirementDate}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.LegacyTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Legacy model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[舊版模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.RetiredMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model was retired]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型已淘汰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.RetiredMessageWithDate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model was retired on {RetirementDate}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型已於 {RetirementDate} 淘汰]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLifeCycle.RetiredTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retired model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已淘汰的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelListEmptyResourceNotice.DescriptionBodyText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Each time you register a model with the same name as an existing one, the registry assures that it's a new version. Additionally, you can provide metadata tags and use the tags when you search for models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每次您登錄與現有模型同名的模型時，登錄都會確保所登錄的模型為新版本。此外，您也可以提供中繼資料標籤，以在搜尋模型時使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelListEmptyResourceNotice.DescriptionHeroText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create, manage and track your registered models as incremental versions under the same name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過在相同名稱下累加版本的方式，建立、管理與追蹤所登錄的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelListEmptyResourceNotice.GitHubUrl" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[https://github.com/Azure/MachineLearningNotebooks/tree/master/how-to-use-azureml/training]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[https://github.com/Azure/MachineLearningNotebooks/tree/master/how-to-use-azureml/training]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelListEmptyResourceNotice.ImageTooltipText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelListEmptyResourceNotice.TutorialUrl" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[https://github.com/Azure/MachineLearningNotebooks/tree/master/tutorials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[https://github.com/Azure/MachineLearningNotebooks/tree/master/tutorials]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Benchmarks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基準]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.MaapSurchargePricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unit price varies depending on your deployment type. See more info on the model description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[單價會根據您的部署類型而不同。請參閱模型描述的詳細資訊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.ModelInput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型輸入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.ModelOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型輸出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.April" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[April]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[4 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.August" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[August]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[8 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.December" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[December]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[12 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.February" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[February]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[2 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.January" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[January]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[1 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.July" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[July]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[7 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.June" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[June]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[6 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.March" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[March]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[3 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.May" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[May]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[5 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.November" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[November]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[11 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.October" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[October]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[10 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Months.September" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[September]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[9 月]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.NIMsSurchargeAddition" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Compute charges also apply based on deployment configuration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 計算的收費也會根據部署設定而有所不同]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[in addition to your chosen Azure VM infrastructure cost]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.NIMsSurchargeTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NIM Surcharge:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPU 附加費:]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Surcharge:]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.NotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.PayAsYouGoTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go ({ConsumptionUnitType})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隨用隨付 ({ConsumptionUnitType})]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Pay-as-you-go (per 1K tokens)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.Pricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[價格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.ProvisionedThroughputUnit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioned throughput unit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已佈建的輸送量單位]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.QuickFacts" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quick facts]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[快速事實]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.RouterModelPricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only the underlying model use is charged today. Starting August 1, the model router usage will be charged as well. {PricingLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[今天僅收取基礎模型的使用費用。自 8 月 1 日起，模型路由器的使用量也將開始收費。{PricingLink}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Only underlying model usage will be billed. {PricingLink}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.SeeAzureDirectPricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See direct from Azure pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請參閱直接來自 Azure 價格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.SeePricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看價格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelNutritionCard.TrainingDataLastUpdated" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Training data last updated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[訓練資料最後更新時間]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelPickerFilterBar.ClearFieldPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clear {fieldName} filter]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[清除 {fieldName} 篩選]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelPickerFilterBar.Collections" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Collections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[集合]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelPickerFilterBar.Models" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelPickerFilterBar.NoModelForFinetuneText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No models available to fine-tune]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有可供微調的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelPickerFilterBar.NoModelText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model to see description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取模型以查看描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelPickerFilterBar.ShowDescriptionView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MoreInformation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更多資訊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MoreInformationAria" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More information about direct from Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於直接來自 Azure 的詳細資訊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NIMsLastUpdatedTimeDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Getting the latest NIM updates requires redeploying the model after {fieldName}. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得最新的 NIM 更新需要在 {fieldName} 之後重新部署模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NIMsLastUpdatedTimeTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NIM was last updated on {fieldName}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NIM 最後更新日期: {fieldName}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NIMsLearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NumberModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{count} models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{count} 個模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OpenInVSCode.OpenInVSCodeButtonDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model or language not supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不支援模型或語言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OpenInVSCode.OpenInVSCodeButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open in VS Code]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 VS Code 中開啟]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Playground.CodeCopied" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Code Copied]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[代碼已複製]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Playground.CopyCodeToClipboard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy code to clipboard ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將程式碼複製到剪貼簿 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PolicyRestriction.DeployBlockedOnModelDeprecation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model cannot be deployed because it has been deprecated.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法部署此模型，因為它已被取代。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PolicyRestriction.Message" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model and version cannot be deployed due to a policy in your subscription. Contact your admin to request access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由於您訂用帳戶中的原則，無法部署此模型和版本。連絡您的系統管理員以要求存取權。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PolicyRestriction.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restricted by admin policy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受系統管理原則限制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Registry.NoRegistries" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are no registries available to share to]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沒有可共用的登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Registry.RegistryNoPermission" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have permission to share to this registry. Choose another registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[您沒有共用此登錄的權限。請選擇其他登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Registry.SelectRegistry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[選取登錄]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Registry.ValidatingRegistry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating registry …]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在驗證登錄...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestModelAccessBannerText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Access will be granted according to Microsoft's eligibility criteria; you may not be eligible for access at this time. If you have already requested access, thank you for your patience while your submission is reviewed. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存取權會依 Microsoft 的資格準則授予，您目前可能不符合存取資格。如已要求存取，請耐心等候您的提交審核。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestModelAccessBannerTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registration is required to use this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要註冊才能使用此模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RequestModelAccessFormTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要求存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SeeLess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See less]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示較少]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SeeMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更多]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SuggestModel.Message" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Can't find the model you are looking for?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到您要找的模型嗎?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SuggestModel.PrefilledComment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[model: <replace this with the public URL about the model you want us to onboard>]A;]A;How can we improve the model catalog for you?]A;]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型: <replace this with the public URL about the model you want us to onboard>]A;]A;我們該如何為您改善模型目錄?]A;]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SuggestModel.SubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please share details of the model below:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[請分享下列模型的詳細資料:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SuggestModel.SuggestAModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Suggest a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[建議模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SuggestModel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rate your overall experience with the model catalog.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為您使用模型目錄的整體體驗評分。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TaskFilterTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.BitextMining" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bitext mining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[雙文字挖掘]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ChatCompletion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat completion]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ChatCompletions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat completions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Clustering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[叢集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Completions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Completions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Conversational" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Conversational]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[交談]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Embeddings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Embeddings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[內嵌]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.FillMask" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fill mask]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[填滿遮罩]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Forecasting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Forecasting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[預測]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ImageClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ImageSegmentation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image segmentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像分割]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ImageToImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image to image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像轉換影像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ImageToText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image to text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像轉換文字]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ManagedCompute" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受控計算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.MultiObjectTracking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multi-Object tracking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多個物件追蹤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ObjectDetection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Object detection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[物件偵測]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.PairClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pair classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配對分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.QuestionAnswering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[問題解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Reranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新評等]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Retrieval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[擷取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.STS" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[STS]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[STS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ServerlessAPI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless API]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無伺服器 API]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.SpeechRecognition" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Speech recognition]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[語音辨識]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.TableQuestionAnswering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Table question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[表格問題解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Text2TextGeneration" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to text generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字到文字產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.TextClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.TextGeneration" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字產生]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.TextSummarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.TextToImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字轉換影像]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Text-to-image]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.TextTranslation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text translation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文字翻譯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.TokenClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[權杖分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Translation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Translation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[翻譯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.VideoClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影片分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.VisualQuestionAnswering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visual question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[視覺效果問題解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ZeroShotClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zero-shot classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零拍攝分類]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Zero shot classification]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ZeroShotImageClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zero-shot image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零拍攝影像分類]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TryEarlyAccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try early access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嘗試提早存取]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TryLimitedAccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try limited access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嘗試有限存取權]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Types.Base" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Base]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基礎]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Types.Finetuned" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine -tuned]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微調]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Undisclosed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[無法使用]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Undisclosed]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";UseInProjectButton.SignInToUseInProject" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to use this model in project]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登入以在專案中使用此模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";UseInProjectButton.UseInProject" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewAllModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View all models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視所有模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewModelsForCollection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View {collectionName} models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視 {collectionName} 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
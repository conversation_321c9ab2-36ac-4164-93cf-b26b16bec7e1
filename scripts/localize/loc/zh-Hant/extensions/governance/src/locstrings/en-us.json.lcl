﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\1\s\extensions\governance\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-TW" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";Governance.BreadCrumb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Governance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[治理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card1.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Streamline AI governance with Azure AI Foundry by integrating evaluation tools with top governance platforms. These integrations allow you to define, execute, and monitor AI risk and compliance workflows seamlessly, without affecting developer productivity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過將評估工具與頂級治理平台整合，使用 Azure AI Foundry 簡化 AI 治理。這些整合使您能夠順暢地定義、執行和監控 AI 風險與合規性工作流程，而不影響開發人員的生產力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card1.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Governance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[治理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card2.ButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to Purview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[前往 Purview]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card2.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assess and manage compliance for your AI apps with Microsoft Purview Compliance Manager. It translates AI regulations, such as the EU AI Act, into actionable suggestions that you can implement in Azure AI Foundry to run your AI evaluations. Upload the results back to Microsoft Purview Compliance Manager assessments for a comprehensive view of your regulatory posture, ensuring you stay current with regulations and certifications, and can report effectively to auditors.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Microsoft Purview 合規性管理員評估和管理您的 AI 應用程式的合規性。它將 AI 法規，例如歐盟 AI 法案，轉化為可在 Azure AI Foundry 中實作的可行建議，以執行您的 AI 評估。將結果上傳回 Microsoft Purview 合規性管理員評估，以全面查看您的合規狀況，確保您能跟上法規和認證的最新要求，並能有效向稽核者報告。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Assess and manage compliance for your AI apps with Microsoft Purview Compliance Manager. It translates AI regulations, such as the EU AI Act, into actionable suggestions that you can implement in Azure AI Foundry to run your AI evaluations. Upload the results back to Compliance Manager assessments for a comprehensive view of your regulatory posture, ensuring you stay current with regulations and certifications, and can report effectively to auditors.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card2.LinkLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card2.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Purview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Purview]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Purview Compliance Manager]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card3.ButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to Credo AI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[前往 Credo AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Go to Credo]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card3.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Define evaluation requirements for AI projects with the Credo AI integration for Azure AI Foundry. Governance teams can set project-specific and compliance-based requirements, which are then converted into executable code for developers to run Evaluators directly in Foundry. This closed-loop approach removes traditional bottlenecks and ensures AI systems meet compliance standards from the start.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過適用於 Azure AI Foundry 的 Credo AI 整合，定義 AI 專案的評估需求。治理團隊可以設定專案特定和合規性的需求，這些需求將轉換為可執行的代碼，供開發人員直接在 Foundry 中執行評估工具。此封閉迴圈方法消除了傳統瓶頸，並確保 AI 系統從一開始就符合合規性標準。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card3.LinkLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card3.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Credo AI integration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Credo AI 整合]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Credo integration]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card4.ButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Saidot]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 Saidot]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card4.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connect your Azure model registry to govern AI models and agents in Saidot with the Microsoft Azure AI Foundry integration. Generate evaluation plans based on risk profiles, including red teaming and simulated datasets. Run evaluations in Azure, review results in Saidot, and report to risk and compliance—all in one streamlined workflow.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將您的 Azure 模型登錄連接至 Saidot，以透過 Microsoft Azure AI Foundry 整合來管理 AI 模型和代理程式。根據風險設定檔產生評估計劃，包括紅隊測試和模擬資料集。在 Azure 中執行評估，於 Saidot 中檢視結果，並向風險與合規性報告 -- 這一切都可在一個簡化的工作流程中完成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card4.LinkLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card4.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Saidot integration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Saidot 整合]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card5.ButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View Apps]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[檢視應用程式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card5.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Foundry system-assigned managed identities are now automatically labeled as Microsoft Entra Agent IDs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Foundry 系統指派的受控識別現在會自動標示為 Microsoft Entra 代理程式識別碼。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card5.LinkLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card5.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Entra]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Entra]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Microsoft Entra Agent ID]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.ComingSoon" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coming Soon]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即將推出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.PageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI development with a governance-first approach]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以治理優先的方式進行 AI 開發]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
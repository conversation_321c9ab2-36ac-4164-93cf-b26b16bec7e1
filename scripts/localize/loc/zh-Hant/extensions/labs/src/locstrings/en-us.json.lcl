﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\3\s\extensions\labs\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-TW" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";Banner.AboutText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[About]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關於]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.BrandName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Foundry Labs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Foundry 實驗室]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.FollowLinkedin" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Follow us on LinkedIn]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[追蹤我們LinkedIn]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.FollowX" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Follow us on X]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 X 上關注我們]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.FollowYoutube" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Follow us on YouTube]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在YouTube上關注我們]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.MoreOptions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More options]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[其他選項]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.SwitchToDarkTheme" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dark theme]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深色佈景主題]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.ContactLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Contact]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[連絡人]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.CopyrightText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[© Microsoft 2025]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[© Microsoft 2025]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.PrivacyLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Privacy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隱私權]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.TermsLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Terms]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[條款]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.TrademarksLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trademarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[商標]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.AuroraDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora is a foundation model, built on vast amounts of atmospheric data, that can significantly improve our ability to predict extreme weather events. Explore how this innovative model can enhance weather forecasting.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[極光是以大量大氣數據為基礎的基礎模型，可大幅提升我們預測極端天氣事件的能力。探索此創新模型如何增強天氣預測。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Introducing Aurora, a foundation model, built on vast amounts of atmospheric data, that can significantly improve our ability to predict extreme weather events. Explore how this innovative model can enhance weather forecasting.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.BeFirst" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Be the first to know about the latest AI innovations and accelerate your journey.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成為第一個瞭解最新 AI 創新功能的人，並加速您的旅程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.BioEmuDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A deep learning model that can generate thousands of protein structures per hour on a single GPU.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一種深度學習模型，能在單一圖形處理器上每小時產生數千種蛋白質結構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.BitnetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research, BitNet b1.58 2B4T is the first open-source, native 1-bit large language model (LLM) at a 2-billion parameter scale.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 Microsoft Research 開發的 BitNet b1.58 2B4T 是第一個開放原始碼的原生 1 位元大型語言模型 (LLM)，參數規模達到 20 億。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Moving from curiosity to clarity, from imagination to reality.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從好奇心到清晰，從想像力到現實。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.EvoDiffDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A general-purpose diffusion framework for controllable protein generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用於可控制蛋白質產生的一般用途擴散架構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.ExACTDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT 是一種教導 AI 代理程式更有效率地探索的方法，可讓他們以智慧方式瀏覽其環境、收集有價值的資訊、評估選項，以及找出最佳決策制定與規劃策略。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Developed by Microsoft Research, ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.InnovationSubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get a glimpse of potential future directions for AI, with these experimental technologies from Microsoft Research.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過Microsoft研究的這些實驗性技術，深入瞭解 AI 未來的潛在方向。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Glimpse of potential future directions for AI, with these experimental technologies from Microsoft Research.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Innovations" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Innovations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[創新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.JoinCommunity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Join the community and stay connected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加入社群並保持聯繫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Labs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Labs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[實驗室]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MCPServerDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Integrate, Prototype, and Accelerate AI Model Experimentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[整合、建立原型並加速 AI 模型實驗]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MSRACCDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the largest high-accuracy dataset for training deep-learning-based models for density functional theory (DFT). It enables a leap forward in predictive chemistry and supports the development of Skala, a new DFT functional from Microsoft Research that has achieved a breakthrough in accuracy for this workhorse method that thousands of scientists use every year to simulate matter at the atomistic level.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這是目前規模最大、專為訓練基於深度學習的密度泛函理論 (DFT) 模型而建立的高準確度資料集。它推動了預測化學的重大進展，並支援 Skala 的開發，這是 Microsoft Research 推出的新型 DFT 泛函，在這項每年有數千位科學家用於原子層級物質模擬的核心方法中，實現了前所未有的準確度突破。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MagenticOneDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One is a multi-agent system designed to navigate complex tasks across diverse domains. Discover how intelligent agents could operate autonomously to enhance workflow efficiency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 是多重代理程式系統，設計用來跨不同網域瀏覽複雜的工作。探索智慧型手機代理程式如何自主運作以提升工作流程效率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MagenticUIDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI is an open-source experimental platform to accelerate progress in human-agent collaboration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 是一項開放原始碼實驗平台，可加速人類與代理程式共同作業的進度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MagmaDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in digital and physical environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[Magma]5D; 是一種多模式基礎模型，設計用來瞭解數位及實體環境中的數位及實際運作。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in dexpigital and physical environments.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MatterSimDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An AI-driven innovation transforming how we create and understand new materials, starting with accurate and efficient simulations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 驅動的創新正在改變我們建立及瞭解新材料的方法，從精確且高效率的模擬開始。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MuseDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empowering game creatives with generative AI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過產生式 AI 讓遊戲有創意。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Empowering game creatives with Generative AI.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.NextCoderDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhancing the ability of coding models to handle diverse editing requirements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增強編碼模型處理各種編輯需求的能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.OmniParserDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is a pioneering screen parsing module that transforms user interfaces into actionable elements through visual input. Discover how this innovative approach can enhance automated UI interactions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 是一個通過視覺輸入將使用者介面轉換成可操作元素的輔助螢幕剖析模組。探索此創新方法如何增強自動化UI互動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.PEACEDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE paves the way for advanced AI applications in geology, powering more efficient and accurate disaster detection, resource exploration, and civil engineering. Learn how PEACE transforms general-purpose multimodal LLMs into powerful domain-specific agents.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 為地質學進階 AI 應用程式鋪路，讓災害偵測、資源探索和土木工程更有效率且精確。了解 PEACE 如何將一般用途的多模式 LLM 轉換為強大的特定領域代理程式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Pause" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pause]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[暫停]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Phi4Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore the capabilities of Phi-4, the latest model in Microsoft's Phi family of advanced AI technologies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Phi-4 的功能，這是 Microsoft Phi 系列中最新的進階 AI 技術模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Play" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Play]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[播放]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.ProjectAmelieDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie is our first Foundry autonomous agent built in collaboration with Microsoft Research that can perform machine learning engineering tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 是我們與 Microsoft Research 共同作業建置的第一個 Foundry 自主代理程式，可執行機器學習工程工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.ReMeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe enables scalable, personalized approaches to cognitive health—a growing need that affects millions. Explore this web-based framework, which puts powerful AI-enabled research tools in the hands of scientists and clinicians.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 針對認知健康提供可調整的個人化方法，影響數百萬人且日益增長的需求。探索此網頁架構，其將強大的 AI 研究工具交到科學家和臨床醫生手中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.TamGenDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover how AI could accelerate the process of pharmaceutical discovery, leading to faster medical breakthroughs and improved treatment options.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 AI 如何加速藥物探索的程式，進而加速醫學突破和改良的治療選項。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Foundry]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.TrellisDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis is an AI model that generates high-quality 3D assets from text or image prompts in formats like meshes, radiance fields, and 3D Gaussians. Discover how it uses Structured LATents (SLAT) to fuse sparse 3D grids with dense visual features—powering easy and creative 3D content creation in gaming, AR/VR, design, and simulation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 是一款 AI 模型，可根據文字或影像提示產生高品質的 3D 資產，格式包括網格、輻射場與 3D 高斯表示。探索它如何利用結構化 LATents (SLAT) 將稀疏的 3D 格點與密集視覺特徵融合，推動遊戲、AR/VR、設計與模擬中的輕鬆且富創意的 3D 內容創作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.TypeAgentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent is sample code that explores an architecture for building a single personal agent with natural language interfaces leveraging current advances in LLM technology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 是一項範例程式碼，可探索運用 LLM 技術目前的進展，以自然語言介面建置單一個人代理程式的結構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.VASA3DDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D turns a single portrait and speech audio into a lifelike 3D talking head using a novel motion-latent-driven model. Discover how it enables real-time, expressive, and multiview-consistent avatars for education, collaboration, and immersive experiences.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 使用新穎的動作潛在特徵驅動模型，將單一肖像和語音音訊轉換成逼真的 3D 說話頭像。探索它如何為教育、共同作業和沉浸式體驗提供即時、表達力強和多視角一致的虛擬人偶。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.VibePodDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a multi-speaker audio generation framework for creating long-form, realistic dialogue from transcripts. It’s ideal for podcasts and voiceovers, with strong performance in pacing, coherence, and speaker dynamics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一個多演講者音訊產生架構，能從文字記錄中建立長篇、逼真的對話。它非常適合用於播客與配音，並在節奏控制、語意連貫性與演講者變化方面表現優異。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.DownloadDemo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Link to demo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[示範連結]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.OpenInGithub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open in Github]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Github 中開啟]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.ReadAnnouncements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read announcements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[讀取公告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.ReadPaper" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read paper]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[閱讀文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.WatchVideo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Watch video]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[觀看影片]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraBodyVideoAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Megan Stanley, Senior Researcher at Microsoft Research AI for Science, discusses Aurora, a groundbreaking model for weather forecasting that could revolutionize predictions and mitigation of extreme events, air pollution, and climate change.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MeganLey， senior research at Microsoft Research AI for Science， discusses Aurora， a groundbreaking model for weather forecasting that could revolutionize predictions and mitigation of extreme events， air mitigation， and mitigation change.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora is a large-scale foundation model developed for atmospheric forecasting. By leveraging extensive atmospheric data, this model enhances our capacity to predict and mitigate the impacts of extreme weather events.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[極光是針對大氣預測而開發的大型基礎模型。透過利用廣泛的大氣數據，此模型可增強我們預測及降低極端天氣事件影響的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Aurora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索極光]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeVideoDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Built on vast amounts of atmospheric data, this foundation model can significantly improve our ability to predict extreme weather events.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這個基礎模型以大量的大氣數據為基礎，可以大幅改善我們預測極端天氣事件的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora emerged from the recognition that traditional weather prediction models often fall short in capturing the rapid intensification and peak wind speeds that characterize extreme storms. Aurora’s innovative architecture has been trained on over a million hours of diverse weather and climate simulations, enabling it to excel in a broad spectrum of predictive tasks while achieving an impressive spatial resolution of 0.1° - approximately 11 km at the equator. This level of granularity enhances the accuracy of operational forecasts and confers an estimated computational speed advantage of around 5,000 times over conventional numerical weather-prediction systems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 會根據傳統天氣預測模型經常在擷取快速強化與尖峰風速時，因而導致識別極端天氣預測模型失敗。Aurora 的創新架構已在超過一百萬小時的不同天氣和氣候模擬上進行訓練，讓它能夠在各種預測工作中 excel，同時達成 0.1° 的顯著空間解析度 - 在赤道大約 11 公里。這種細微度層級可增強操作預測的正確性，並會比傳統數值預測系統提供約5,000倍的估計計算速度優勢。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora’s capabilities extend beyond accuracy and efficiency; it showcases versatility in forecasting a variety of atmospheric variables, including temperature, wind speed, and air pollution levels. Built using a flexible 3D Swin Transformer architecture and incorporating Perceiver-based encoders and decoders, Aurora effectively processes heterogeneous input data and generates predictions across multiple resolutions. Utilizing extensive pretraining on diverse datasets and fine-tuning for specific tasks, Aurora discerns complex patterns in atmospheric data, often yielding noteworthy results even with limited training data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 的功能延伸超過正確性和效率;它展示預測各種大氣變數的多功能性，包括溫度、風速和空氣暴毀程度。Aurora 使用彈性的 3D Swin Transformer 架構建置，並結合 Perceiver 型編碼器和譯碼器，可有效處理異質性輸入數據，並跨多個解析度產生預測。利用對不同數據集進行廣泛的預先訓練，並針對特定工作進行微調，Aurora 解析度在大氣數據中的複雜模式，即使具有有限的定型數據，通常會產生值得注意的結果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The significance of Aurora transcends performance metrics; its robust architecture and diverse pretraining illustrate how scale and data variety enhance atmospheric forecasting. By incorporating data from climate simulations, reanalysis products, and operational forecasts, Aurora builds a nuanced and generalizable understanding of atmospheric dynamics. Compared to leading specialized deep learning models, Aurora demonstrates the ability to surpass existing benchmarks, establishing it as a crucial tool for future environmental predictions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 的重要性比效能計量更重要;其健全架構和不同的預先訓練示範了規模和數據種類如何增強大氣預測。透過結合來自氣候模擬、重新分析產品和作業預測的數據，Aurora 會建置大氣動態的一致和一般瞭解。與領先的主要專業深入學習模型相比，Aurora 示範了已超過現有基準的能力，將它建立為未來環境預測的重要工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Aurora model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得極光模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Try Aurora]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu samples functionally distinct protein conformations. a) Large-scale domain motions such as opening/closing, rotation, and repacking. b) Local unfolding or unbinding of parts of the protein. c) Formation of cryptic binding pockets that are not present in the apo ground state.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu 取樣功能上不同的蛋白質構象。a) 大規模的域運動，例如開啟/關閉、旋轉和重新打包。b) 蛋白質部分的局部展開或解除結合。c) 形成在無配體基態中不存在的隱秘結合口袋。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu multi-conformation benchmark of local unfolding. For each case, the PDB structure used as folded state reference is shown in red, with the part can unfold highlighted. Energy landscapes show the empirical free energy sampled by the pre-trained (black) and fine-tuned (blue) model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu 多符合性基準的局部展開。針對每個案例，以紅色顯示作為折疊狀態參考的 PDB 結構，並突出顯示可展開的部分。能量景觀顯示預訓練模型 (黑色) 和微調 (藍色) 模型所取樣的經驗自由能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu-1 is a deep learning model that can generate thousands of protein structures per hour on a single graphics processing unit. It provides orders of magnitude greater computational efficiency compared to classical MD simulations, thereby opening the door to insights that have, until now, been out of reach.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu-1 是一種深度學習模型，能在單一圖形處理器上每小時產生數千種蛋白質結構。與傳統的分子動力學模擬相比，它提供了數量級更高的計算效率，從而開啟了以往無法獲得的深入見解之門。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover BioEmu]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 BioEmu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From forming muscle fibers to protecting us from disease, proteins play an essential role in almost all biological processes in humans and other life forms alike. There has been extraordinary progress in recent years toward better understanding protein structures using deep learning, enabling the accurate prediction of protein structures from their amino acid sequences. However, predicting a single protein structure from its amino acid sequence is like looking at a single frame of a movie—it offers only a snapshot of a highly flexible molecule. Biomolecular Emulator-1 (BioEmu-1) is a deep-learning model that provides scientists with a glimpse into the rich world of different structures each protein can adopt, or structural ensembles, bringing us a step closer to understanding how proteins work. A deeper understanding of proteins enables us to design more effective drugs, as many medications work by influencing protein structures to boost their function or prevent them from causing harm.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[從形成肌肉纖維到保護我們免受疾病的侵害，蛋白質在幾乎所有人類及其他生命形式的生物過程中扮演著重要角色。近年來，在利用深度學習更好地理解蛋白質結構方面取得了非凡的進展，這使得我們能夠根據氨基酸序列準確預測蛋白質結構。然而，從氨基酸序列預測單一蛋白質結構就像是觀看電影中的一個畫面——它僅提供了這種高度靈活分子的快照。生物分子模擬器-1 (BioEmu-1) 是一種深度學習模型，為科學家提供了不同蛋白質可以採用的豐富結構或結構集的洞察，讓我們更接近理解蛋白質的運作方式。對蛋白質的更深入理解使我們能夠設計出更有效的藥物，因為許多藥物的作用是通過影響蛋白質結構來增強其功能或防止其造成傷害。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu-1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu-1]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get BioEmu model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 BioEmu 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTryItOutGitHub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open BioEmu repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 BioEmu 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A scatter plot graph showing the average score of 11 benchmarks on the y-axis and memory footprint in gigabytes (GB) on the x-axis for various open-weight large language models (LLMs). The Pareto Frontier of Open-weight LLMs is indicated by a blue dashed line. Data points include Qwen2-5.3B, BitNet b1.58 2B (marked with a red star as an outlier with low memory footprint), Qwen2-5.1-5B, SmolLM2-1.7B, MiniCPM-2B, LLaMa-2-13B, Gemma-3-13B, and Qwen2-0.5-8B. The image shows a comparison of different large language models based on their performance and memory usage, highlighting which models are more efficient or powerful relative to their memory footprint. This is relevant for understanding trade-offs in model design and deployment efficiency in machine learning applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[散佈圖圖表的 y 軸顯示了 11 個基準的平均分數，而 x 軸則是各種開放權數大型語言模型 (LLM) 的磁碟使用量 (以 GB 為單位)。開放權數 LLM 的 Pareto 先驅由藍色虛線表示。資料點包括 Qwen2-5.3B、BitNet b1.58 2B (以紅色星號標示為磁碟使用量較低的極端值)、Qwen2-5.1-5B、SmolLM2-1.7B、MiniCPM-2B、LLaMa-2-13B、Gemma-3-13B 和 Qwen2-0.5-8B。該圖像會顯示根據其效能和記憶體使用量對不同大型語言模型進行的比較，並突顯出相對於其磁碟使用量，哪些模型更有效率或功能更強大。這對於理解機器學習應用程式中的模型設計與部署效率的權衡非常重要。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research, BitNet b1.58 2B4T is the first open-source, native 1-bit large language model (LLM) in which every parameter is ternary (i.e., -1, 0, 1), at a 2-billion parameter scale. Trained on a corpus of 4 trillion tokens, this model demonstrates that native 1-bit LLMs can achieve performance comparable to leading open-weight, full-precision models of similar size, while offering substantial advantages in computational efficiency, including substantially reduced memory footprint, energy consumption, and decoding latency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 Microsoft Research 開發的 BitNet b1.58 2B4T 是第一個開放原始碼的原生 1 位元大型語言模型 (LLM)，其每個參數均為三元 (即 -1、0、1)，參數規模達到 20 億。該模型在 4 兆個權杖的主體上進行訓練，顯示出原生 1 位元 LLM 達到的效能可與類似規模的開放權數全精確度模型相媲美，同時在計算效率方面提供了顯著的優勢，包括大幅降低磁碟使用量、能源使用量和解碼延遲。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about BitNet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 BitNet]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft researchers have demonstrated that BitNet b1.58 2B4T achieves performance on par with leading open-weight, full-precision LLMs of similar size, while offering significant advantages in computational efficiency, including substantially reduced memory footprint, energy consumption, and decoding latency. To facilitate further research and adoption, the model weights have been released along with open-source inference implementations for both GPU and CPU architectures.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 研究人員已證明，BitNet b1.58 2B4T 達到的效能與相似規模的領先開放權數全精確度 LLM 旗鼓相當，同時在計算效率方面提供了顯著的優勢，包括大幅降低磁碟使用量、能源使用量和解碼延遲。為了促進進一步的研究和採用，模型權數已與針對 GPU 和 CPU 架構的開放原始碼推斷實作一起發布。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BitNet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open BitNet repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 BitNet 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BitNet has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet 供研究用途發行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An animated sequence showing how a chain of amino acids folds into a protein’s three-dimensional structure using the natural sequences predicted by EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一個動畫序列，顯示胺基酸鏈如何使用 EvoDiff 預測的自然序列摺疊成蛋白質的三維結構]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff is a general-purpose diffusion framework that combines evolutionary-scale data with the distinct conditioning capabilities of diffusion models for controllable protein generation in sequence space. EvoDiff generates high-fidelity, diverse, and structurally-plausible proteins that cover natural sequence and functional space.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff 是一項一般用途擴散架構，其將進化規模的資料與擴散模型的相異調節功能結合，以在序列空間中獲得可控制的蛋白質產生。EvoDiff 會產生高逼真度、多樣化且結構上貌似真實的蛋白質，涵蓋自然序列和功能空間。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 EvoDiff]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Critically, EvoDiff can generate proteins inaccessible to structure-based models, such as those with disordered regions, while maintaining the ability to design scaffolds for functional structural motifs, demonstrating the universality of our sequence-based formulation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[關鍵來說，EvoDiff 可以產生無法供結構型模型模型 (例如有失序區域的模型) 使用的蛋白質，同時維持設計功能性結構模體支架的能力，展現我們以序列為基礎的配方的普遍性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff will expand capabilities in protein engineering beyond the structure-function paradigm toward programmable, sequence-first design. The sequence and MSA models – EvoDiff-Seq and EvoDiff-MSA, respectively – were evaluated across a range of generation tasks to demonstrate their power for controllable protein design.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff 將蛋白質工程中的功能擴大，超出結構-功能的典範，成為可程式化、序列優先的設計。序列和 MSA 模型 (分別為 EvoDiff-Seq 和 EvoDiff-MSA) 已跨一系列產生工作進行評估，以展現其對於可控制的蛋白質設計的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTryItOutAzureAI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get EvoDiff model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 EvoDiff 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTryItOutGithub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open EvoDiff repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 EvoDiff 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* A gradient blue to green background features a white flowchart with rectangular boxes connected by arrows, ending in a hexagonal “STOP” sign and a check mark on the right side.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*裝飾* 漸層藍到綠色背景具有白色流程圖，其中矩形方塊由箭頭連接，結尾為六邊形的 「停止」符號和右側的複選標記。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT 是一種教導 AI 代理程式更有效率地探索的方法，可讓他們以智慧方式瀏覽其環境、收集有價值的資訊、評估選項，以及找出最佳決策制定與規劃策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Teach AI agents to explore more effectively.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[教導 AI 代理程式更有效率地探索。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover ExACT]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 ExACT]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Autonomous AI agents are transforming the way we approach multi-step decision-making processes, streamlining tasks like web browsing, video editing, and file management. By applying advanced machine learning, they automate workflows, optimize performance, and reduce the need for human input.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自主 AI 代理程式正在轉換我們處理多步驟決策程式、串流工作的方式，例如網頁流覽、影片編輯及檔案管理。透過套用進階機器學習，其會自動化工作流程、優化效能，並降低人類輸入的需要。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[However, these systems struggle in complex, dynamic environments. A key challenge lies in balancing exploitation, using known strategies for immediate gains, with exploration, which involves seeking new strategies that could yield long-term benefits. Additionally, they often have difficulty adapting to unpredictable changes in conditions and objectives, as well as generalizing knowledge across contexts, limiting their ability to transfer learned strategies between domains.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不過，這些系統會在複雜的動態環境中進行困難。關鍵挑戰在於平衡惡意探索，利用已知策略立即獲得收益，並探索，其涉及搜尋可能產生長期利益的新策略。此外，它們通常難以適應條件和目標的無法預測變更，以及跨內容的一般化知識，限制他們在網域之間轉移已知策略的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In response, Microsoft researchers have developed ExACT, an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為了回應，Microsoft研究人員開發了 ExACT，這種方法可教導 AI 代理程式更有效率地探索，讓他們能夠智慧地瀏覽其環境、收集有價值的資訊、評估選項，以及找出最佳的決策制定和規劃策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[精確]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open ExACT repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 ExACT 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The MCP Server for Azure AI Foundry Labs is designed to supercharge team velocity in adopting and evaluating breakthrough AI research. By equipping GitHub Copilot with custom tools for intelligent model discovery, tailored implementation guidance, and rapid prototyping, this can achieve exponential productivity gains and reduce the “idea-to-prototype” cycle to under 10 minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[適用於 Azure AI Foundry Labs 的 MCP 伺服器的設計目的是要促進小組採用和評估創新 AI 研究的速度。透過為 GitHub Copilot 提供智慧型模型探索的自訂工具、量身打造的實作指導和快速的原型建立，這可達成指數型生產力提升，並將「構想至原型」週期縮短到 10 分鐘以內。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about the MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 MCP 伺服器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Unified Model Discovery:</strong> Instantly list 45+ models (Microsoft Research, OpenAI, Meta, Mistral, and Azure Foundry Labs specialties) inside your coding environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>整合模型探索:</strong> 在您的程式碼編寫環境中快速列出超過 45 個模型 (Microsoft Research、OpenAI、Meta、Mistral 和 Azure Foundry Labs 專長)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Implementation Guidance On Demand:</strong> GitHub Copilot receives detailed integration documentation and usage hints for each model—reducing hallucination and speeding up “from idea to working code."]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>隨選實作指導:</strong> GitHub Copilot 會收到每個模型的詳細整合文件和使用提示，藉以減少幻覺並加速實現「從構想到工作程式碼」。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Seamless Copilot Integration:</strong> GitHub Copilot is enhanced via MCP servers to understand model endpoints, available tools, and recommend best-fit models for your use case.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>無縫 Copilot 整合:</strong> GitHub Copilot 透過 MCP 伺服器增強，可了解模型端點、可用的工具，並建議最適合您的使用案例的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Generative Coding Without the Chaos:</strong> “Prototyping without the rabbit holes.” The MCP Server constrains and guides the AI, avoiding runaway file generation, dead ends, or coding spirals typical of other agentic tools.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>沒有混亂的生成式程式碼編寫:</strong>「建立原型而不遇到複雜的情況」。MCP 伺服器會約束和引導 AI，避免檔案產生失控、困境，或其他代理程式工具的程式碼編寫螺旋典型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Prototyping at Lightning Speed:</strong> Build evaluators, dashboards, analyzers, and bespoke AI apps in minutes. Typical initial working apps are generated in <10 minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong>光速建立原型:</strong> 在數分鐘內建置評估工具、儀表板、分析程式，以及客製化 AI 應用程式。一般的初始工作應用程式會在 10 分鐘內產生。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MCP 伺服器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get started with the MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開始使用 MCP 伺服器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Skala functional will enable more accurate, scalable predictions in computational chemistry. It starts with the largest high-accuracy dataset ever built for training deep-learning-based density functional theory (DFT) models. This dataset underpins Skala—coming soon to the Azure AI Foundry catalog—a new machine-learned exchange-correlation functional that reaches experimental accuracy for atomization energies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skala 泛函將在計算化學中實現更精確且可擴展的預測。它始於有史以來最大規模、用於訓練深度學習密度泛函理論 (DFT) 模型的高準確度資料集。這個資料集是 Skala 的基礎，這是一個即將在 Azure AI Foundry 目錄推出的新型機器學習交換相互關聯泛函，能在原子化能量的預測上達到實驗級準確度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore MSR-ACC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 MSR-ACC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCShortenedTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MSR-ACC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MSR-ACC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This research marks a major advance in computational chemistry by dramatically improving the accuracy of <em>density functional theory</em> (DFT)—the most widely used method for simulating materials and molecules. The core breakthrough is a new deep-learning-based exchange-correlation (XC) functional, called Skala, which achieves <em>experimental-level accuracy</em> in predicting molecular properties like atomization energy—something previously thought out of reach for DFT. Skala will be available in the Azure AI Foundry catalog in the future. Researchers have released a large part of this dataset to the scientific community.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這項研究在計算化學領域取得重大突破，大幅提升了<em>密度泛函理論</em> (DFT) 的準確度，這是目前最廣泛用於模擬材料與分子的計算方法。這項核心突破是名為 Skala 的新型深度學習交換相互關聯 (XC) 泛函，它在預測原子化能等分子性質方面達到<em>實驗級準確度</em>，這在過去被認為是 DFT 難以達到的目標。Skala 將於未來在 Azure AI Foundry 目錄中推出。研究人員已將這個資料集的大部分內容公開釋出給科學社群。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DFT is fast but limited by manual approximations of the XC functional. The research team addressed that limitation by generating the largest high-accuracy dataset of molecular energies to date, leveraging first principles methods and cloud-scale computation. They then trained Skala to learn directly from electron densities, bypassing hand-crafted feature engineering that has stalled progress for decades.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DFT 雖然速度快，但受到 XC 泛函手動近似的限制。研究團隊利用第一原理方法和雲端計算產生了迄今為止最大的分子能量高精度資料集，解決了這個限制。他們隨後訓練 Skala 直接從電子密度中學習，跳過了數十年來阻礙進展的手工特徵工程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This achievement removes a long-standing barrier in computational chemistry, enabling DFT to shift from interpreting experimental results to predicting them reliably. That unlocks enormous potential across domains—from drug design to battery development—where accurate, affordable simulations can replace costly lab work.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這項成就打破了計算化學領域長久以來的障礙，讓 DFT 得以從解釋實驗結果，轉變為可靠地預測實驗結果。這將釋放橫跨多個領域的巨大潛力 (從藥物設計到電池開發)，在這些領域中精確且低成本的模擬有望取代昂貴的實驗室工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Research Accurate Chemistry Collection (MSR-ACC)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Research Accurate Chemistry Collection (MSR-ACC)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Diagram with five items (‘Orchestrator,’ ‘Coder,’ ‘FileSurfer,’ ‘WebSurfer,’ ‘ComputerTerminal’) connected to a single point.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[圖表中有五個專案 ('Orchestrator，' 'Coder，' 'FileSurfer，' 'WebSurfer，' 'ComputerTerminal' ) 連接到單一點。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One is a generalist multi-agent system created to address intricate web and file-based tasks. By utilizing an intelligent Orchestrator alongside specialized agents, it facilitates the automation of complex, multi-step activities across various environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 是一個一般性的多代理程式系統，用來處理複雜的Web和檔案工作。透過利用智慧型 Orchestrator 與特殊代理程式，可協助自動化各種環境中的複雜多步驟活動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One introduces advancements in agentic AI through its modular architecture, which features a lead agent termed the Orchestrator. This component manages a network of specialized agents, enabling each to concentrate on specific tasks, such as web navigation, code execution, or local file management. This structure supports the efficient pursuit of complex objectives.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 透過代理式 AI 的模組化架構，引進代理式 AI 中發展，其特色為主要代理程式即 Orchestrator。此元件會管理特定代理程式的網路，讓每個代理程式都能專心處理特定工作，例如 Web 瀏覽、程式代碼執行或本機檔案管理。這個結構支持複雜目標的有效實現。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Central to Magentic-One’s operation are its dual planning mechanisms: the Task Ledger and the Progress Ledger. The Task Ledger empowers the Orchestrator to formulate strategic approaches, while the Progress Ledger provides real-time updates on task statuses. This interconnected system allows for ongoing evaluation and adjustment, optimizing overall efficiency. In situations where obstacles arise, the Orchestrator can adapt plans and reallocate tasks, ensuring effective workflow management under varying conditions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 作業的核心是雙規劃機制： 工作總賬和進度總賬。工作總賬可讓 Orchestrator 調整策略方法，而進度總賬則會針對任務狀態提供即時更新。這個互連式系統允許進行評估和調整，優化整體效率。在發生障礙的狀況下，Orchestrator 可以調整計劃並重新配置工作，確保在不同條件下有效率的工作流程管理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 Magentic-One]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Try Magentic-One with AutoGen]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The image shows a bar chart comparing accuracy percentages across different systems on the medium subset of the GAIA benchmark.: Magentic-One (about 33%), Webby autonomous (about 38%), Webby + Simulated Human (about 58%), and Human (about 90%).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[影像顯示橫條圖，比較 GAIA 效能評定的中子集上不同系統的正確性百分比。: Magentic-One (約 33%)、Webby 自主 (約 38%)、Webby + 模擬人類 (約 58%) 和人類 (約 90%)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlocking the full potential of AI requires the development of effective mechanisms for human-AI collaboration. By reducing cognitive load while ensuring users remain in control, AI can significantly enhance human capabilities and streamline complex workflows. Magentic-UI was designed with this goal in mind, serving as a research platform aimed at advancing research on human-in-the-loop experiences.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要充分發掘 AI 的潛力，需要開發人類與 AI 共同作業的有效機制。透過降低認知負載，同時確保使用者保持在控制，AI 能大幅增強人類能力並簡化複雜的工作流程。Magentic-UI 的設計正是基於此目標，作為研究平台，旨在推動有關「人類參與循環」(HITL) 體驗的研究。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about Magentic-UI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 Magentic-UI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI builds on  Magentic-One, a generalist multi-agent system that specializes in complex web and file-based tasks, and is powered by {AutoGenLink}, our leading agent framework.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 以 Magentic-One 為建置基礎，這是一種跨領域的多代理程式系統，專精於複雜的 Web 和檔案型工作，並且由我們領先的代理程式架構 {AutoGenLink} 提供。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key features of the Magentic-UI research include:<ol><li><strong>Co-planning.</strong> Magentic-UI allows users to directly modify its plan through a plan editor or by providing textual feedback before Magentic-UI executes any actions.</li><li><strong>Co-tasking.</strong> Users can pause the system and give feedback in natural language or demonstrate it by directly taking control of the browser.</li><li><strong>Action guards.</strong> Magentic-UI seeks user approval before executing potentially irreversible actions, and the user can specify how often Magentic-UI needs approvals. Furthermore, Magentic-UI is sandboxed for the safe operation of tools such as browsers and code executors.</li><li><strong>Task learning.</strong> Magentic-UI can learn and save plans from previous interactions to improve task completion for future tasks.</li></ol>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 研究的主要功能包括:<ol><li><strong>共同規劃。</strong>Magentic-UI 可讓使用者在 Magentic-UI 執行任何動作之前，透過計劃編輯器直接修改其計劃或提供文字意見反應。</li><li><strong>共同作業。</strong>使用者可以暫停系統，並以自然語言提供意見反應，或直接控制瀏覽器來示範。</li><li><strong>動作保護。</strong>Magentic-UI 會先尋求使用者核准，再執行可能無法復原的動作，且使用者可以指定 Magentic-UI 需要核准的頻率。此外，Magentic-UI 已沙盒化，可進行瀏覽器和程式碼執行程式等工具的安全作業。</li><li><strong>工作學習。</strong>Magentic-UI 可以從先前的互動中學習並儲存計劃，以改善未來工作的工作完成。</li></ol>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUITitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUITryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Magentic-UI repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 Magentic-UI 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A mechanical employee operating a large green machine in an industrial setting. He’s using a control panel with his right hand and holding a tablet in the other.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[機械員工在工業環境中作大型綠色機器。他使用控制面板，右手拿著平板電腦。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Internal wiring and components of a device with green check marks and orange circles indicating areas of interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有綠色複選標記和橙色圓形的裝置內部線路和元件，指出感興趣的區域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in digital and physical environments. Magma builds on the foundation models paradigm that pretraining on a larger amount of more diverse datasets allows these models to generalize better to new tasks and environments. Magma can perceive visual and textual inputs and generate actions, whether it’s clicking a button in a user interface or grabbing a tool in the real world. This new model represents a significant step towards AI agents that can serve as general-purpose assistants.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[Magma]5D; 是一種多模式基礎模型，設計用來瞭解數位及實體環境中的數位及實際運作。在基礎模型參數上建置的岩漿組建，在更多更不同的數據集上預先訓練，可讓這些模型對新的工作和環境進行更完善的一般化。無論是單擊使用者介面中的按鈕或擷取真實世界中的工具，Magma 都可以感知視覺與文字輸入併產生動作。這個新模型代表邁向可作為一般用途助理之 AI 代理程式的重要步驟。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A multimodal AI foundation model designed to both understand and act in digital and physical environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多模組 AI 基礎模型，設計用於瞭解及在數位和實體環境中運作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Magma]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索岩漿]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Imagine an AI assistant that can book a meeting online and also set up the room for it – navigating software menus as effortlessly as it moves physical objects. Such seamless integration of digital and physical tasks has long been a sci-fi vision. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[試想一下可在線預約會議的 AI 助理，也可以為它設定會議室 – 在行動實體物件時，輕鬆瀏覽軟體功能表。數字和實體工作的順暢整合，是一個科幻的視覺。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft researchers are bringing it closer to reality with Magma, a multimodal AI foundation model designed to both understand and act in digital and physical environments. Magma builds on the foundation models paradigm, that pretraining on a larger amount of more diverse datasets allows these models to generalize better to new tasks and environments. Magma can perceive visual and textual inputs and generate actions, whether it’s clicking a button in a user interface or grabbing a tool in the real world. This new model represents a significant step towards AI agents that can serve as general-purpose assistants. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft研究人員利用Magma，讓它更接近現實。Magma是一種多模組 AI 基礎模型，設計用於瞭解數位及實體環境中並加以運作。以基礎模型參數為基礎建置的岩漿，在更多更不同的數據集上預先訓練，可讓這些模型對新工作和環境更能一般化。無論是單擊使用者介面中的按鈕或擷取真實世界中的工具，Magma 都可以感知視覺與文字輸入併產生動作。這個新模型代表邁向可作為一般用途助理之 AI 代理程式的重要步驟。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Vision-Language-Action (VLA) models are typically pretrained on large amounts of vision-language-action datasets to obtain the vision-language understanding ability (verbal intelligence) and the ability to perceive and interact with the visual spatial world to perform a wide range of tasks (spatial intelligence). However, due to the dramatic difference among various digital and physical environments, separate VLA models are trained and used for different environments. These models cannot easily generalize to new tasks and new environments that are unseen in training data. Moreover, most of these models do not leverage pretrained vision-language (VL) models or diverse vision-language datasets. As a result, their vision language understanding ability is often inferior to state-of-the-art VL models, which further limits model generalizability.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Vision-Language-Action (VLA) 模型通常會在大量的視覺語言動作數據集上預先訓練，以取得視覺語言理解能力，(語言智慧)，以及感知視覺空間世界並與之互動的能力，(空間智慧) 執行各種工作。然而，由於各種數位和實體環境之間的顯著差異，不同的 VLA 模型會經過訓練，並用於不同的環境。這些模型無法輕易地對訓練數據中未顯示的新工作和新環境進行一般化。此外，這些模型大部分不會利用預先訓練的視覺語言 (VL) 模型或不同的視覺語言數據集。因此，他們的視覺語言理解能力通常會受到最先進的 VL 模型的限制，進一步限制模型的一般性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma, is a VLA foundation model that can adapt to downstream (unseen) agentic tasks in both the digital and physical environments. With Magma, researchers showed that it is beneficial to pretrain a single VLA model for AI agents across these environments while still achieving state-of-the-art results on UI navigation and robotic manipulation tasks, outperforming previous models that are tailored specifically to these tasks. On VL tasks, Magma also compares favorably to popular VL models that are trained on much larger datasets.  ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[Magma]5D; 是 VLA 基礎模型，可在數位和實體環境中調整至下游 (未) 代理程式工作。使用 [Magma]5D; 後，研究人員表示您可以在這些環境中預先為 AI 代理程式預先訓練單一 VLA 模型，同時在 UI 導覽和機器人作工作上，仍能取得最先進的結果，進而驗證專為這些工作量身打造的舊模型。在 VL 工作上，[Magma]5D; 也會優先與在較大數據集上訓練的熱門 VL 模型比較。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[岩漿]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Magma model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得Magma模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Various molecular structures and crystal lattices displayed in a grid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[網格線中顯示各種分子結構和水晶板。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim is a deep learning model for accurate and efficient materials simulation and property prediction over a broad range of elements, temperatures and pressures to enable in silico materials design.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 是一種深入的學習模型，可讓您精確且有效率地模擬數據，並針對廣泛的元素進行屬性預測、加強和預測，以在 Silico 材料設計中啟用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore MatterSim]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 MatterSim]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim employs deep learning to understand atomic interactions from the very fundamental principles of quantum mechanics, across a comprehensive spectrum of elements and conditions—from 0 to 5,000 Kelvin (K), and from standard atmospheric pressure to 10,000,000 atmospheres.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 運用深度學習，從量子機制的非常基本原則，跨全方位的元素和條件，從 0 到 5,000 Kelvin (K)，以及從標準大氣壓力到 10,000,000 個氣壓之間的原子互動。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[MatterSim is a deep-learning model for accurate and efficient materials simulation and property prediction over a broad range of elements, temperatures, and pressures to enable the in silico materials design. MatterSim employs deep learning to understand atomic interactions from the very fundamental principles of quantum mechanics, across a comprehensive spectrum of elements and conditions—from 0 to 5,000 Kelvin (K), and from standard atmospheric pressure to 10,000,000 atmospheres.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim efficiently handles simulations for a variety of materials, including metals, oxides, sulfides, halides, and their various states such as crystals, amorphous solids, and liquids. Additionally, it offers customization options for intricate prediction tasks by incorporating user-provided data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 有效率地處理各種材質的模擬，包括金屬、蟲子、萢液、光菘及其各種狀態，例如水晶、非定型固件和液體。此外，它會合併使用者提供的數據，為複雜的預測工作提供自定義選項。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get MatterSim model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 MatterSim 模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Try MatterSim]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research in collaboration with game studio Ninja Theory, Muse is a World and Human Action Model (WHAM) - a generative AI model of a video game that can generate game visuals, controller actions, or both. Trained exclusively on the game Bleeding Edge, researchers and game creatives can explore how these model capabilities will have potential to accelerate their creativity in the future.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 Microsoft Research 與 game studio Ninja Collaboration 共同開發，Muse 是一款世界與人類動作模型 (WHAM) - 一款可產生遊戲視覺效果、控制器動作或兩者皆可產生影片遊戲的產生 AI 模型。在遊戲 Bleeding Edge 上獨佔訓練，研究人員和遊戲創意者可以探索這些模型功能未來如何提升其創意。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Developed by Microsoft Research in collaboration with game studio Ninja Theory, Muse is a World and Human Action Model (WHAM) - a Generative AI of a video game that can generate game visuals, controller actions, or both. Trained exclusively on the game Bleeding Edge, researchers and game creatives can explore how these model capabilities will have potential to accelerate their creativity in the future.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseDownloadDemo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download demonstrator app]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下載下載下載應用程式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A generative AI model that can generate visuals and controller actions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可產生視覺效果與控制器動作的一般 AI 模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[A Generative AI model that can generate visuals and controller actions.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Muse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索馬斯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse was trained on more than 1 billion images and controller actions, from the game Bleeding Edge, corresponding to over 7 years of continuous human gameplay.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Muse 已從遊戲 Bleeding Edge 進行超過 10 億個影像和控制器動作的訓練，對應到超過 7 年的連續人類遊戲。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The WHAM demonstrator app provides a visual interface for interacting with a deployment of the Muse model instance on Azure AI Foundry. Creators can load a screenshot from Bleeding Edge as an initial prompt, then use the model to generate multiple potential continuations of gameplay from this starting point. They can then explore the generated sequences and tweak them, such as changing the controller inputs or pasting game elements into the scene and predicting what will happen as a result. These features demonstrate how Muse’s capabilities could someday enable AI-supported iteration and brainstorming as part of the creative process.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[WHAM 群組應用程式提供可視化介面，以便在 Azure AI Foundry 上與使用者模型實例的部署互動。創作者可以從 Bleeding Edge 載入螢幕快照作為初始提示，然後使用模型從此起始點產生多個可能的連續遊戲。然後，它們可以探索產生的順序並進行調整，例如變更控制器輸入，或將遊戲元素貼到場景中，以及預測結果。這些功能示範了使用者的功能如何在某一天啟用 AI 支援的反覆運算和腦力激蕩，作為創意程式的一部分。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The WHAM demonstrator app provides a visual interface for interacting with a deployment of the Muse model instance on Azure Foundry. Creators can load a screenshot from Bleeding Edge as an initial prompt, then use the model to generate multiple potential continuations of gameplay from this starting point. They can then explore the generated sequences and tweak them, such as changing the controller inputs or pasting game elements into the scene and predicting what will happen as a result. These features demonstrate how Muse’s capabilities could someday enable AI-supported iteration and brainstorming as part of the creative process.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[繆斯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Muse model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 Muse 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Focus on the grid of 9 AI generated gaming video frames by the Muse model set in the same scene. On the left, showing 3 ground truth (original) scenes that are compared to the AI generated frames.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*DECORATIVE* 將焦點放在 9 個 AI 產生的遊戲影片框架中，由在相同場景中設定的使用者模型所產生。在左側，顯示 3 個真實 (與 AI 產生的框架比較的原始) 場景。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by the Muse model set in the same scene. The frames highlight differences when the game player takes a left, center, or right path.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*DECORATIVE* 9 個 AI 產生遊戲影片框架的網格線，由在相同場景中設定的 Muse 模型所產生。當玩家取得左、中或右路徑時，框架會醒目提示不同之處。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by the Muse model crafted in the creator tool. This game creator surface showcases and Xbox controller that can be used to impact the AI generate frames.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*DECORATIVE* 9 個 AI 產生遊戲影片框架的網格線，這些影片由 Muse 模型在創作者工具中製作而成。此遊戲創作者表面展示櫃和 Xbox 控制器，可用來影響 AI 產生框架。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Navigation.NextArticleText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下一個]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Next article]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Navigation.PreviousArticleText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Previous]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上一個]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Previous article]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhancing the ability of coding models to handle diverse editing requirements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增強編碼模型處理各種編輯需求的能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover NextCoder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Software engineering activities frequently involve edits to existing code. However, contemporary code language models lack the ability to handle diverse types of code-edit requirements. In this work, Microsoft researchers attempt to overcome this shortcoming through a novel synthetic data generation pipeline and a robust model adaptation algorithm. Starting with seed code examples and diverse editing criteria, their pipeline generates high-quality samples comprising original and modified code, along with natural language instructions in different styles and verbosity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[軟體工程活動經常涉及現有程式碼的編輯。不過，當代的程式碼語言模型無法處理各種類型的程式碼編輯需求。在此工作中，Microsoft 研究人員嘗試透過新穎的綜合資料產生管線和強大的模型調適演算法來克服這個缺點。從種子程式碼範例和各種編輯準則開始，其管線會產生高品質的範例，包含原始和修改過的程式碼，以及不同樣式和詳細程度的自然語言指示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Today’s code LMs come bundled with strong abilities, such as code generation and instruction following, which should not be lost due to fine-tuning. To ensure this, researchers proposed a novel adaptation algorithm, SeleKT, that (a) leverages a dense gradient-based step to identify the weights that are most important for code editing, and (b) does a sparse projection onto the base model to avoid overfitting.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[現今的程式碼 LM 結合了強大的能力 (例如程式碼產生和遵循指示)，這不應該因為微調而遺失。為了確保這點，研究人員提議了新穎的調適演算法 (SeleKT)，該演算法 (a) 利用密集梯度型步驟來識別程式碼編輯最重要的權數，以及 (b) 對基本模型進行稀疏投影以避免過度學習。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Using this approach, researchers obtained a new series of models called NextCoder (adapted from QwenCoder-2.5) that achieves strong results on five code-editing benchmarks, outperforming comparable size models and even several larger ones. In their research paper, they demonstrate the generality of their approach on two model families (DeepSeekCoder and QwenCoder), compare against other fine-tuning approaches, and demonstrate robustness by showing retention of code generation and general problem-solving abilities post adaptation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用這個方法，研究人員取得了一系列名為 NextCoder (從 QwenCoder-2.5 調適) 的新模型，在五個程式碼編輯基準上達成豐碩的結果，其表現優於同等規模的模型，甚至優於數個更大的模型。在其研究論文中，他們透過兩個模型系列 (DeepSeekCoder 和 QwenCoder) 展現其方法的普遍性、與其他微調方法比較，以及藉由顯示調適後程式碼產生的保留和一般問題解決能力來展現實力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are now available for experimental purposes on Azure AI Foundry.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這些模型現在可在 Azure AI Foundry 上用於實驗用途。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NextCoder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try NextCoder in Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Foundry 中試用 NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Parsed Teams screenshot image by OmniParser.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 OmniParser 剖析的 Teams 螢幕快照影像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is an advanced vision-based screen parsing module that converts user interface (UI) screenshots into structured elements, allowing agents to execute actions across various applications using visual data . By harnessing large vision-language model capabilities, OmniParser improves both efficiency and accuracy in UI interactions. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 是進階視覺型螢幕剖析模組，可將使用者介面 (UI) 螢幕快照轉換為結構化元素，讓代理程式可以使用視覺數據在各種應用程式之間執行動作。藉由利用大型視覺語言模型功能，OmniParser 可提高UI互動的效率與準確度。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[OmniParser is an advanced vision-based screen parsing module that converts user interface (UI) screenshots into structured elements, allowing agents to execute actions across various applications using visual data . By harnessing large vision-language model capabilities, OmniParser improves both efficiency and accuracy in UI interactions.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Turn any LLM into a computer use agent ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[將任何 LLM 轉換為電腦使用代理程式 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore OmniParser V2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 OmniParser V2]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Click to learn more!]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recent developments in large vision-language models (VLMs), such as GPT-4V and GPT-4o, showcase their potential in creating agent systems that integrate smoothly within user interfaces. However, the practical application of these multimodal models, especially as general agents across different operating systems, faces challenges. A significant barrier to progress has been the absence of reliable screen parsing techniques that can effectively identify interactable icons and link intended actions to specific screen regions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大型視覺語言模型 (VLM) 的最新發展，例如 GPT-4V 和 GPT-4o，展示其在建立可在使用者介面中順暢整合的代理程式系統方面的潛在潛力。然而，這些多模式模型的實用應用程式，特別是不同操作系統的一般代理程式，面臨挑戰。進度的一大障礙是缺少可靠的螢幕剖析技術，可有效地識別可互動的圖示，並將預定的動作連結到特定螢幕區域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser addresses this limitation through its compact and powerful architecture. It transforms UI screenshots into structured output elements, enabling the design of agents that can perform precise actions across various applications. When combined with models like GPT-4V, OmniParser markedly improves the agent's capability to engage accurately with user interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 透過其壓縮且強大的架構來解決此限制。它會將UI螢幕快照轉換為結構化輸出元素，讓代理程式可以跨各種應用程式執行精確動作的設計。當與 GPT-4V 等模型搭配使用時，OmniParser 會明確改善代理程式的功能，以正確地與使用者介面互動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser V2 takes this capability to the next level. Compared to its predecessor, It achieves higher accuracy in detecting smaller interactable elements and faster inference, making it a useful tool for GUI automation. In particular, OmniParser V2 is trained with larger size of interactive element detection data and icon functional caption data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser V2 將此功能帶到下一個層級。與其前置任務相比，它在偵測較小的可互動元素和更快的推斷方面達到較高的精確度，因此它是一個適用於 GUI 自動化的工具。特別是，OmniParser V2 已使用較大的互動式元素偵測數據和圖示功能 標題 數據進行訓練。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The creation of OmniParser involved the development of specialized datasets, including an interactable icon detection dataset that identifies actionable regions within popular web pages, and an icon description dataset that correlates UI elements with their functions. These resources are crucial for training the detection and captioning models utilized by OmniParser. The detection model, specifically fine-tuned on the interactable icon dataset, reliably locates actionable screen regions, while the captioning model provides contextually relevant descriptions for the detected elements.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The creation of OmniParser involved the development of specialized datasets, including an interactable icon detection dataset that identifies actionable regions within popular web pages, and an icon description dataset that correlates UI elements with their functions. These resources are crucial for training the detection and captioning models utilized by OmniParser. The detection model, specifically fine-tuned on the interactable icon dataset, reliably locates actionable screen regions, while the captioning model provides contextually relevant descriptions for the detected elements.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 的建立涉及特殊數據集的開發，包括可互動的圖示偵測數據集，可識別常用網頁內可採取動作的區域，以及將UI元素與其函數相互關聯的圖示描述數據集。這些資源對於訓練 OmniParser 所使用的偵測和輔助字幕模型而言十分重要。偵測模型，特別在可互動圖示數據集上微調，可可靠地尋找可採取動作的螢幕區域，而輔助字幕模型則提供偵測到之元素的內容相關描述。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[OmniParser is designed to be modular and adaptable, enhancing interactions across both PC and mobile platforms.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is designed to be modular and adaptable, enhancing interactions across both PC and mobile platforms.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 的設計設計為模組化且可適配，可加強計算機和行動平臺間的互動。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser V2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser V2]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[OmniParser]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open OmniParser repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 OmniParser 存放庫]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Get OmniParser V2 model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A colorful geological map with various regions marked in different colors and a detailed legend in Chinese on the right side.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一幅彩色地質圖，不同區域以多種顏色標示，右側附有中文圖例詳細說明。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE enhances multimodal large language models (MLLMs) with geologic expertise, enabling accurate interpretation of complex, high-resolution maps. By integrating structured extraction, domain knowledge, and reasoning, it supports critical tasks in disaster risk, resource discovery, and infrastructure planning—turning general AI into a specialized tool for geoscience.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 利用地質專業知識來增強多模式大型語言模型 (MLLM)，進而能正確解譯複雜的高解析度地圖。它透過整合結構化擷取、領域知識和推理，支援災害風險、資源探勘和基礎結構規劃的關鍵任務：將一般 AI 轉變為地球科學的專用工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore PEACE]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 PEACE]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE (emPowering gEologic mAp holistiC undErstanding) enhances multimodal large language models (MLLMs) for expert-level geologic map understanding. Geologic maps, which provide critical insights into the structure and composition of Earth’s subsurface and surface, are vital tools in disaster detection, resource exploration, and civil engineering. But their complexity—featuring high-resolution visuals, symbolic representations, and domain-specific knowledge—poses significant challenges for current AI models. General-purpose MLLMs often fall short when interpreting such data due to the intricacies of cartographic generalization and geoscientific reasoning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE (emPowering gEologic mAp holistiC undErstanding) 增強多模式大型語言模型 (MLLM)，以達到專家級地質圖理解。地質圖提供地表下和地表結構與組合的重要深入解析，是災害偵測、資源探勘和土木工程的重要工具。但是其複雜度 (以高解析度視覺效果、符號表示法和特定領域知識為特徵) 對目前的 AI 模型構成了重大挑戰。由於製圖概括化和地球科學推理的錯綜複雜，通用型 MLLM 在解譯這類資料時往往會達不到要求。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To bridge this gap, Microsoft researchers and collaborators introduced GeoMap-Bench, the first benchmark specifically designed to evaluate MLLMs across five capabilities essential to geologic map interpretation: extracting, referring, grounding, reasoning, and analyzing. They also developed GeoMap-Agent, an AI system tailored to these challenges.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為了銜接這個差距，Microsoft 研究人員和共同合作者引進了 GeoMap-Bench，這是第一個特別針對地質圖解譯的五項必備功能評估 MLLM 所設計的基準：擷取、參考、建基、推理和分析。他們也開發了 GeoMap-Agent，這是專為這些挑戰量身打造的 AI 系統。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GeoMap-Agent is composed of three key modules:]A;<ul><li><strong>Hierarchical Information Extraction (HIE)</strong> for parsing structured content from complex maps,</li><li><strong>Domain Knowledge Injection (DKI)</strong> for embedding geological expertise, and</li><li><strong>Prompt-enhanced Question Answering (PEQA)</strong> for improved interpretive and reasoning capabilities.</li></ul>Together, these modules enable GeoMap-Agent to outperform existing models with superior accuracy and depth in geologic tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GeoMap-Agent 由三個主要模組組成：]A;<ul><li><strong>階層式資訊擷取 (HIE)</strong> 用於剖析複雜地圖中的結構化內容、</li><li><strong>領域知識注入 (DKI)</strong> 用於內嵌地質專業知識，以及</li><li><strong>提示增強式問題解答 (PEXML)</strong> 用於改善解譯和推理能力。</li></ul>這些模組共同使 GeoMap-Agent 在地質工作中以更高的正確性和深度超越現有的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rather than modifying MLLMs themselves, PEACE builds intelligent, domain-specific layers on top of them, turning general models into specialized agents capable of handling real-world geoscientific problems. This advancement marks a critical step toward applying AI in Earth science, empowering faster, more accurate geological assessments for both researchers and practitioners.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[除了修改 MLLM 本身之外，PEACE 還在其上建置智慧、特定領域層，將一般模型轉變為能夠處理真實世界地球科學問題的專門代理程式。這項進展在地球科學領域應用 AI 邁出了關鍵一步，使研究人員和從業人員能夠更快速、更準確地進行地質評估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open PEACE repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 PEACE 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 供研究用途發行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4BodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The text 'Phi-4' in glowing white letters on a purple and blue gradient background]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[紫色和藍色漸層背景上發光的白色字母 'Phi-4']]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4BodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Table showing Phi-4 benchmarks against comparable models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[顯示 Phi-4 與可比較模型的基準資料表]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore the capabilities of Phi-4, the latest model in Microsoft's Phi family of advanced AI technologies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Phi-4 的功能，這是 Microsoft Phi 系列中最新的進階 AI 技術模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4HomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Microsoft Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解 Microsoft Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal and Phi-4-mini, the newest models in Microsoft’s Phi family of small language models (SLMs) are now available. These models are designed to empower developers with advanced AI capabilities. Phi-4-multimodal, with its ability to process speech, vision, and text simultaneously, opens new possibilities for creating innovative and context-aware applications. Phi-4-mini, on the other hand, excels in text-based tasks, providing high accuracy and scalability in a compact form.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的 Phi 系列中最新的小型語言模型 (SLM) Phi-4-multimodal 和 Phi-4-mini 現已可供使用。這些模型旨在為開發人員提供進階的 AI 能力。Phi-4-multimodal 能夠同時處理語音、視覺和文字，為創建創新且具上下文感知的應用程式開啟了新的可能性。另一方面，Phi-4-mini 在基於文字的工作中表現出色，以緊湊的形式提供高正確性和可擴縮性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal marks a new milestone in Microsoft’s AI development as our first multimodal language model. At the core of innovation lies continuous improvement, and that starts with listening to our customers. In direct response to customer feedback, we’ve developed Phi-4-multimodal, a 5.6B parameter model, that seamlessly integrates speech, vision, and text processing into a single, unified architecture. By leveraging advanced cross-modal learning techniques, this model enables more natural and context-aware interactions, allowing devices to understand and reason across multiple input modalities simultaneously. Whether interpreting spoken language, analyzing images, or processing textual information, it delivers highly efficient, low-latency inference—all while optimizing for on-device execution and reduced computational overhead. Natively built for multimodal experiences Phi-4-multimodal is a single model with mixture-of-LoRAs that includes speech, vision, and language, all processed simultaneously within the same representation space. The result is a single, unified model capable of handling text, audio, and visual inputs seamlessly—no need for complex pipelines or separate models for different modalities. The Phi-4-multimodal is built on a new architecture that enhances efficiency and scalability. It incorporates a larger vocabulary for improved processing, supports multilingual capabilities, and integrates language reasoning with multimodal inputs. All of this is achieved within a powerful, compact, highly efficient model that’s perfectly suited for deployment on devices and edge computing platforms. This breakthrough model represents a major leap forward in AI technology, offering unprecedented performance in a small package. Whether you’re looking for advanced AI capabilities on mobile devices or edge systems, Phi-4-multimodal provides a high-capability option that’s both efficient and versatile. With its impressive range of capabilities and flexibility, Phi-4-multimodal opens exciting new possibilities for app developers, businesses, and industries looking to harness the power of AI in innovative ways. The future of multimodal AI is here, and it’s ready to transform your applications. Phi-4-multimodal is capable of processing both visual and audio together. The following table shows the model quality when the input query for vision content is synthetic speech on chart/table understanding and document reasoning tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-multimodal 標誌了 Microsoft AI 開發的新里程碑，成為我們首個多模態語言模型。創新的核心在於持續改進，而這一切始於傾聽客戶的聲音。為了直接回應客戶的反饋，我們開發了 Phi-4-multimodal，這是一個擁有 5.6B 參數的模型，能夠將語音、視覺和文本處理無縫整合到一個統一的架構中。通過利用進階的跨模態學習技術，該模型實現了更自然且具上下文感知的互動，使裝置能夠同時理解和推理多種輸入模態。無論是解譯口語語言、分析影像還是處理文本資訊，它都能提供高效、低延遲的推斷，並針對裝置執行進行最佳化，降低計算負擔。Phi-4-multimodal 是為多模態體驗而原生構建的單一模型，具備混合 LoRAs，能夠同時處理語音、視覺和語言，所有這些都在相同的表示空間內進行處理。最終結果是一個單一的統一模型，能夠無縫處理文本、音訊和視覺輸入，無需為不同模態設置複雜的管道或分開模型。Phi-4-multimodal 建立在新架構上，提升了效率和可擴縮性。它包含更大的詞彙以改善處理，支援多語言能力，並將語言推理與多模態輸入整合。所有這些都在一個強大、精簡且高效的模型中實現，該模型非常適合在裝置和邊緣計算平台上部署。這一突破性模型代表了 AI 技術的一次重大進展，並透過小模型提供了前所未有的效能。無論您是在行動裝置還是邊緣系統上尋找進階 AI 功能，Phi-4-multimodal 都提供了一個高效且多功能的選擇。憑藉其令人印象深刻的功能範圍和靈活性，Phi-4-multimodal 為應用程式開發人員、企業和行業開啟了令人興奮的新可能性，讓他們能夠以創新的方式利用 AI 的力量。多模態 AI 的未來已經來臨，並準備好改變您的應用程式。Phi-4 多模態能夠同時處理視覺和音訊。下表顯示當視覺內容的輸入查詢為針對圖表/資料表理解和文件推理工作的合成語音時，模型的品質。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal is capable of processing both visual and audio together. The following table shows the model quality when the input query for vision content is synthetic speech on chart/table understanding and document reasoning tasks. Compared to other existing state-of-the-art omni models that can enable audio and visual signals as input, Phi-4-multimodal achieves much stronger performance on multiple benchmarks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4 多模態能夠同時處理視覺和音訊。下表顯示當視覺內容的輸入查詢為針對圖表/資料表理解和文件推理工作的合成語音時，模型的品質。與其他現有的最先進全能模型 (能夠將音訊和視覺信號作為輸入) 相比，Phi-4 多模態在多項基準測試中展示了更强的效能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-mini is a 3.8B parameter model and a dense, decoder-only transformer featuring grouped-query attention, 200,000 vocabulary, and shared input-output embeddings, designed for speed and efficiency. Despite its compact size, it continues outperforming larger models in text-based tasks, including reasoning, math, coding, instruction-following, and function-calling. Supporting sequences up to 128,000 tokens, it delivers high accuracy and scalability, making it a powerful solution for advanced AI applications. Function calling, instruction following, long context, and reasoning are powerful capabilities that enable small language models like Phi-4-mini to access external knowledge and functionality despite their limited capacity. Through a standardized protocol, function calling allows the model to seamlessly integrate with structured programming interfaces. When a user makes a request, Phi-4-Mini can reason through the query, identify and call relevant functions with appropriate parameters, receive the function outputs, and incorporate those results into its responses. This creates an extensible agentic-based system where the model’s capabilities can be enhanced by connecting it to external tools, application program interfaces (APIs), and data sources through well-defined function interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-mini 是一個擁有 3.8B 參數的模型，為密集型、僅限解碼器的轉換器，具備群組查詢注意、200,000 個詞彙和共用的輸入輸出嵌入，專為速度和效率而設計。儘管其體積小巧，但針對基於文字的工作，其表現仍持續超越更大型的模型，包括推理、數學、編碼、遵從指令和函數呼叫。Phi-4-mini 支援最多 128,000 個權杖的序列，提供高正確性和可擴縮性，使其成為進階 AI 應用的強大解決方案。由於有函數呼叫、遵從指令、長背景内容和推理等強大功能，因此即便像 Phi-4-mini 的小型語言模型，也能夠在容量有限的情況下，存取外部的知識和功能。透過標準化的通訊協議，函數呼叫使模型能夠與結構化的程式介面無縫整合。當使用者提出請求時，Phi-4-mini 能夠透過查詢進行推理，並使用適當的參數識別和呼叫相關的函數，接收函數輸出，並將這些結果整合到其回應中。這會建立可擴展的代理型系統，透過定義明確的功能介面，將模型連接至外部工具、應用程式介面 (API) 和資料來源，以強化其功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are designed to handle complex tasks efficiently, making them ideal for edge case scenarios and compute-constrained environments. Given the new capabilities Phi-4-multimodal and Phi-4-mini bring, the uses of Phi are only expanding. Phi models are being embedded into AI ecosystems and used to explore various use cases across industries.]A;]A;<strong>Embedded directly to your smart device:</strong> Integrating Phi-4-multimodal directly into a smartphone could enable smartphones to process and understand voice commands, recognize images, and interpret text seamlessly. Users could benefit from advanced features like real-time language translation, enhanced photo and video analysis, and intelligent personal assistants that understand and respond to complex queries. This would elevate the user experience by providing powerful AI capabilities directly on the device, ensuring low latency and high efficiency.]A;]A;<strong>On the road:</strong> Imagine an automotive company integrating Phi-4-multimodal into their in-car assistant systems. The model could enable vehicles to understand and respond to voice commands, recognize driver gestures, and analyze visual inputs from cameras. For instance, it could enhance driver safety by detecting drowsiness through facial recognition and providing real-time alerts. Additionally, it could offer seamless navigation assistance, interpret road signs, and provide contextual information, creating a more intuitive and safer driving experience while connected to the cloud and offline when connectivity isn't available.]A;]A;<strong>Multilingual financial services:</strong> Imagine a financial services company integrating Phi-4-mini to automate complex financial calculations, generate detailed reports, and translate financial documents into multiple languages. For instance, the model can assist analysts by performing intricate mathematical computations required for risk assessments, portfolio management, and financial forecasting. Additionally, it can translate financial statements, regulatory documents, and client communications into various languages and could improve client relations globally.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[這些模型的設計旨在有效率地處理複雜的工作，因此非常適合用於邊緣案例情境和計算受限的環境。隨著 Phi-4-multimodal 和 Phi-4-mini 帶來的新功能，Phi 的應用範圍不斷擴大。Phi 模型正被嵌入到 AI 生態系統中，並用於探索各行各業的各種使用案例。]A;]A;<strong>直接嵌入到您的智慧型裝置中:</strong> 將 Phi-4-multimodal 直接整合到智慧型手機中，可以使手機處理和理解語音命令、辨識影像，並順暢解譯文字。使用者可從即時語言翻譯、增強的相片和影片分析，以及能理解和回應複雜查詢的智慧型個人助理等進階功能中受益。透過直接在裝置上提供強大的 AI 功能，這將能提升使用者體驗，確保低延遲和高效率。]A;]A;<strong>在路上:</strong> 想像一家汽車公司將 Phi-4-multimodal 整合到其車内助理系統中。該模型可以使車輛理解並回應語音命令、辨識駕駛者手勢，並分析來自相機的視覺輸入。例如，它可以通過臉部辨識檢測駕駛者的疲勞，並提供即時警示，以增強駕駛安全。此外，它還可以提供流暢的導航協助、解譯路標，並提供上下文資訊，創造更直觀和安全的駕駛體驗 (無論是連接到雲端或是離線)。]A;]A;<strong>多語言財經服務:</strong> 想像一家財經服務公司整合了 Phi-4-mini，以自動化複雜的財務計算、生成詳細報告，並將財務文件翻譯成多種語言。例如，該模型可以協助分析師執行風險評估、投資組合管理和財務預測所需的複雜數學計算。此外，它還可以將財務報表、法規文件和客戶通訊翻譯成多種語言，並改善全球客戶關係。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[These models are designed to handle complex tasks efficiently, making them ideal for edge case scenarios and compute-constrained environments. Given the new capabilities Phi-4-multimodal and Phi-4-mini bring, the uses of Phi are only expanding. Phi models are being embedded into AI ecosystems and used to explore various use cases across industries.]A;]A;Embedded directly to your smart device: Integrating Phi-4-multimodal directly into a smartphone could enable smartphones to process and understand voice commands, recognize images, and interpret text seamlessly. Users could benefit from advanced features like real-time language translation, enhanced photo and video analysis, and intelligent personal assistants that understand and respond to complex queries. This would elevate the user experience by providing powerful AI capabilities directly on the device, ensuring low latency and high efficiency.]A;]A;On the road: Imagine an automotive company integrating Phi-4-multimodal into their in-car assistant systems. The model could enable vehicles to understand and respond to voice commands, recognize driver gestures, and analyze visual inputs from cameras. For instance, it could enhance driver safety by detecting drowsiness through facial recognition and providing real-time alerts. Additionally, it could offer seamless navigation assistance, interpret road signs, and provide contextual information, creating a more intuitive and safer driving experience while connected to the cloud and offline when connectivity isn't available.]A;]A;Multilingual financial services: Imagine a financial services company integrating Phi-4-mini to automate complex financial calculations, generate detailed reports, and translate financial documents into multiple languages. For instance, the model can assist analysts by performing intricate mathematical computations required for risk assessments, portfolio management, and financial forecasting. Additionally, it can translate financial statements, regulatory documents, and client communications into various languages and could improve client relations globally.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4TryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bar chart comparing agent performance by ML task complexity between AIDE and Project Amelie (powered by R&D-Agent). The chart shows performance metrics (%) across different complexity levels (Low/Lite, Medium, High) with Project Amelie achieving 22.4% overall performance compared to AIDE's 16.9%. Project Amelie shows improved performance across all complexity categories, with the most significant improvement in the Low/Lite category represented by a light blue section at the bottom of each bar.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比較 AIDE 與 Project Amelie (由 R&D-Agent 提供) 之間，ML 工作複雜度的代理程式效能的橫條圖。圖表顯示不同複雜度層級 (低/輕量、中、高) 的效能計量 (%)，相較於 AIDE 的 16.9%，Project Amelie 的整體效能達到 22.4%。Project Amelie 在所有複雜度類別均顯示了效能改善，而低/輕量類別最顯著的改善，則由每個橫條底部的淺藍色區段表示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A demo of using the Project Amelie agent to predict accommodation rental prices in Seattle. The Agent builds a regression model, answers questions about it, and provides code to run the model. The user then opens the code in VS Code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Project Amelie 代理程式來預測西雅圖住宿租賃價格的示範。代理程式會建置迴歸模型、回答其相關問題，並提供執行模型的程式碼。使用者接著會在 VS Code 中開啟程式碼。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[With Project Amelie, we are unveiling our first Foundry autonomous agent that can perform machine learning engineering tasks. ML teams can use the agent to initiate complex machine learning tasks using prompts —such as, “Help me create a model to predict customer churn"—and receive fully validated ML pipelines, detailed evaluation metrics, trained model, and ready-to-use, reproducible Python code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透過 Project Amelie，我們將開展我們的第一個可執行機器學習工程工作的 Foundry 自主代理程式。ML 小組可以使用代理程式，使用提示來初始化複雜的機器學習工作 (例如「協助我建立模型來預測客戶流失」)，並接收經完整驗證的 ML 管線、詳細的評估計量、訓練的模型，以及可供使用的可重現 Python 程式碼。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Project Amelie]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Project Amelie]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieSignUpLink1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign up for Private Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[註冊個人預覽版]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieSignUpLink2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[sign up here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在這裡註冊]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{SignUpLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{SignUpLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<em>Private Preview Coming Soon! Sign up to get early access and opportunity to share feedback. Approved users can explore and experiment with Project Amelie.</em>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<em>私人預覽即將推出!註冊以提早存取並有機會分享意見反應。經核准的使用者可以探索和實驗 Project Amelie。</em>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[At its core, Project Amelie is powered by innovation from Microsoft Research designed specifically to automate and optimize research and development processes in machine learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 的核心是由 Microsoft Research 創新所提供，專為自動化和最佳化機器學習中的研究與開發程序所設計。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie outperforms the current state of the art benchmarks on MLE-Bench by OpenAI, which measures MLE agent’s effectiveness on real world ML engineering tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 強於 MLE-Bench by OpenAI 上目前最先進的效能評定，後者可測量 MLE 代理程式在真實世界 ML 工程工作上的有效性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are currently in early beta and plan to launch Private Preview soon! If you are interested in getting early access to Project Amelie and sharing your insights to help shape the product, please {SignUpLink}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們目前處於早期 Beta，並計劃儘快推出私人預覽!如果您有興趣提早存取 Project Amelie 並分享您的深入解析來協助塑造產品，請 {SignUpLink}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A healthcare professional in scrubs sits with an older adult in a care facility, holding a tablet displaying a meal. In the foreground, a smartphone screen shows the ReMe app interface with options for game training, user feedback, and starting a conversation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一位身著醫護服的醫護人員在安養機構中與一位長者對話，手持平板電腦，螢幕上顯示著一份餐點。在前景中，一部智慧型手機螢幕顯示了 ReMe 應用程式介面，包含「遊戲訓練」、「使用者回饋」和「開始對話」等選項。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe is a web-based framework that helps researchers create AI chatbots for personalized training and interventions aimed at strengthening memory and cognitive functions. Early evaluations show its potential to contribute to digital health innovation and advance non-pharmacological approaches to cognitive health.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 是一種網頁架構，可協助研究人員建立 AI 聊天機器人來進行個人化訓練和介入，以期強化記憶和認知功能。早期評估顯示它有潛力促進數位健康創新並推動非藥物認知健康方法的發展。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore ReMe]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 ReMe]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe is a web-based framework designed to accelerate research into personalized cognitive training using AI chatbots. As cognitive decline becomes a growing public health concern, ReMe supports researchers, clinicians, and caregivers in developing interactive training tasks focused on episodic memory and open-ended cognitive challenges.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 是 Web 架構，旨在加速使用 AI 聊天機器人進行個人化認知訓練的研究。隨著認知下降成為日益嚴重的公共衛生疑慮，ReMe 支援研究人員、臨床醫生和照護人員發展著重於情節記憶和開放式認知挑戰的互動式訓練工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The framework integrates a puzzle engine, a life-logging module for personal memory recall, and a multimodal training interface featuring text, image, and voice capabilities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此架構整合了謎題引擎、用於個人記憶回憶的生命週期記錄模組，以及具備文字、影像和語音功能的多模式訓練介面。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cognitive training is one of the few non-pharmacological methods shown to help delay decline, but existing programs are often generic and not very engaging. ReMe aims to make cognitive training more personalized and adaptable while reaching more people at lower cost.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[認知訓練是少數被證實有助於延緩衰退的非藥物方法之一，但現有的程式往往比較籠統，吸引力不大。ReMe 旨在讓認知訓練更加個人化且更具調適性，同時以更低的成本觸及更多人。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instead of building every new cognitive training chatbot from scratch, researchers can use ReMe to prototype, test, and improve interventions more quickly, speeding up discovery of what works.  ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[研究人員可使用 ReMe 更快速地建立原型、測試及改善介入，而不需從頭開始建置每個新的認知訓練聊天機器人，進而加速探索可行的方法。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In initial evaluations, ReMe was well received by users citing strong conversational fluency and moderate difficulty. While not intended for clinical treatment, ReMe provides a valuable tool for exploring AI’s role in supporting cognitive health, paving the way for future innovations in personalized digital therapies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在初步評估中，ReMe 深受使用者好評，他們聲稱交談極為流暢且難度適中。雖然 ReMe 並非用於臨床治療，但提供了一個寶貴的工具，以便探索 AI 在支援認知健康方面的角色，進而為個人化數位療法的未來創新鋪路。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open ReMe repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 ReMe 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 供研究用途發行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chemical structures and colored markers on a scatter plot.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[散布圖上的化學結構和彩色標記。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen is a transformer-based chemical language model for developing target-specific drug compounds. Research shows that TamGen can also optimize existing molecules by designing target-aware molecule fragments, potentially enabling the discovery of novel compounds that build on a known molecular core structure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen 是一種轉換程式型化學語言模型，可用於開發目標特定藥物複合。研究顯示 TamGen 也可以透過設計目標感知分子片段來優化現有的分子，可能會啟用以已知分子核心結構建置的新穎複合探索。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover TamGen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 TamGen]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generative AI is opening new avenues for scientific exploration by allowing computers to autonomously learn and produce original content. TamGen offers a new approach to drug discovery by applying the principles of generative AI to molecular design. Unlike traditional methods, which depend on systematically screening known compounds—a process that is long, complex, and costly due to its reliance on empirical knowledge and the time-consuming task of exploring a vast chemical library—generative AI provides opportunities for designing entirely new chemical structures.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Generative AI 藉由允許電腦自主地學習及產生原始內容，為科學探索開啟新的探索。TamGen 將產生 AI 的原則套用到分子設計，藉此提供新的藥物探索方法。與傳統方法不同，這種方法相依於有條不息地檢測已知的複合體—這個程式既長又複雜，而且成本高昂，因為它在經驗知識和探索大型化學連結庫的耗時工作上很費時，而產生 AI 提供設計全新化學結構的機會。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen showcases the transformative potential of generative AI in drug design, combining advanced molecular modeling with researcher-AI collaboration. Tasks that once took years could now be accomplished in a fraction of the time. This research underscores AI’s expanding role in drug discovery and its promise for developing effective treatments against persistent infectious diseases like tuberculosis.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen 展示藥物設計中產生式 AI 的轉換潛能，結合進階分子模型與研究人員-AI 共同作業。曾經花費年數的任務現在可以以時間的一小部分完成。此研究會強調 AI 在藥物探索中的擴充角色，以及其對永續性病患如包輿病培養有效患者的承諾。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get TamGen model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取得 TamGen 模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Try TamGen]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis creates  high-quality 3D assets from simple text or image inputs. Using a unified latent space (SLAT), it delivers detailed, textured 3D models in formats like meshes,radiance fields, and 3D Gaussians. Its flexibility, editing capabilities, and superior quality enable faster, more adaptable workflows in gaming, virtual worlds, industrial design, and beyond.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 可從簡單的文字或影像輸入建立高品質的 3D 資產。它利用統一的延遲空間 (SLAT) 輸出具有細節與紋理的 3D 模型，格式包括網格、輻射場與 3D 高斯表示。它的彈性、編輯能力與卓越品質，使其在遊戲、虛擬世界、工業設計等領域實現更快速且具適應性的工作流程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Trellis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Trellis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis provides a powerful foundation for scalable, AI-driven content creation. Built to meet rapidly growing industrial demand, Trellis creates high-quality, editable 3D assets from simple text or image prompts in lieu of manual modeling. This saves time, lowers barriers, and unlocks new possibilities for developers, designers, and digital content creators.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 為可擴展的 AI 驅動內容建立提供了強大的基礎。為滿足快速增長的產業需求，Trellis 可透過簡單的文字或影像提示產生高品質且可編輯的 3D 資產，而無需手動建模。這不僅可節省時間，降低門檻，還為開發人員、設計師與數位內容創作者開啟了全新可能性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis is built on a novel Structured LATent (SLat) representation that captures both geometric structure and visual detail in a compact, editable form. Trained on 500,000 diverse 3D objects using rectified flow transformers with up to 2 billion parameters, Trellis significantly improves both quality and flexibility compared to existing models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 建構於創新的結構化 LaTent (SLat) 表示法上，能以精簡且可編輯的形式，同時捕捉幾何結構與視覺細節。Trellis 以 500,000 個多樣化 3D 物件訓練，並採用高達 20 億參數的校正式流變換器，與現有模型相比，大幅提升了品質與靈活性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike traditional methods that target a single output type or require labor-intensive setup, Trellis can generate a 3D asset in multiple formats, including meshes, 3D gaussians, and radiance fields. This makes it compatible with different rendering pipelines and applications. The generated models feature detailed structure and rich texture, enabling their direct use in games, AR/VR experiences, digital twins, simulation environments, and product visualization.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不同於僅針對單一輸出格式或需要大量人工設定的傳統方法，Trellis 能生成多種格式的 3D 資產，包括網格、3D 高斯表示及輻射場。這使得它能相容於各種呈現管線與應用程式。所生成的模型具備精細結構與豐富紋理，可直接應用於遊戲、AR/VR 體驗、數位分身、模擬環境與產品視覺效果等領域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis also allows for prompt-guided local edits—such as removing, replacing, or adding parts of a 3D model—without retraining or manual sculpting, which dramatically accelerates iteration and customization. Its design eliminates the need for costly 3D fitting and leverages pretrained vision models for high-fidelity results, even when working with sparse 3D data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 也允許由提示引導的局部編輯——例如移除、更換或新增 3D 模型的某部分——而無需重新訓練或手動雕刻，大幅加快迭代與自訂流程。它的設計省去了高成本的 3D 擬合流程，並利用預訓練視覺模型，即使在處理稀疏的 3D 數據時也能產出高保真度的成果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[細網紋]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Trellis repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 Trellis 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TryOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{modelName} has been released for research purposes. Users can learn, explore and experiment with {modelName}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已發行 {modelName} 供研究之用。用戶可以學習、探索及實驗 {modelName}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A demonstration of using the TypeAgent shell navigating and interacting with a visually rich Paleobiology Database (paleodb) website, highlighting its ability to process and act on complex web interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[示範使用 TypeAgent 殼層瀏覽並與視覺效果豐富的古生物學資料庫 (paleodb) 網站互動，強調其處理複雜 Web 介面並在其上採取行動的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An demonstration of the TypeAgent Shell where a user converses with the agent about events and entities extracted months earlier. The gif shows entities being retrieved from long-term memory into the conversation, enabling the user to take actions on them.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 殼層的示範，其中的使用者與代理程式討論數月前所擷取的事件和實體。gif 顯示將實體從長期記憶擷取到交談中，讓使用者能夠對它們採取動作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent is sample code that explores an architecture for building a single personal agent with natural language interfaces leveraging current advances in LLM technology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 是一項範例程式碼，可探索運用 LLM 技術目前的進展，以自然語言介面建置單一個人代理程式的結構。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover TypeAgent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 TypeAgent]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The goal of the TypeAgent team is to explore how to get work done by safely and efficiently combining stochastic systems like language models with traditional software components. Three principles have emerged during this investigation. They are listed below along with examples of how the principles apply to actions, memory and plans.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 小組的目標是要探索如何安全且有效率地結合推測系統 (例如語言模型) 與傳統軟體元件，以完成工作。此調查期間呈現了三個原則。以下列出這些原則，以及原則如何套用至動作、記憶和計劃的範例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<ul><li><strong>Principle:</strong> distilling models into logical structures</li><ul><li>Actions: find translation patterns and replace some model calls by applying patterns</li><li>Memory: build ontologies from text</li><li>Plans: people, programs and models collaborate using “tree of thought”</li></ul><br><li><strong>Principle:</strong> control information density</li><ul><li>Actions: applications define discrete categories with dense descriptions of action sets</li><li>Memory: tight semantic structures fit into attention budget</li><li>Plans: each search tree node defines a focused sub-problem</li></ul><br><li><strong>Principle:</strong> use logical structures to enable collaboration</li><ul><li>Actions: humans decide how to disambiguate action requests</li><li>Memory: simple models extract logical structure from text</li><li>Plans: quality models, advantage models, language models, humans and programs collaborate to expand each best-first-search node</li></ul>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<ul><li><strong>原則:</strong> 將模型放入邏輯結構</li><ul><li>動作: 尋找翻譯模式並透過套用模式來取代一些模型呼叫</li><li>記憶: 從文字建置本體</li><li>計劃: 使用「思維樹」進行人員、程式和模型共同作業</li></ul><br><li><strong>原則:</strong> 控制資訊密度</li><ul><li>動作: 應用程式使用動作集的密集描述來定義相異類別</li><li>記憶: 緊密的語意結構符合注意預算</li><li>計劃: 每個搜尋樹狀目錄節點都定義聚焦的子問題</li></ul><br><li><strong>原則:</strong> 使用邏輯結構來啟用共同作業</li><ul><li>動作: 人員決定如何消除動作要求的歧義</li><li>記憶: 從文字擷取邏輯結構的簡單模型</li><li>計劃: 品質模型、優點模型、語言模型、人類和程式共同作業，以擴大每個最佳搜尋節點</li></ul>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are trying to build a single personal agent that can apply to any application.  To apply agent interfaces to all applications, we need to map user requests to actions at much lower cost and latency than current systems. To make this possible, we have created a system that can distill language models into logical systems that can handle most user requests.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們正在嘗試建置可套用至任何應用程式的單一個人代理程式。 若要將代理程式介面套用至所有應用程式，我們需要以較目前系統低很多的成本和延遲，將使用者要求對應至動作。為了讓這點可行，我們建立了一個系統，可以將語言模型放入可處理大部分使用者要求的邏輯系統中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Figure 1: The TypeAgent shell example navigating a visually rich {paleodbLink} website.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[圖 1: 瀏覽視覺效果豐富的 {paleodbLink} 網站的 TypeAgent 殼層範例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We use LLMS with structured prompts to extract a logical representation of actions on a page (e.g. buy product). This logical schema is the same across multiple sites, even if the sites have different HTML and JS implementations. We demonstrate the power of this approach by building automation to interact with multiple crossword sites and multiple e-commerce sites using consistent logical schema.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們使用 LLMS 搭配結構化提示，以擷取頁面上動作 (例如購買產品) 的邏輯表示法。此邏輯架構在多個網站間是相同的，即使網站有不同的 HTML 和 JS 實作亦然。我們透過建置自動化，以使用一致的邏輯架構與多個填字遊戲網站和多個電子商務網站互動，來示範此方法的強大能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are trying to create human-like memory with super-human precision and recall for agent conversations. We are using a new indexing and query processing approach called <strong>Structured RAG</strong> as the basis for agent memory. Structured RAG does substantially better than Classic RAG at answering questions about past conversations such as "what were the books we talked about?" and "what step were we on in building the photo montage?"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我們正嘗試建立具有超級人類精確度與代理程式交談回顧的類人類記憶。我們使用名為<strong>結構化 RAG</strong> 的新索引編製和查詢處理方法，做為代理程式記憶的基礎。結構化 RAG 在回答有關過去交談的問題 (例如「我們討論了哪些書籍?」和「我們在建立相片拼集 (蒙太奇) 時到了什麼步驟?」) 方面，比傳統 RAG 更好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Figure 2: Here using the experimental TypeAgent Shell a user can have conversations with the agent about events and entities that were extracted months ago. Entities are pulled from long-term memory into the conversation memory and user can then take actions on the entities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[圖 2: 在這裡，使用實驗性 TypeAgent 命令殼層，使用者可以與代理程式進行有關數月前所擷取事件和實體的交談。實體會從長期記憶提取到交談記憶，然後使用者可以對實體採取動作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText8" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Actions and memories flow together. Actions like "add to my calendar pickleball game 2-3pm on Friday" yield memories that can become parameters of future actions like "put in an hour of recovery time after my pickleball game." We are working on an architecture, <strong>AMP</strong>, that enables this natural information flow by integrating actions, memories, and plans. We are applying AMP to the web by creating a browser that enables web sites to register actions through a JavaScript interface.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[動作和記憶會合流。像是「將星期五下午 2:00 到下午 3:00 匹克球遊戲新增到我的行事曆」這樣的動作，會產生記憶，其可能會成為未來動作的參數，例如「在我的匹克球遊戲後放入一小時的休息時間」。我們正在努力開發架構 <strong>AMP</strong>，其會整合動作、記憶和計劃，以實現這種自然資訊流程。我們正在透過建立可讓網站透過 JavaScript 介面登錄動作的瀏覽器，將 AMP 套用至 Web。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open TypeAgent repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 TypeAgent 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A diagram showing the process of generating a 3D talking head animation from a single image using VASA-3D. The steps include creating VASA-1 videos from the image, building a VASA-3D model, and combining it with an audio clip and optional control signals to produce various animated outputs of the subject.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一個圖表顯示了如何使用 VASA-3D 從單一影像產生 3D 頭像動畫的流程。這些步驟包括: 從影像產生 VASA-1 影片，以這些影片構建 VASA-3D 模型，並將模型與音訊剪輯及選用的控制訊號結合，以產生主題的各種動畫輸出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D exemplifies how generative AI can enhance human-computer interaction by making expressive, customizable 3D avatars accessible from minimal input. Extending VASA-1's motion latent into 3D and optimizing with synthetic multiview data, it opens new frontiers in communication, immersion, and assistive technology—setting a new standard for realism and responsiveness in avatar generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 展示了生成式 AI 如何讓您經由最少的輸入來存取表達力強的可自訂 3D 虛擬人偶，進而增強人機互動。將 VASA-1 的動作潛在特徵延伸至 3D 並使用合成多視角資料進行最佳化，在通訊、沉浸式及輔助技術方面開啟了新的領域，進而為虛擬人偶生成的真實技術和回應能力樹立新標準。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore VASA-3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 VASA-3D]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D is a major step forward in 3D avatar generation, producing realistic, expressive, and multiview-consistent 3D talking heads from just a single image and speech audio. The system builds on VASA-1, which introduced high-fidelity 2D talking head synthesis through a richly expressive motion latent. VASA-3D extends this capability into 3D by conditioning a neural 3D head model on the motion latent, capturing nuanced facial expressions and natural head motion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 是 3D 虛擬人偶生成的一大進步，從單一影像和語音音訊產生逼真、表達力強和多視角一致的 3D 說話頭像。此系統以 VASA-1 為基礎，透過富有表達力的動作潛在特徵引進了高度逼真 2D 說話頭像合成。VASA-3D 藉由調節動作潛在特徵的神經 3D 頭像模型，將此功能延伸至 3D，以捕捉細微的面部表情和自然頭像動作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To personalize the 3D head from a single portrait, VASA-3D creates additional views of the face from different angles and uses them to fine-tune the 3D model, even if some of those views have visual flaws or limited variety. The result is a model capable of real-time generation at 75 frames per second with just 65 milliseconds latency, supporting free-view rendering, emotional control, and high-quality animation, even from stylized or artistic portraits.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為了從單一肖像個人化 3D 頭像，VASA-3D 會從不同角度建立臉部的額外視圖，並使用它們來微調 3D 模型，即使其中某些視圖有視覺瑕疵或變化有限。結果是一個模型，能夠即時產生每秒 75 個畫面格且延遲只有 65 毫秒，並支援自由視角呈現、情緒控制，以及高品質的動畫，即使是從非寫實或藝術肖像產生。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D significantly outperforms prior methods on realism and expressiveness in both audio- and video-driven talking head tasks. Its broad potential spans virtual collaboration (AI coworkers), education (AI tutors), entertainment, and neuroscience research. Early applications include VR-based social interaction, memory activation studies, and adaptive learning tools. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 在音訊和視訊驅動說話頭像工作的真實技術和表達能力方面，明顯超越先前的方法。其廣泛的潛力涵蓋虛擬共同作業 (AI 同事)、教育 (AI 導師)、娛樂和神經科學研究。早期應用程式包括 VR 型社交互動、記憶體啟用研究，以及調適型學習工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Responsible AI is integral to VASA-3D’s development. To prevent misuse, the model and APIs are not publicly released. Face forgery detection systems trained on VASA-3D outputs can reliably distinguish synthetic content, enhancing safety and model robustness.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[負責的 AI 是 VASA-3D 開發不可或缺的一部分。為了防止濫用，不會公開發行該模型和 API。在 VASA-3D 輸出上訓練的臉部偽造偵測系統能可靠地區分合成內容，並增強安全和模型穩健性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D exemplifies how advanced generative AI can enhance human-computer interaction. By making expressive, customizable 3D avatars accessible from minimal input, it opens new frontiers in communication, immersion, and assistive technology—setting a new standard for realism and responsiveness in avatar generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 展示了進階生成式 AI 如何增強人機互動。讓您經由最少的輸入來存取表達力強的可自訂 3D 虛擬人偶，在通訊、沉浸式和輔助技術方面開啟了新的領域，進而為虛擬人偶生成的真實技術和回應能力樹立新標準。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Vasa-3D repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[開啟 Vasa-3D 存放庫]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA3D has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA3D 供研究用途發行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a framework for generating realistic, long-form, multi-speaker dialogue from transcripts, making it ideal for podcasts, voiceovers, and narrative audio. Unlike typical TTS tools, VibePod handles up to four speakers across 30-minute sessions with strong speaker consistency, natural pacing, and turn-taking.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一個可從文字記錄產生真實感十足、長篇多角色對話的架構，非常適合用於播客、配音及敘事型音訊內容。不同於一般的文字轉換語音工具，VibePod 可在長達 30 分鐘的對話中處理多達四位演講者，並維持高度一致性、自然的節奏與流暢的輪流發言。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore VibePod]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 VibePod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a new framework designed to generate realistic, multi-speaker audio content from transcripts. Ideal for producing podcasts, voiceovers, and other narrative formats, it outperforms conventional text-to-speech (TTS) tools, which are unable to produce long-form, coherent, and interactive multi-speaker dialogue. VibePod supports up to four distinct voices in 30-minute segments. With its improved pacing, turn-taking, and speaker consistency, VibePod rated highly in user testing for spontaneity and realism.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是新的框架，旨在從文字記錄產生真實的多演講者音訊內容。它非常適合製作播客、配音及其他敘事形式，其表現超越傳統的文字轉換語音 (TTS) 工具，後者無法生成長篇、連貫且具互動性的多角色對話。VibePod 在 30 分鐘的段落中最多支援四個不同的語音。憑藉其節奏控制、輪流發言和說話者一致性的提升，VibePod 在使用者測試中因其自然流暢與真實感獲得高度評價。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike systems focused on voice cloning, VibePod emphasizes dialogue quality over personalization. It was trained on publicly available and synthetically generated datasets, with safeguards built in to prevent misuse. It does not allow custom voice uploads, reflecting {ResponsibleAIPrinciples}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[與專注於語音複製的系統不同，VibePod 更加重視對話的品質而非個人化。它是以公開可取得和合成生成的資料集進行訓練，並內建防濫用機制以避免不當使用。它不允許上傳自訂語音，以體現 {ResponsibleAIPrinciples}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText2Link" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft’s Responsible AI principles]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的負責任 AI 原則]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To promote research, transparency, and responsible use, VibePod is being released on Hugging Face under the MIT License. Open-sourcing the technology invites collaboration from the broader speech synthesis community. Future enhancements will include multilingual support and controls for emotional tone, expanding its creative potential.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[為了促進研究、透明度及負責任的使用，VibePod 將在 MIT 授權下於 Hugging Face 上發行。開源技術邀請來自更廣泛的語音合成社群進行合作。未來的增強功能將包括多語言支援及情感語調控制項，以擴展其創意潛力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\1\s\common\models-details\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-CN" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";AOAIFinetuneInvalidRegion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI model fine-tune is not available in your workspace's region. Supported regions include: {{region}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 微调在你的工作区所在的区域中不可用。支持的区域包括: {{region}}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service model fine-tune is not available in your workspace's region. Supported regions include: {{region}}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAIInvalidRegion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is not available in your workspace's region.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 在工作区的区域中不可用。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is not available in your workspace's region.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAINotSignedUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This subscription is not enabled for Azure OpenAI yet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[还没有为 Azure OpenAI 启用此订阅]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This subscription is not enabled for Azure OpenAI Service yet]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAIRequest" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request access to Azure OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请求访问 Azure OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Request access to Azure OpenAI Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAISignUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use of Azure OpenAI models in Azure Machine Learning requires Azure OpenAI resources. This subscription or region does not have access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure 机器学习中使用 Azure OpenAI 模型需要 Azure OpenAI 资源。此订阅或区域没有访问权限。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use of Azure OpenAI Service models in Azure Machine Learning requires Azure OpenAI Service resources. This subscription or region does not have access.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AOAISignUpWithSub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use of Azure OpenAI models in Azure Machine Learning requires Azure OpenAI resources. This subscription ({subscription}) or region ({location}) does not have access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure 机器学习中使用 Azure OpenAI 模型需要 Azure OpenAI 资源。此订阅({subscription})或区域({location})没有访问权限。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use of Azure OpenAI Service models in Azure Machine Learning requires Azure OpenAI Service resources. This subscription ({subscription}) or region ({location}) does not have access.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.BenchmarkBreadcrumbText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型基准]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare benchmarks across models and datasets available in the industry to assess which one meets your business scenario. {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比较行业中可用的模型和数据集的基准，以评估哪一个符合你的业务方案。{1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageDescriptionEmbeddings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View benchmarks for embeddings models across various tasks for comparison. {1}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看各种任务的嵌入模型基准以进行比较。{1}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageDescriptionLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about how model performance is scored]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解如何对模型性能进行评分]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageDescriptionLinkEmbeddings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about how we benchmark embeddings models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解如何对嵌入模型进行基准测试]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AiHome.Explore.LeaderboardPageHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assess model performance with evaluated metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用已评估的指标来评估模型性能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AzureOpenAiDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 部署]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service deployment]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AzureOpenAiPlayground" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI playground]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 操场]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service playground]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AzureOpenAiService" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.BarChart" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bar chart]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[条形图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.BreadcrumbText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse leaderboards]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.CompareBetweenMetrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare between metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在指标之间进行比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Cost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.CostDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Estimated cost for the sum of cost per input tokens and cost per output tokens, with a ratio of 3:1.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每个输入令牌的成本和每个输出令牌的成本之和的估计成本，比率为 3:1。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[USD per 1M Tokens; Lower is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.CostSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[USD per 1M Tokens; Lower is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[美元/100 万个令牌；越低越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Estimated cost for the sum of cost per input tokens and cost per output tokens, with a ratio of 3:1.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.ExportButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Export as a report view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[导出为报告视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.HelpPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevant resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相关资源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.HelpPanel.linkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about benchmarks leaderboard details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解基准排行榜详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.SaveButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save current view as another leaderboard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将当前视图另存为其他排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.SubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tier-1 is a small representative set of critical benchmarks. These benchmarks include important capabilities, have large sample sizes and correct examples, and are challenging.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[第 1 层是一组具有代表性的小型关键基准。这些基准包含重要的功能，样本量大且示例正确，同时具有挑战性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Details.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multilingual]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多语言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HarmbenchContextualSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate used in safety benchmarking; Lower is safer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全基准测试中使用的攻击成功率；越低越安全]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Attack success rates in contextually harmful behaviors; Lower is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HarmbenchCopyRightViolationsSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate used in safety benchmarking; Lower is safer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全基准测试中使用的攻击成功率；越低越安全]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Attack success rates in copyright violations; Lower is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HarmbenchStandardSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate used in safety benchmarking; Lower is safer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全基准测试中使用的攻击成功率；越低越安全]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Attack success rates in standard harmful behaviors; Lower is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HelpPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevant resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相关资源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Benchmarking Leaderboard]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HelpPanel.linkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about benchmarks leaderboard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解基准排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HighLights" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Highlights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[热点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.HighLightsDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Which model performs best in following criteria.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按照以下标准性能最佳的模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Which model perform best in following criteria.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.KnowledgeInSensitiveDomainsSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy; Higher means more knowledge of sensitive domains in cybersecurity, biosecurity and chemical security]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准确度；越高意味着对网络安全、生物安全和化学安全方面的敏感领域的了解越多]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Higher accuracy denotes more knowledge of dangerous capabilities.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.LeaderboardByBenchmarks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Leaderboards by scenario]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按场景显示的排行榜]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Leaderboard by scenarios]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.LeaderboardDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Take a deep dive into detailed metrics of model performance in these categories.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深入了解这些类别中模型性能的详细指标。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deep dive of details metrics of model performance]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.LeaderboardTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Leaderboards by benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[排行榜(按基准检验)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详情]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.MetricDescriptionPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scores are presented at the dataset and the model levels. At the dataset level, the scored. At the dataset level, the scored.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分数在数据集和模型级别显示。在数据集级别已评分。在数据集级别已评分。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.Deploy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.EvaluateOwnData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate on your data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对数据进行评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.Model" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.ModelDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.ModelNotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model details not available in your Azure cloud region.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型详细信息在 Azure 云区域中不可用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.ModelNotAvailableInFDP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The model is not available in this resource.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[该模型在此资源中不可用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.ModelNotAvailableInFoundry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model is not available in Azure AI Foundry.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型在 Azure AI Foundry 中不可用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.QualityIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.Ranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[排名]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.TooltipContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An average of accuracy scores from comprehensive benchmark datasets measuring model capabilities, such as reasoning, knowledge, and coding.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测量模型功能(如推理、知识和编码)的综合基准数据集的准确度分数平均值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelInfoCard.TryInPlayground" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try in playground]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在操场中试用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ModelsSelected" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所选模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.MostAttractiveQuadrant" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Most attractive quadrant]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最佳象限]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Most Attractive Quadrant]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.No" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[序号]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Quality" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An average of accuracy scores from comprehensive benchmark datasets measuring model capabilities, such as reasoning, knowledge, and coding.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测量模型功能(如推理、知识和编码)的综合基准数据集的准确度分数平均值。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Quality index; higher is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityLeaderboard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality leaderboard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualitySubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index; Higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量索引；越高越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[An average of accuracy scores from comprehensive benchmark datasets measuring model capabilities, such as reasoning, knowledge, and coding.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityVsCost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality vs Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量与成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityVsSafety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality vs Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量与安全性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.QualityVsThroughput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality vs Throughput]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量与吞吐量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.RadarChart" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Radar plot]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[雷达图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Safety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SafetyDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average attack success rate from benchmark datasets (HarmBench) evaluating behaviors in cybercrime & unauthorized intrusion, chemical & biological weapons/drugs, copyright violations, misinformation & disinformation, harassment & bullying, illegal activities, and general harm. Benchmarked with Azure AI Content Safety turned off.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基准数据集(HarmBench)中评估网络犯罪和未经授权的入侵、化学和生物武器/药品、版权侵权、错误信息和虚假信息、骚扰和霸凌、非法活动及一般伤害方面的行为的平均攻击成功率。已在关闭 Azure AI 内容安全的情况下进行基准检验。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Average attack success rate from benchmark datasets (HarmBench) evaluating behaviors in cybercrime & unauthorized intrusion, chemical & biological weapons/drugs, copyright violations, misinformation & disinformation, harassment & bullying, illegal activities, and general harm.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SafetySubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate; Lower is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[攻击成功率; 越低越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Quality index; Higher is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SafetyXAxisTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[攻击成功率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SeeDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.SubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare model performance in Quality, Safety, Cost, and Throughput, backed by industry standard public benchmarks. {link} about our scoring methodology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在行业标准公共基准的支持下，比较模型在质量、安全、成本和吞吐量方面的性能。关于评分方法的 {link}。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Compare Quality, Cost, Throughput and other criteria. {link} about our scoring methodology.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Throughput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[吞吐量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ThroughputDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of output tokens that are getting generated per second from the time the request is sent to the endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这是从请求发送到终结点开始每秒生成的输出令牌数。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Output Tokens per Second; Higher is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ThroughputSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output Tokens per Second; Higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒输出令牌数；越高越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The number of output tokens that are getting generated per second from the time the request is sent to the endpoint.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ThroughputVsCost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput vs Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[吞吐量与成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Find the best model by comparing model performance across various criteria]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过比较不同条件的模型性能来查找最佳模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Model leaderboards]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.CostUnit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[USD per 1M tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每 100 万个令牌的价格(美元)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.ModelDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to model details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[转到模型详细信息]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Model details]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.QualityIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.SafetyIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attack success rate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[攻击成功率]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Safety index]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TooltipDialog.ThroughputUnit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output tokens per second]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒输出令牌数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.ToxicityDetectionSubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1-based accuracy in the ability to detect toxic content; Higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于 F1 的有毒内容检测能力的准确度；越高越好]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Accuracy in the ability of detecting toxic content; Higher is better]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TradeOffs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trade-off charts]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[权衡图表]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Trade-offs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.TradeOffsSubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You might need a model that excels in one category but is a lower performer in another. View the trade-offs for various categories.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你可能需要一个在一个类别中表现优异但在另一个类别中表现较差的模型。查看各种类别的权衡。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[How you choose on that best fit your need]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.BitextMining" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bitext mining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[双语文本挖掘]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Coding" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coding]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编码]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.DocumentClustering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Document clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文档聚类分析]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.GeneralKnowledge" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[General knowledge]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[常识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根基性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.HarmbenchContextual" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Contextually harmful behavior]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上下文有害的行为]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Contextually harmful behavior (HarmBench)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.HarmbenchCopyRightViolations" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copyright violations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版权冲突]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Copyright violations (HarmBench)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.HarmbenchStandard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard harmful behavior]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标准有害行为]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Standard harmful behavior (HarmBench)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.InformationRetrieval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Information retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[信息检索]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.KnowledgeInSensitiveDomains" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Knowledge in sensitive domains]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[敏感领域的知识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Math" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Math]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数学]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.QA" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[问题解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Reasoning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Safety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.Summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[汇总]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.TextClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksLeaderboard.datasetLeaderboard.ToxicityDetection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Toxicity detection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[毒性检测]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Toxicity Detection]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BarChartCard.FullScreen" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Full screen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[全屏]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.LatencyMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Time to first token]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成第一个标记所需的时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.Metric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.ModelVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.PublicDataBenchmark" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public data benchmark results]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公共数据基准结果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.QualityIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.Seconds" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Seconds]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[秒]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.ThroughputMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated tokens per second]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒生成的令牌数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.Tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标记]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkCard.USDPer1MTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[USD per 1M tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每 100 万个令牌的价格(美元)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.BenchmarkMetric.NA" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[N/A]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[N/A]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.CostCaption" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(USD per 1M tokens; lower is better)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(美元/100 万个令牌；越低越好)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[(USD per 1M tokens; Lower is better)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.DefaultCaption" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(Higher is better)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(越高越好)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.EmbeddingsLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Embeddings Index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嵌入索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.LatencyCaption" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(Lower is better)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(越低越好)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.QualityLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality Index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.X" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[X-axis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[X 轴]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Axes.Y" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Y-axis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Y 轴]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准确度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Cost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Embeddings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Embeddings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嵌入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.EmbeddingsIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Embeddings index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嵌入索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.F1Score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1 score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流利度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.GPTSimilarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPT similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT 相似度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根基性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Latency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.MAP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mean average precision]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均精度均值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.NDCGAt10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NDCG at 10]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NDCG@10]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Quality" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量更新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.QualityIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相关性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.Safety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.SpearmanCorrelation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[斯皮尔曼等级相关系数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.MenuItems.VMeasure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 度量值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Tooltip.Ranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ranking No.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[排名编号]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Tooltip.Score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.CompareChart.Versus" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[vs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.AddModelToCompareButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model to compare]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要比较的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Back" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Back]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[后退]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ChartContainerHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metrics to compare]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要比较的指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ClearAllModelsTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clear all models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[清除所有模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ClearAllTasksTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clear all tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[清除所有任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.CompareView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比较视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.DialogTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Done" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Done]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Ellipsis" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[…]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.FilterPanelHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models to compare]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要比较的模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.FilterPanelModelsCount" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.FilterPanelModelsHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Selected model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所选模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.FilterPanelTasksHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Popular tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[常用任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.GroupByDatasets" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Group by datasets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按数据集分组]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.GroupByModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Group by models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按模型分组]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ListView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[List view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[列表视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.MetricsDetails.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.MetricsDetails.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.ModelVersionSelector" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version selector]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本选择器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.PageDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare benchmarks across models and datasets available in the industry to assess which one meets your business scenario. {1} about how model performance is scored.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跨行业中可用模型和数据集比较基准，以评估哪一个符合你的业务方案。{1}模型性能的评分方式。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Compare benchmarks across models and datasets available in the industry to assess which one meets your business scenario. ({Learn more}) about how model performance is scored.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.PageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assess model performance with evaluated metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用已评估的指标来评估模型性能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.RemoveModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Remove model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.SearchBoxAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜索任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.SearchBoxPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜索]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.SeeLess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See less]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更少]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.SeeMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更多]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models evaluated on performance metrics can't be compared with models evaluated on embeddings metrics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法将根据性能指标计算的模型与根据嵌入指标评估的模型进行比较。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.DescriptionNoCostLatencyData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost and latency data are not yet supported for certain models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[某些模型尚不支持成本和延迟数据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.Remove" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Remove]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[移除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are not comparable]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这些模型不可比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparePage.Warning.TitleAxes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These axes are not comparable]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这些轴不可比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonButton.ReadMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[阅读更多]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonCard.CompareMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare with more models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与更多模型进行比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonCard.ComparingWith" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Comparing with]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonCard.ComparisonHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonChartUtils.NoData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No data available for the selected metric and dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所选指标和数据集没有可用数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.AIQualityHeadline" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compares models based on accuracy, coherence, groundedness, relevance, fluency, and GPT similarity metrics. The default view is an average of these scores, but you can also view scores for each metric individually.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于准确性、并行性、地基度、相关性、流畅度和 GPT 相似度指标比较模型。默认视图是这些分数的平均值，但你也可以单独查看每个指标的分数。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.AIQualityTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI quality comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 质量比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.BackToResults" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Back to benchmark results]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[返回到基准结果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.CalculatedDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset used to calculate score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用于计算分数的数据集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.CalculatedMetrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Calculated metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测算指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.Close" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.CostHeadline" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compares models based on the dollar amount required to run 1M tokens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根据运行 1M 令牌所需的美元金额比较模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.CostTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成本比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.LatencyHeadline" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compares models based on the time taken to generate the first token or the response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根据生成第一个令牌或响应所用的时间比较模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.LatencyTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.Metric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.PublicData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公开数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.ThroughputHeadline" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compares models based on the number of tokens or requests processed per second.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根据每秒处理的令牌数或请求数比较模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonDialog.ThroughputTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[吞吐量比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.AIQuality" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 质量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.AIQualitySubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality index; higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量索引；越高越好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.Cost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Estimated cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[估计成本]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Cost]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.CostSubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Estimated cost index; lower is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[估计成本索引；越低越好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.CostTableHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.Latency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.LatencySubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Time to first token; lower is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[第一个令牌的时间；越低越好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.Throughput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[吞吐量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonMetrics.ThroughputSubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated tokens per second; higher is better]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒生成的令牌数；越高越好]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonView.CompareMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare with more models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与更多模型进行比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonView.ComparingWith" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Comparing with]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比较对象]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ComparisonView.ComparisonHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Comparison]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.GPTSimilarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures the similarity in semantic meaning and context between a sentence in the source data (ground truth) and a sentence generated by an AI model as its response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[衡量源数据(基本事实)中的句子与 AI 模型生成的响应句子之间的语义和上下文的相似性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.NoDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述不可用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[At the model level, the accuracy score is the average of the dataset-level accuracies for each model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在模型级别，准确度分数是每个模型的数据集级别准确度的平均值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures how well the language model can produce output that flows smoothly, reads naturally, and resembles human-like language. Important to assess the readability and user-friendliness of your model's generated responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测量语言模型能产生流利性、自然阅读以及类似于人类语言的输出的效果。请务必评估模型生成的响应的可读性和用户友好性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.f1score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The F1 score is the weighted mean of the precision and recall, where an F1 score reaches its best value at 1 (perfect precision and recall) and worst at 0.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分数是精准率和召回率的加权平均值，其中 F1 分数的最佳值为 1 (完美精准率和召回率)，最差值为 0。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures the grammatical proficiency of a generative AI's predicted answer. Used to evaluate whether AI-generated text adheres to grammatical rules, syntactic structures, and proper vocabulary usage.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[衡量生成式 AI 预测答案的语法熟练程度。用于评估 AI 生成的文本是否遵循语法规则、语法结构和正确的词汇用法。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures how well the model's generated answers align with information from the source data. Essential for applications where factual correctness and contextual accuracy are key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[衡量模型生成的答案与源数据信息的匹配程度。对于事实正确性和上下文准确性至关重要的应用非常重要。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.input_token_cost_per_1M_tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dollar value for pay-as-you-go for 1 million input tokens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[100 万个输入令牌的美元价值(即用即付)。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Input token cost per 1M tokens]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.inter_token_latency_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the time between tokens received (inter token latency).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这是令牌接收的间隔时间(令牌间延迟)。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Intertoken latency]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_mean_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average time in seconds taken for processing a request, computed over multiple requests. To do this, we send a request to the endpoint every hour, for 2 weeks, and compute the average.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[处理请求所用的平均时间(以秒为单位)，通过多个请求计算得出。为此，我们每小时向终结点发送一个请求，持续 2 周，并计算平均值。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency mean in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_p50_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[50th percentile value (the median) of latency (the time taken between the request and when we receive the entire response with a successful code). For example: When we send a request to the endpoint, 50% of the requests are completed in x seconds, with x being the latency measurement.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟的第 50 个百分位值(中值)(请求与我们收到具有成功代码的整个响应之间的时间)。例如: 将请求发送到终结点时，50% 的请求在 x 秒内完成，其中 x 是延迟度量。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P50 in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_p90_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[90th percentile value (the median) of latency (the time taken between the request and when we receive the entire response with a successful code). For example: When we send a request to the endpoint, 90% of the requests are completed in x seconds, with x being the latency measurement.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟的第 90 个百分位值(中值)(请求与我们收到具有成功代码的整个响应之间的时间)。例如: 将请求发送到终结点时，90% 的请求在 x 秒内完成，其中 x 是延迟度量。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P90 in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_p95_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[95th percentile value (the median) of latency (the time taken between the request and when we receive the entire response with a successful code). For example: When we send a request to the endpoint, 95% of the requests are completed in x seconds, with x being the latency measurement.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟的第 95 个百分位值(中值)(请求与我们收到具有成功代码的整个响应之间的时间)。例如: 将请求发送到终结点时，95% 的请求在 x 秒内完成，其中 x 是延迟度量。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P95 in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_p99_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[99th percentile value (the median) of latency (the time taken between the request and when we receive the entire response with a successful code). For example: When we send a request to the endpoint, 99% of the requests are completed in x seconds, with x being the latency measurement.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟的第 99 个百分位值(中值)(请求与我们收到具有成功代码的整个响应之间的时间)。例如: 将请求发送到终结点时，99% 的请求在 x 秒内完成，其中 x 是延迟度量。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P99 in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.latency_ttft_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TTFT stands for total time to first token. This is the time taken for the first token in the response to be returned from the endpoint when streaming is enabled.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TTFT 表示收到第一个令牌所用的总时间。这是启用流式传输时要从终结点返回的响应中的第一个令牌所用的时间。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency TTFT in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.map" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mean Average Precision (MAP) evaluates the quality of ranking and recommender systems. It measures both the relevance of suggested items and how good the system is at placing more relevant items at the top. Values can range from 0 to 1. The higher the MAP, the better the system can place relevant items high in the list.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[均值平均精准率(MAP)将评估排序和推荐系统的质量。它将同时衡量建议项的相关性，以及系统在将更多相关项放在顶部方面的表现。值的范围可以是 0 到 1。MAP 越高，系统就越能将相关项放在列表的顶部。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.ndcgat10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Normalized Discounted Cumulative Gain (NDCG) evaluates the quality of information retrieval systems. NDCG helps measure a machine learning algorithm's ability to sort items based on relevance. It compares rankings to an ideal order where all relevant items are at the top of the list. K is a user-assigned parameter that defines the cutoff point (list length) while evaluating ranking quality. For example, if k=10, ndcg_at_10, will look at top-10 items.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[归一化折损累计增益(NDCG)将评估信息检索系统的质量。NDCG 可帮助衡量机器学习算法根据相关性对各项进行排序的能力。它会将排序与所有相关项均位于列表顶部的理想顺序进行比较。K 是用户分配的参数，用于定义评估排序质量时的截止点(列表长度)。例如，如果 k=10 (ndcg_at_10)，将查看前 10 项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.output_token_cost_per_1M_tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dollar value for pay-as-you-go for 1 million output tokens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[100 万个输出令牌的美元价值(即用即付)。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Output token cost per 1M tokens]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Measures the extent to which the model's generated responses are pertinent and directly related to the given questions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测量模型生成的响应与给定问题相关且直接相关的程度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.spearmancorrelation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation based on cosine similarity is the main metric for STS (Semantic Textual Similarity) and Summarization Embedding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于余弦相似度的斯皮尔曼相关性是 STS (语义文本相似度)和摘要嵌入任务的主要指标。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.throughput_gtps_token_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GTPS stands for generated tokens per second. This is the number of output tokens that are getting generated per second from the time the request is sent to the endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GTPS 表示每秒生成的令牌数。这是从请求发送到终结点开始每秒生成的输出令牌数。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput GTPS token count]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.throughput_rps_request_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[RPS stands for requests per second. This is the total number of requests processed per second.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[RPS 表示每秒请求数。这是每秒处理的请求总数。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput RPS request count]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.throughput_ttps_token_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TTPS stands for total tokens per second. This is the number of total tokens processed per second including both from the input prompt and generated output tokens.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TTPS 表示每秒令牌总数。这是每秒处理的令牌总数，包括来自输入提示的令牌和生成的输出令牌。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput TTPS token count]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.time_between_tokens_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the time between tokens received (inter token latency).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这是令牌接收的间隔时间(令牌间延迟)。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Time between tokens in seconds]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricDescriptions.vmeasure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure is a metric used to evaluate the quality of clustering. It is calculated as the harmonic mean of homogeneity (each cluster contains only members of a single class) and completeness (all members of a given class are assigned to the same cluster), ensuring a balance between the two for a meaningful score. Possible score lies between 0 and 1. Score of 1 stands for perfectly complete labeling.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 度量值是用于评估聚类分析质量的指标。它计算为同质性(每个聚类仅包含单个类的成员)和完整性(给定类的所有成员都分配给同一个聚类)的调和均值，从而确保两者之间的平衡以获得有意义的分数。可能的分数介于 0 和 1 之间。分数 1 代表完全完整的标记。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.GPTSimilarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPT similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT 相似度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.NoMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric name not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指标名称不可用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准确度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.f1score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1 score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流利度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根基性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.index" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average of all]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[全部平均值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.input_token_cost_per_1M_tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input token cost per 1M tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每 100 万个令牌的输入令牌成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.inter_token_latency_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Intertoken latency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[令牌间延迟]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_mean_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency mean (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均延迟 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency mean]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_p50_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency P50 (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟 P50 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P50]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_p90_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency P90 (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟 P90 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P90]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_p95_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency P95 (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟 P95 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P95]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_p99_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency P99 (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟 P99 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency P99]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.latency_ttft_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latency TTFT (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[延迟 TTFT (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Latency TTFT]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.map" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mean average precision]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均精度均值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.ndcgat10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NDCG at 10]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NDCG@10]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.output_token_cost_per_1M_tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output token cost per 1M tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每 100 万个令牌的输出令牌成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相关性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.spearmancorrelation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[斯皮尔曼等级相关系数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.throughput_gtps_token_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput GTPS (tokens per sec)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒的 GTPS (令牌吞吐量)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput GTPS]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.throughput_rps_request_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput RPS (requests per sec)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒的 RPS (请求吞吐量)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput RPS]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.throughput_ttps_token_count" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput TTPS (tokens per sec)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每秒吞吐量 TTPS (令牌)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Throughput TTPS]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.time_between_tokens_secs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Time between tokens (in secs)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[令牌之间的时间 (秒)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Time between tokens]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.MetricNames.vmeasure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 度量值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ModelCatalogBenchmarksPivot.NoBenchmarksData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No benchmarking data exists for given model. Please ensure the model version is correct.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[给定模型不存在基准数据。请确保模型版本正确。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ModelsTable.Index" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.ModelsTable.Metrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.NextSteps.ButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try with your own data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[尝试使用你自己的数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.NextSteps.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate this model with your own data to see how it performs with your scenario.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用你自己的数据评估此模型，查看它在应用场景中的表现。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Get benchmark results on your own data to see how this model performs with your scenario.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.NextSteps.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate results with your data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用你的数据生成结果]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[What's next?]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.BenchmarksReportsView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Benchmarks reports view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基准报表视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.Datasets" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Datasets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.FileName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件名]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.GoToReportsView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to reports view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[转到报表视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.Models" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Reports.ShareView" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Share view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共享视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.BitextMining" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bitext mining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[双语文本挖掘]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Clustering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聚类分析]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.PairClassification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pair classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配对分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.QuestionAnswering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[问题解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Reranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新排序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Retrieval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[检索]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.STS" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[STS]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[STS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.Summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[汇总]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BenchmarksPage.Tasks.TextGeneration" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.AccessRequest" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Request access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请求访问权限]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.CardHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Versions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.DeploymentType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.FeatureFlaggedModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Access will be granted according to Microsoft's eligibility criteria; you may not be eligible for access at this time. If you have already requested access, thank you for your patience while your submission is reviewed. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们将根据 Microsoft 的资格条件授予访问权限；你目前可能没有资格获得访问权限。如果你已请求访问权限，那么感谢你耐心等待审查你的提交内容。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.FeatureFlaggedTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Access to the model is required to be able to see the model versions:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要访问模型才能查看模型版本:]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Registration is required for this model: ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.FindingAvailability" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Finding availability...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在查找可用性...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.GenerallyAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generally available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正式版]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.LearnMoreAvailability" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about region availability]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解区域可用性]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Learn more about regional availability]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.Lifecycle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Lifecycle]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生命周期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.LowerPlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[model versions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.MaxRequest" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max request]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大请求数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.ModelId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.NoVersions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No model versions available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有可用的模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.NotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不可用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.NotForProd" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not suitable for production use, please proceed with caution]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不适合生产用途，请谨慎操作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.Preview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[预览]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CatalogModelList.RetirementDate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retirement Date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停用日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ClearChatError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clear the output to start a new dialog.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[清除输出以启动新对话。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Common.Archive" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Archive]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存档]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Common.Restore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restore]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[还原]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Common.Restoring" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restoring]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在还原]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConnectionSelector.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 资源]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service resource]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.ClickHere" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click {here} to learn more about Azure AI Content Safety and how to use it.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击 {here} 了解有关 Azure AI 内容安全及其使用方式的详细信息。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.ComingSoon" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This option will deploy in a notebook. Wizard support coming soon!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此选项将在笔记本中部署。即将推出向导支持!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Enable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy with Azure AI Content Safety (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure AI 内容安全(预览版)进行部署]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Enable Azure AI Content Safety (preview)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.EnableShort" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Re-enable Azure AI Content Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新启用 Azure AI 内容安全性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Infotext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Content safety detects harmful user-generated and AI-generated content. Enabling content safety on your deployment can help your application comply with regulations or maintain the intended environment for your users.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[内容安全检测到有害用的户生成内容和 AI 生成内容。在部署上启用内容安全可以帮助应用程序遵守法规或维护用户的预期环境。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.LearnMoreAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Azure AI Content Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解 Azure AI 内容安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Options.Disabled.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model will be deployed without Azure AI Content Safety. You may be at higher risk of exposing users to harmful content in generated text.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无需 Azure AI 内容安全即可部署此模型。在生成的文本中向用户公开有害内容的风险可能更高。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[This model will be deployed without the Azure AI content moderation safe filters. By proceeding in this manner, there is a risk of exposing users to offensive or inappropriate content in your generated text, which could have a negative impact on their experience.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Options.Disabled.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Skip Azure AI Content Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跳过 Azure AI 内容安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Options.Enabled.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use Azure AI Content Safety to enable a safer content experience for both inputs and outputs. Azure AI Content Safety can detect and filter harmful content in four categories (hate, sexual, violence, and self-harm) at specified severity levels.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure AI 内容安全性为输入和输出提供更安全的内容体验。Azure AI 内容安全可以在指定的严重性级别检测和筛选四个类别(恶意、性、暴力和自残)的有害内容。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use Azure AI Content Safety to actively ensure safe content moderation for both your input and output. Azure AI Content Safety will efficiently and swiftly filter out offensive or inappropriate content in your text, aiming to enhance online experiences for your users.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Options.Enabled.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable Azure AI Content Safety (Recommended)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启用 Azure AI 内容安全(推荐)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Proceed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Proceed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[继续]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.PromptToLaunch" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To create a deployment with content safety enabled, click {here}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要创建启用了内容安全的部署，请单击 {here}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.PromptToLaunchAria" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a deployment with content safety enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建启用了内容安全的部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ContentSafety.Warning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The model will be deployed without Azure AI content moderation safe filters. By proceeding in this manner, there is a risk of exposing users to offensive or inappropriate content in your generated text, which could have a negative impact on their experience.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将在不使用 Azure AI 内容审查安全筛选器的情况下部署模型。通过这种方式继续，可能会使用户在生成的文本中公开冒犯内容或不适当内容，这可能会对他们的体验产生负面影响。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The model will be deployed without Azure AI content moderation safe filters. By proceeding in this manner, there is a risk of exposing users to offensive or inappropriate content in my generated text, which could have a negative impact on their experience.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateEvalWithModelAndPromptDialog.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateEvalWithModelAndPromptDialog.Create" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateEvalWithModelAndPromptDialog.CreateANewEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a new evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建新的评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.AGIEval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{zhong2023agieval,]A;title={AGIEval: A Human-Centric Benchmark for Evaluating Foundation Models}, ]A;author={Wanjun Zhong and Ruixiang Cui and Yiduo Guo and Yaobo Liang and Shuai Lu and Yanlin Wang and Amin Saied and Weizhu Chen and Nan Duan},]A;year={2023},]A;eprint={2304.06364},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{zhong2023agieval,]A;标题={AGIEval: 以人为中心的评估基础模型的基准}, ]A;作者={Wanjun Zhong 和 Ruixiang Cui 和 Yiduo Guo 和 Yaobo Liang 和 Shuai Lu 和 Yanlin Wang 和 Amin Saied 和 Weizhu Chen 和 Nan Duan},]A;年份={2023},]A;eprint={2304.06364},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.AGIEval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AGIEval is a human-centric benchmark specifically designed to evaluate the general abilities of foundation models in tasks pertinent to human cognition and problem-solving. This benchmark is derived from 20 official, public, and high-standard admission and qualification exams intended for general human test-takers, such as general college admission tests (e.g., Chinese College Entrance Exam (Gaokao) and American SAT), law school admission tests, math competitions, lawyer qualification tests, and national civil service exams.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AGIEval 是以人为中心的基准，旨在评估基础模型在与人类认知和解决问题相关的任务中的一般能力。此基准源自 20 项针对普通考生的官方、公开、高标准入学和资格考试，例如普通高校入学考试(例如，中国高考和美国 SAT)、法学院入学考试、数学竞赛、律师资格考试、国家公务员考试。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.AGIEval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AGIEval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AGIEval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.AGIEval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/microsoft/AGIEval",]A;"arxiv": "https://arxiv.org/pdf/2304.06364.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/microsoft/AGIEval",]A;"arxiv": "https://arxiv.org/pdf/2304.06364.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArenaHard.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{li2024crowdsourceddatahighqualitybenchmarks,]A;title={From Crowdsourced Data to High-Quality Benchmarks: Arena-Hard and BenchBuilder Pipeline},]A;author={Tianle Li and Wei-Lin Chiang and Evan Frick and Lisa Dunlap and Tianhao Wu and Banghua Zhu and Joseph E. Gonzalez and Ion Stoica},]A;year={2024},]A;eprint={2406.11939},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG},]A;url={https://arxiv.org/abs/2406.11939}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{li2024crowdsourceddatahighqualitybenchmarks，]A;title={From Crowdsourced Data to High-Quality Benchmarks： Arena-Hard and BenchBuilder Pipeline}，]A;author={Cubele Li and Wei-Lin Evan and Evan Frick and Dunlap and Evan dunlap and Banghua Wu and Banghua Zhu and Zhu E. Bang and Ion Stoica}，]A;year={2024}，]A;eprint={2406.11939}，]A;archivePrefix={arXiv}，]A;primaryClass={cs.LG}，]A;url={https://arxiv.org/abs/2406.11939}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArenaHard.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Arena-Hard-Auto-v0.1 is an automatic evaluation tool for instruction-tuned LLMs. It contains 500 challenging user queries sourced from Chatbot Arena. We prompt GPT-4-Turbo as judge to compare the models' responses against a baseline model (default: GPT-4-0314). Notably, Arena-Hard-Auto has the highest correlation and separability to Chatbot Arena among popular open-ended LLM benchmarks. If you are curious to see how well your model might perform on Chatbot Arena, we recommend trying Arena-Hard-Auto.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Arena-Hard-Auto-v0.1 是用于指令优化 LLM 的自动评估工具。它包含 500 个具有挑战性的用户查询，源自 Chatbot Arena。我们提示GPT-4-Turbo 作为判断，将模型的响应与默认 (基线模型进行比较： GPT-4-0314)。在热门开放式 LLM 基准中，Arena-Hard-Auto 与 Chatbot Arena 具有最高的相关性和分隔性。如果你想知道你的模型在 Chatbot 竞技场上的性能如何，建议试用 Arena-Hard-Auto。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArenaHard.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Arena Hard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[竞技场硬]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArenaHard.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/lmarena/arena-hard-auto",]A;"arxiv": "https://arxiv.org/abs/2406.11939",]A;"webpage": "https://lmsys.org/blog/2024-04-19-arena-hard/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[“github”： "https://github.com/lmarena/arena-hard-auto"，]A;“arxiv”： "https://arxiv.org/abs/2406.11939"，]A;“网页”： "https://lmsys.org/blog/2024-04-19-arena-hard/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArguAna.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{wachsmuth:2018a,]A;author={Wachsmuth, Henning and Syed, Shahbaz and Stein, Benno},]A;title={Retrieval of the Best Counterargument without Prior Topic Knowledge},]A;booktitle ={Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers){,]A;year={2018},]A;publisher={Association for Computational Linguistics},]A;location={Melbourne, Australia},]A;pages={241--251},]A;url={http://aclweb.org/anthology/P18-1023}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{wachsmuth:2018a,]A;author={Wachsmuth, Henning and Syed, Shahbaz and Stein, Benno},]A;title={Retrieval of the Best Counterargument without Prior Topic Knowledge},]A;booktitle ={Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers){,]A;year={2018},]A;publisher={Association for Computational Linguistics},]A;location={Melbourne, Australia},]A;pages={241--251},]A;url={http://aclweb.org/anthology/P18-1023}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArguAna.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A Full-Text Learning to Rank Dataset for Medical Information Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用于医疗信息检索的纯文本排序学习数据集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArguAna.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ArguAna]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ArguAna]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ArguAna.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/arguana",]A;"webpage": "http://argumentation.bplaced.net/arguana/data"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/arguana",]A;"webpage": "http://argumentation.bplaced.net/arguana/data"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BUCC.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{zweigenbaum-etal-2017-overview,]A;title = {Overview of the Second {BUCC} Shared Task: Spotting Parallel Sentences in Comparable Corpora},]A;author = {Zweigenbaum, Pierre  and]A;Sharoff, Serge  and]A;Rapp, Reinhard},]A;editor = {Sharoff, Serge  and]A;Zweigenbaum, Pierre  and]A;Rapp, Reinhard},]A;booktitle = {Proceedings of the 10th Workshop on Building and Using Comparable Corpora},]A;month = aug,]A;year = {2017},]A;address = {Vancouver, Canada},]A;publisher = {Association for Computational Linguistics},]A;url = {https://aclanthology.org/W17-2512},]A;doi = {10.18653/v1/W17-2512},]A;pages = {60--67},]A;abstract = {This paper presents the BUCC 2017 shared task on parallel sentence extraction from comparable corpora. It recalls the design of the datasets, presents their final construction and statistics and the methods used to evaluate system results. 13 runs were submitted to the shared task by 4 teams, covering three of the four proposed language pairs: French-English (7 runs), German-English (3 runs), and Chinese-English (3 runs). The best F-scores as measured against the gold standard were 0.84 (German-English), 0.80 (French-English), and 0.43 (Chinese-English). Because of the design of the dataset, in which not all gold parallel sentence pairs are known, these are only minimum values. We examined manually a small sample of the false negative sentence pairs for the most precise French-English runs and estimated the number of parallel sentence pairs not yet in the provided gold standard. Adding them to the gold standard leads to revised estimates for the French-English F-scores of at most +1.5pt. This suggests that the BUCC 2017 datasets provide a reasonable approximate evaluation of the parallel sentence spotting task.},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{zweigenbaum-etal-2017-overview,]A;title = {Overview of the Second {BUCC} Shared Task: Spotting Parallel Sentences in Comparable Corpora},]A;author = {Zweigenbaum, Pierre  and]A;Sharoff, Serge  and]A;Rapp, Reinhard},]A;editor = {Sharoff, Serge  and]A;Zweigenbaum, Pierre  and]A;Rapp, Reinhard},]A;booktitle = {Proceedings of the 10th Workshop on Building and Using Comparable Corpora},]A;month = aug,]A;year = {2017},]A;address = {Vancouver, Canada},]A;publisher = {Association for Computational Linguistics},]A;url = {https://aclanthology.org/W17-2512},]A;doi = {10.18653/v1/W17-2512},]A;pages = {60--67},]A;abstract = {This paper presents the BUCC 2017 shared task on parallel sentence extraction from comparable corpora.It recalls the design of the datasets, presents their final construction and statistics and the methods used to evaluate system results.13 runs were submitted to the shared task by 4 teams, covering three of the four proposed language pairs: French-English (7 runs), German-English (3 runs), and Chinese-English (3 runs).The best F-scores as measured against the gold standard were 0.84 (German-English), 0.80 (French-English), and 0.43 (Chinese-English).Because of the design of the dataset, in which not all gold parallel sentence pairs are known, these are only minimum values.We examined manually a small sample of the false negative sentence pairs for the most precise French-English runs and estimated the number of parallel sentence pairs not yet in the provided gold standard.Adding them to the gold standard leads to revised estimates for the French-English F-scores of at most +1.5pt.This suggests that the BUCC 2017 datasets provide a reasonable approximate evaluation of the parallel sentence spotting task.},}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BUCC.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The BUCC mining task is a shared task on parallel sentence extraction from two monolingual corpora with a subset of them assumed to be parallel, and that has been available since 2016. For each language pair, the shared task provides a monolingual corpus for each language and a gold mapping list containing true translation pairs. These pairs are the ground truth. The task is to construct a list of translation pairs from the monolingual corpora. The constructed list is compared to the ground truth, and evaluated in terms of the F1 measure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BUCC 挖掘任务是从两个单语语料库中并行提取句子的共享任务，其中一部分假定为并行，自 2016 年以来一直可用。对于每个语言对，共享任务将为每种语言提供一个单语语料库和一个包含正确翻译对的黄金映射列表。这些语言对是基本事实。任务是基于单语语料库构造一个翻译对列表。构造的列表将与基本事实进行比较，并根据 F1 度量值进行评估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BUCC.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BUCC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BUCC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BUCC.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/bucc-bitext-mining",]A;"webpage": "https://aclanthology.org/W17-2512.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/bucc-bitext-mining",]A;"webpage": "https://aclanthology.org/W17-2512.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Banking77Classification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{casanueva2020efficient,]A;title={Efficient Intent Detection with Dual Sentence Encoders}, ]A;author={Iñigo Casanueva and Tadas Temčinas and Daniela Gerz and Matthew Henderson and Ivan Vulić},]A;year={2020},]A;eprint={2003.04807},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{casanueva2020efficient,]A;title={Efficient Intent Detection with Dual Sentence Encoders},]A;author={Iñigo Casanueva and Tadas Temčinas and Daniela Gerz and Matthew Henderson and Ivan Vulić},]A;year={2020},]A;eprint={2003.04807},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Banking77Classification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset composed of online banking queries annotated with their corresponding intents.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由带有相应意图注释的联机银行查询组成的数据集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Banking77Classification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Banking77Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Banking77Classification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Banking77Classification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/banking77",]A;"arxiv": "https://arxiv.org/abs/2003.04807"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/banking77",]A;"arxiv": "https://arxiv.org/abs/2003.04807"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigBenchHard.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{srivastava2023beyond,]A;title={Beyond the Imitation Game: Quantifying and extrapolating the capabilities of language models},]A;author={BIG-bench authors},]A;journal={Transactions on Machine Learning Research},]A;issn={2835-8856},]A;year={2023},]A;url={https://openreview.net/forum?id=uyTL5Bvosj},]A;note={}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{srivastava2023beyond,]A;标题={超越模仿游戏: 量化和外推语言模型的能力},]A;作者={BIG-bench 作者},]A;期刊={Transactions on Machine Learning Research},]A;issn={2835-8856},]A;年份={2023},]A;url={https://openreview.net/forum?id=uyTL5Bvosj},]A;note={}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigBenchHard.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Beyond the Imitation Game Benchmark (BIG-bench) is a collaborative benchmark of more than 200 tasks intended to probe large language models and extrapolate their future capabilities. Task topics are diverse, drawing problems from linguistics, childhood development, math, common-sense reasoning, biology, physics, social bias, software development, and beyond. BIG-Bench focuses on tasks that are believed to be beyond the capabilities of current language models. **BIG-Bench Hard (BBH)** is a suite of 23 challenging BIG-Bench tasks for which prior language model evaluations did not outperform the average human-rater.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[超越模仿游戏基准 (BIG-bench) 是 200 多个任务的协作基准，旨在探测大型语言模型并外推其未来功能。任务主题多种多样，涉及语言、儿童发展、数学、常识推理、生物、物理、社会偏见、软件开发等领域的问题。BIG-Bench 专注于被认为超出当前语言模型功能的任务。**BIG-Bench Hard (BBH)** 是由 23 项具有挑战性的 BIG-Bench 任务组成的套件，之前的语言模型评估未优于人类评估者的平均水平。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigBenchHard.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BigBench-Hard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BigBench-Hard]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigBenchHard.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google/BIG-bench/tree/main",]A;"arxiv": "https://arxiv.org/abs/2210.09261",]A;"webpage": "https://github.com/suzgunmirac/BIG-Bench-Hard"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google/BIG-bench/tree/main",]A;"arxiv": "https://arxiv.org/abs/2210.09261",]A;“网页”: "https://github.com/suzgunmirac/BIG-Bench-Hard"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigCodeBenchInstruct.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={Program Synthesis with Large Language Models},]A;author={Jacob Austin, Augustus Odena, Maxwell Nye, Maarten Bosma, Henryk Michalewski, David Dohan, Ellen Jiang, Carrie Cai, Michael Terry, Quoc Le, Charles Sutton},]A;year={2021},]A;eprint={2108.07732},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={使用大型语言模型进行程序合成},]A;作者={Jacob Austin、Augustus Odena、Maxwell Nye、Maarten Bosma、Henryk Michalewski、David Dohan、Ellen Jiang、Carrie Cai、Michael Terry、Quoc Le、Charles Sutton}，]A;year={2021},]A;eprint={2108.07732}，]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigCodeBenchInstruct.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Beyond the Imitation Game Benchmark (BIG-bench) is a collaborative benchmark of more than 200 tasks intended to probe large language models and extrapolate their future capabilities. Task topics are diverse, drawing problems from linguistics, childhood development, math, common-sense reasoning, biology, physics, social bias, software development, and beyond. BIG-Bench focuses on tasks that are believed to be beyond the capabilities of current language models. **BIG-Bench Hard (BBH)** is a suite of 23 challenging BIG-Bench tasks for which prior language model evaluations did not outperform the average human-rater.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[超越模仿游戏基准 (BIG-bench) 是 200 多个任务的协作基准，旨在探测大型语言模型并外推其未来功能。任务主题多种多样，涉及语言、儿童发展、数学、常识推理、生物、物理、社会偏见、软件开发等领域的问题。BIG-Bench 专注于被认为超出当前语言模型功能的任务。**BIG-Bench Hard (BBH)** 是由 23 项具有挑战性的 BIG-Bench 任务组成的套件，之前的语言模型评估未优于人类评估者的平均水平。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigCodeBenchInstruct.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BigCodeBench (instruct)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BigCodeBench (指示)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BigCodeBenchInstruct.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"hugging_face": "https://huggingface.co/datasets/mbpp",]A;"arxiv": "https://arxiv.org/pdf/2108.07732.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"hugging_face": "https://huggingface.co/datasets/mbpp",]A;"arxiv": "https://arxiv.org/pdf/2108.07732.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BoolQ.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{clark2019boolq,]A;title =     {BoolQ: Exploring the Surprising Difficulty of Natural Yes/No Questions},]A;author =    {Clark, Christopher and Lee, Kenton and Chang, Ming-Wei, and Kwiatkowski, Tom and Collins, Michael, and Toutanova, Kristina},]A;booktitle = {NAACL},]A;year = {2019},]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{clark2019boolq,]A;标题 =     {BoolQ: 探索自然是/否问题的惊人难度},]A;作者 =    {Clark, Christopher 和 Lee, Kenton 和 Chang, Ming-Wei, 和 Kwiatkowski, Tom 和 Collins, Michael, 和 Toutanova, Kristina},]A;booktitle = {NAACL},]A;年份 = {2019},]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BoolQ.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BoolQ is a question answering dataset for yes/no questions containing 15942 examples. These questions are naturally occurring - they are generated in unprompted and unconstrained settings. Each example is a triplet of (question, passage, answer), with the title of the page as optional additional context. The text-pair classification setup is similar to existing natural language inference tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BoolQ 是针对是/否问题的问答数据集，包含 15942 个示例。这些问题自然发生 - 它们在无提示和不受约束的设置中产生。每个示例都是三元组(问题、段落、答案)，其中页面标题作为可选的附加上下文。文本对分类设置类似于现有的自然语言推理任务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BoolQ.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BoolQ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BoolQ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.BoolQ.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google-research-datasets/boolean-questions",]A;"hugging_face": "https://huggingface.co/datasets/boolq",]A;"arxiv": "https://arxiv.org/pdf/1905.10044.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google-research-datasets/boolean-questions",]A;"hugging_face": "https://huggingface.co/datasets/boolq",]A;"arxiv": "https://arxiv.org/pdf/1905.10044.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.CodeXGlue.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{DBLP:journals/corr/abs-2102-04664,]A;author    = {Shuai Lu and]A;Daya Guo and]A;Shuo Ren and]A;Junjie Huang and]A;Alexey Svyatkovskiy and]A;Ambrosio Blanco and]A;Colin B. Clement and]A;Dawn Drain and]A;Daxin Jiang and]A;Duyu Tang and]A;Ge Li and]A;Lidong Zhou and]A;Linjun Shou and]A;Long Zhou and]A;Michele Tufano and]A;Ming Gong and]A;Ming Zhou and]A;Nan Duan and]A;Neel Sundaresan and]A;Shao Kun Deng and]A;Shengyu Fu and]A;Shujie Liu},]A;title     = {CodeXGLUE: {A} Machine Learning Benchmark Dataset for Code Understanding]A;and Generation},]A;journal   = {CoRR},]A;volume    = {abs/2102.04664},]A;year      = {2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{DBLP:journals/corr/abs-2102-04664,]A;author    = {Shuai Lu and]A;Daya Guo and]A;Shuo Ren and]A;Junjie Huang and]A;Alexey Svyatkovskiy and]A;Ambrosio Blanco and]A;Colin B. Clement and]A;Dawn Drain and]A;Daxin Jiang and]A;Duyu Tang and]A;Ge Li and]A;Lidong Zhou and]A;Linjun Shou and]A;Long Zhou and]A;Michele Tufano and]A;Ming Gong and]A;Ming Zhou and]A;Nan Duan and]A;Neel Sundaresan and]A;Shao Kun Deng and]A;Shengyu Fu and]A;Shujie Liu},]A;title     = {CodeXGLUE: {A} Machine Learning Benchmark Dataset for Code Understanding]A;and Generation},]A;journal   = {CoRR},]A;volume    = {abs/2102.04664},]A;year      = {2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.CodeXGlue.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CodeXGLUE stands for General Language Understanding Evaluation benchmark for CODE. It is a benchmark dataset for code intelligence.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CodeXGLUE 代表代码的常规语言理解评估基准。它是代码智能的基准数据集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.CodeXGlue.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CodeXGlue Clone Detection BigCloneBench]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CodeXGlue 克隆检测 BigCloneBench]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.CodeXGlue.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/microsoft/codexglue_method_generation"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/microsoft/codexglue_method_generation"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.DROP.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{Dua2019DROP,]A;author={Dheeru Dua and Yizhong Wang and Pradeep Dasigi and Gabriel Stanovsky and Sameer Singh and Matt Gardner},]A;title={  {DROP}: A Reading Comprehension Benchmark Requiring Discrete Reasoning Over Paragraphs},]A;booktitle={Proc. of NAACL},]A;year={2019}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{Dua2019DROP,]A;作者={Dheeru Dua 和 Yizhong Wang 和 Pradeep Dasigi 和 Gabriel Stanovsky 和 Sameer Singh 和 Matt Gardner},]A;标题={  {DROP}: 要求对段落进行离散推理的阅读理解基准},]A;booktitle={Proc. of NAACL},]A;年份={2019}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.DROP.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DROP: A Reading Comprehension Benchmark Requiring Discrete Reasoning Over Paragraphs. DROP is a crowdsourced, adversarially-created, 96k-question benchmark, in which a system must resolve references in a question, perhaps to multiple input positions, and perform discrete operations over them (such as addition, counting, or sorting). These operations require a much more comprehensive understanding of the content of paragraphs than what was necessary for prior datasets.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DROP: 需要对段落进行离散推理的阅读理解基准。 DROP 是众包、对抗创建的 96k 个问题基准，其中系统必须解析问题中的引用(可能是对多个输入位置的引用)，并对它们执行离散操作(例如加法、计数或排序)。与之前数据集需要的内容相比，这些操作需要对段落内容有更全面的理解。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.DROP.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DROP]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DROP]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.DROP.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/drop",]A;"arxiv": "https://arxiv.org/pdf/1903.00161.pdf",]A;"webpage": "https://allenai.org/data/drop"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/drop",]A;"arxiv": "https://arxiv.org/pdf/1903.00161.pdf",]A;“网页”: "https://allenai.org/data/drop"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FEVER.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{Thorne18Fever,]A;author = {Thorne, James and Vlachos, Andreas and Christodoulopoulos, Christos and Mittal, Arpit},]A;title = {{FEVER}: a Large-scale Dataset for Fact Extraction and {VERification}},]A;booktitle = {NAACL-HLT},]A;year = {2018}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{Thorne18Fever,]A;author = {Thorne, James and Vlachos, Andreas and Christodoulopoulos, Christos and Mittal, Arpit},]A;title = {{FEVER}: a Large-scale Dataset for Fact Extraction and {VERification}},]A;booktitle = {NAACL-HLT},]A;year = {2018}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FEVER.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FEVER (Fact Extraction and VERification) consists of 185,445 claims generated by altering sentences extracted from Wikipedia and subsequently verified without knowledge of the sentence they were derived from.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FEVER (事实提取和验证)由 185,445 条声明组成，这些声明是通过更改从维基百科中提取的句子生成的，随后在不知道其来源句子的情况下进行验证。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FEVER.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FEVER]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FEVER]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FEVER.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/fever",]A;"webpage": "https://fever.ai/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/fever",]A;"webpage": "https://fever.ai/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FloresBitextMining.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{nllb2022,]A;author    = {NLLB Team, Marta R. Costa-jussà, James Cross, Onur Çelebi, Maha Elbayad, Kenneth Heafield, Kevin Heffernan, Elahe Kalbassi,  Janice Lam, Daniel Licht, Jean Maillard, Anna Sun, Skyler Wang, Guillaume Wenzek, Al Youngblood, Bapi Akula, Loic Barrault, Gabriel Mejia Gonzalez, Prangthip Hansanti, John Hoffman, Semarley Jarrett, Kaushik Ram Sadagopan, Dirk Rowe, Shannon Spruit, Chau Tran, Pierre Andrews, Necip Fazil Ayan, Shruti Bhosale, Sergey Edunov, Angela Fan, Cynthia Gao, Vedanuj Goswami, Francisco Guzmán, Philipp Koehn, Alexandre Mourachko, Christophe Ropers, Safiyyah Saleem, Holger Schwenk, Jeff Wang},]A;title     = {No Language Left Behind: Scaling Human-Centered Machine Translation},]A;year      = {2022}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{nllb2022,]A;author    = {NLLB Team, Marta R. Costa-jussà, James Cross, Onur Çelebi, Maha Elbayad, Kenneth Heafield, Kevin Heffernan, Elahe Kalbassi,  Janice Lam, Daniel Licht, Jean Maillard, Anna Sun, Skyler Wang, Guillaume Wenzek, Al Youngblood, Bapi Akula, Loic Barrault, Gabriel Mejia Gonzalez, Prangthip Hansanti, John Hoffman, Semarley Jarrett, Kaushik Ram Sadagopan, Dirk Rowe, Shannon Spruit, Chau Tran, Pierre Andrews, Necip Fazil Ayan, Shruti Bhosale, Sergey Edunov, Angela Fan, Cynthia Gao, Vedanuj Goswami, Francisco Guzmán, Philipp Koehn, Alexandre Mourachko, Christophe Ropers, Safiyyah Saleem, Holger Schwenk, Jeff Wang},]A;title     = {No Language Left Behind: Scaling Human-Centered Machine Translation},]A;year      = {2022}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FloresBitextMining.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FLORES is a benchmark dataset for machine translation between English and low-resource languages.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FLORES 是一个用于在英语和低资源语言之间进行机器翻译的基准数据集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FloresBitextMining.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FloresBitextMining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FloresBitextMining]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.FloresBitextMining.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/facebook/flores"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/facebook/flores"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GPQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{rein2023gpqagraduatelevelgoogleproofqa,]A;title={GPQA: A Graduate-Level Google-Proof Q&A Benchmark},]A;author={David Rein and Betty Li Hou and Asa Cooper Stickland and Jackson Petty and Richard Yuanzhe Pang and Julien Dirani and Julian Michael and Samuel R. Bowman},]A;year={2023},]A;eprint={2311.12022},]A;archivePrefix={arXiv},]A;primaryClass={cs.AI},]A;url={https://arxiv.org/abs/2311.12022}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{rein2023gpqagraduatelevelgoogleproofqa，]A;title={GPQA： A Graduate-Level Google-Proof QA Benchmark}，]A;author={David Rein and Betty Li Samuel and Asa Samuel Stickland and Michael Samuel And Richard Yuanzhe Pang and Julien Dirani and Michael Michael and Samuel R. Bowman}，]A;year={2023}，]A;eprint={2311.12022}，]A;archivePrefix={arXiv}，]A;primaryClass={cs.AI}，]A;url={#A(&A)2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GPQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPQA is a multiple-choice, Q&A dataset of very hard questions written and validated by experts in biology, physics, and chemistry. When attempting questions out of their own domain (e.g., a physicist answers a chemistry question), these experts get only 34% accuracy, despite spending >30m with full access to Google.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPQA 是多重选择的问答数据集，其中包含由数学、物理和化学专家编写和验证的非常难解的问题。在尝试自行域之外的问题时 (，例如，物理专家) 回答化学问题，这些专家仅获得 34% 准确性，尽管花费了 30 分钟 >完全访问 Google(&A)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GPQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GPQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/Idavidrein/gpqa",]A;"arxiv": "https://arxiv.org/pdf/2311.12022"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[“hugging_face”： "https://huggingface.co/datasets/Idavidrein/gpqa"，]A;“arxiv”： "https://arxiv.org/pdf/2311.12022"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GSM8K.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{cobbe2021gsm8k,]A;title={Training Verifiers to Solve Math Word Problems},]A;author={Cobbe, Karl and Kosaraju, Vineet and Bavarian, Mohammad and Chen, Mark and Jun, Heewoo and Kaiser, Lukasz and Plappert, Matthias and Tworek, Jerry and Hilton, Jacob and Nakano, Reiichiro and Hesse, Christopher and Schulman, John},]A;journal={arXiv preprint arXiv:2110.14168},]A;year={2021}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{cobbe2021gsm8k,]A;标题={训练验证器解决数学应用题},]A;作者={Cobbe, Karl 和 Kosaraju, Vineet 和 Bavarian, Mohammad 和 Chen, Mark 和 Jun, Heewoo 和 Kaiser, Lukasz 和 Plappert, Matthias 和 Tworek, Jerry 和 Hilton, Jacob 和 Nakano, Reiichiro 和 Hesse, Christopher 和 Schulman, John},]A;期刊={arXiv preprint arXiv:2110.14168},]A;年份={2021}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GSM8K.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GSM8K is a dataset of 8.5K high quality grade school math word problems created by human problem writers. These problems take between 2 and 8 steps to solve, and solutions primarily involve performing a sequence of elementary calculations using basic arithmetic operations (+ - ×÷) to reach the final answer. A bright middle school student should be able to solve every problem. It can be used for multi-step mathematical reasoning. Chain of thought prompting is a method that enables models to decompose multi-step problems into intermediate steps. In other words, instead of asking the model to predict the answer directly, we ask to see the intermediate steps of the calculation. This technique has been shown in some cases to significantly improve quality.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GSM8K 是由人类问题编写者创建的 8.5K 个高质量小学数学应用题的数据集。解决这些问题需要 2 到 8 个步骤，解决方案主要涉及使用基本算术运算 (+ - ×÷) 执行一系列基本计算以获得最终答案。一名聪明的中学生应能够解决每一个问题。它可用于多步骤数学推理。思维链提示是一种使模型能够将多步骤问题分解为中间步骤的方法。换句话说，我们不要求模型直接预测答案，而是要求查看计算的中间步骤。在一些情况下，此技术已被证明可显着提高质量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GSM8K.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GSM8K Chain Of Thought]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GSM8K 思维链]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.GSM8K.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/gsm8k",]A;"arxiv": "https://arxiv.org/abs/2110.14168",]A;"webpage": "https://openai.com/blog/grade-school-math"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/gsm8k",]A;"arxiv": "https://arxiv.org/abs/2110.14168",]A;“网页”: "https://openai.com/blog/grade-school-math"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HellaSwag.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{zellers2019hellaswag,]A;title={HellaSwag: Can a Machine Really Finish Your Sentence?},]A;author={Zellers, Rowan and Holtzman, Ari and Bisk, Yonatan and Farhadi, Ali and Choi, Yejin},]A;booktitle ={Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics},]A;year={2019}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{zellers2019hellaswag,]A;标题={HellaSwag: 机器真的能完成你的句子吗?},]A;作者={Zellers, Rowan 和 Holtzman, Ari 和 Bisk, Yonatan 和 Farhadi, Ali 和 Choi, Yejin},]A;booktitle ={计算语言学协会第 57 届年会论文集},]A;年份={2019}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HellaSwag.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HellaSwag is a dataset for commonsense natural language inference, where a machine must select the most likely followup to an event description. The dataset contains 70,000 examples, each with four possible endings, one of which is correct. The dataset is designed to be challenging for state-of-the-art models, by using Adversarial Filtering to select machine-generated wrong answers that are often misclassified by pretrained models. The dataset covers various domains and requires both world knowledge and logical reasoning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HellaSwag 是用于常识自然语言推理的数据集，其中计算机必须选择最可能的事件说明后续内容。该数据集包含 70,000 个示例，每个示例都有四个可能的结尾(其中一个正确)。该数据集旨在挑战最先进的模型，方法是使用“对抗过滤”选择计算机生成的错误答案，这些答案通常被预训练模型错误地分类。该数据集涵盖各个领域，需要世界知识和逻辑推理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HellaSwag.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HellaSwag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HellaSwag]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HellaSwag.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/hellaswag",]A;"arxiv": "https://arxiv.org/abs/1905.07830",]A;"webpage": "https://rowanzellers.com/hellaswag"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/hellaswag",]A;"arxiv": "https://arxiv.org/abs/1905.07830",]A;“网页”: "https://rowanzellers.com/hellaswag"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{chen2021evaluating,]A;title={Evaluating Large Language Models Trained on Code},]A;author={Mark Chen and Jerry Tworek and Heewoo Jun and Qiming Yuan and Henrique Ponde de Oliveira Pinto and Jared Kaplan and Harri Edwards and Yuri Burda and Nicholas Joseph and Greg Brockman and Alex Ray and Raul Puri and Gretchen Krueger and Michael Petrov and Heidy Khlaaf and Girish Sastry and Pamela Mishkin and Brooke Chan and Scott Gray and Nick Ryder and Mikhail Pavlov and Alethea Power and Lukasz Kaiser and Mohammad Bavarian and Clemens Winter and Philippe Tillet and Felipe Petroski Such and Dave Cummings and Matthias Plappert and Fotios Chantzis and Elizabeth Barnes and Ariel Herbert-Voss and William Hebgen Guss and Alex Nichol and Alex Paino and Nikolas Tezak and Jie Tang and Igor Babuschkin and Suchir Balaji and Shantanu Jain and William Saunders and Christopher Hesse and Andrew N. Carr and Jan Leike and Josh Achiam and Vedant Misra and Evan Morikawa and Alec Radford and Matthew Knight and Miles Brundage and Mira Murati and Katie Mayer and Peter Welinder and Bob McGrew and Dario Amodei and Sam McCandlish and Ilya Sutskever and Wojciech Zaremba},]A;year={2021},]A;eprint={2107.03374},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{chen2021evaluating,]A;标题={评估在代码上训练的大型语言模型},]A;作者={Mark Chen 和 Jerry Tworek 和 Heewoo Jun 和 Qiming Yuan 和 Henrique Ponde de Oliveira Pinto 和 Jared Kaplan 和 Harri Edwards 和 Yuri Burda 和 Nicholas Joseph 和 Greg Brockman 和 Alex Ray 和 Raul Puri 和 Gretchen Krueger 和 Michael Petrov 和 Heidy Khlaaf 和 Girish Sastry 和 Pamela Mishkin 和 Brooke Chan 和 Scott Gray 和 Nick Ryder 和 Mikhail Pavlov 和 Alethea Power 和 Lukasz Kaiser 和 Mohammad Bavarian 和 Clemens Winter 和 Philippe Tillet 和 Felipe Petroski Such 和 Dave Cummings 和 Matthias Plappert 和 Fotios Chantzis 和 Elizabeth Barnes 和 Ariel Herbert-Voss 和 William Hebgen Guss 和 Alex Nichol 和 Alex Paino 和 Nikolas Tezak 和 Jie Tang 和 Igor Babuschkin 和 Suchir Balaji 和 Shantanu Jain 和 William Saunders 和 Christopher Hesse 和 Andrew N. Carr 和 Jan Leike 和 Josh Achiam 和 Vedant Misra 和 Evan Morikawa 和 Alec Radford 和 Matthew Knight 和 Miles Brundage 和 Mira Murati 和 Katie Mayer 和 Peter Welinder 和 Bob McGrew 和 Dario Amodei 和 Sam McCandlish 和 Ilya Sutskever 和 Wojciech Zaremba},]A;year={2021},]A;eprint={2107.03374},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HumanEval is a dataset for evaluating the functional correctness of code synthesis from natural language docstrings. It consists of 164 hand-written programming problems in Python, each with a function signature, docstring, body, and several unit tests. The problems cover a range of topics, such as language comprehension, reasoning, algorithms, and simple mathematics. The dataset is designed to measure the problem-solving capabilities of large language models trained on code, such as Codex.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HumanEval 是数据集，用于从自然语言文档字符串评估代码合成的功能正确性。它由 164 个用 Python 手写的编程问题组成，每个问题都有函数签名、文档字符串、正文和几个单元测试。这些问题涵盖了一系列主题，例如语言理解、推理、算法和简单数学。该数据集旨在衡量在代码上训练的大型语言模型(例如 Codex)解决问题的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HumanEval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HumanEval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/openai/human-eval",]A;"arxiv": "https://arxiv.org/abs/2107.03374"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/openai/human-eval",]A;"arxiv": "https://arxiv.org/abs/2107.03374"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEvalPlus.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{chen2021evaluating,]A;title={Evaluating Large Language Models Trained on Code},]A;author={Mark Chen and Jerry Tworek and Heewoo Jun and Qiming Yuan and Henrique Ponde de Oliveira Pinto and Jared Kaplan and Harri Edwards and Yuri Burda and Nicholas Joseph and Greg Brockman and Alex Ray and Raul Puri and Gretchen Krueger and Michael Petrov and Heidy Khlaaf and Girish Sastry and Pamela Mishkin and Brooke Chan and Scott Gray and Nick Ryder and Mikhail Pavlov and Alethea Power and Lukasz Kaiser and Mohammad Bavarian and Clemens Winter and Philippe Tillet and Felipe Petroski Such and Dave Cummings and Matthias Plappert and Fotios Chantzis and Elizabeth Barnes and Ariel Herbert-Voss and William Hebgen Guss and Alex Nichol and Alex Paino and Nikolas Tezak and Jie Tang and Igor Babuschkin and Suchir Balaji and Shantanu Jain and William Saunders and Christopher Hesse and Andrew N. Carr and Jan Leike and Josh Achiam and Vedant Misra and Evan Morikawa and Alec Radford and Matthew Knight and Miles Brundage and Mira Murati and Katie Mayer and Peter Welinder and Bob McGrew and Dario Amodei and Sam McCandlish and Ilya Sutskever and Wojciech Zaremba},]A;year={2021},]A;eprint={2107.03374},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{chen2021evaluating,]A;标题={评估在代码上训练的大型语言模型},]A;作者={Mark Chen 和 Jerry Tworek 和 Heewoo Jun 和 Qiming Yuan 和 Henrique Ponde de Oliveira Pinto 和 Jared Kaplan 和 Harri Edwards 和 Yuri Burda 和 Nicholas Joseph 和 Greg Brockman 和 Alex Ray 和 Raul Puri 和 Gretchen Krueger 和 Michael Petrov 和 Heidy Khlaaf 和 Girish Sastry 和 Pamela Mishkin 和 Brooke Chan 和 Scott Gray 和 Nick Ryder 和 Mikhail Pavlov 和 Alethea Power 和 Lukasz Kaiser 和 Mohammad Bavarian 和 Clemens Winter 和 Philippe Tillet 和 Felipe Petroski Such 和 Dave Cummings 和 Matthias Plappert 和 Fotios Chantzis 和 Elizabeth Barnes 和 Ariel Herbert-Voss 和 William Hebgen Guss 和 Alex Nichol 和 Alex Paino 和 Nikolas Tezak 和 Jie Tang 和 Igor Babuschkin 和 Suchir Balaji 和 Shantanu Jain 和 William Saunders 和 Christopher Hesse 和 Andrew N. Carr 和 Jan Leike 和 Josh Achiam 和 Vedant Misra 和 Evan Morikawa 和 Alec Radford 和 Matthew Knight 和 Miles Brundage 和 Mira Murati 和 Katie Mayer 和 Peter Welinder 和 Bob McGrew 和 Dario Amodei 和 Sam McCandlish 和 Ilya Sutskever 和 Wojciech Zaremba},]A;year={2021},]A;eprint={2107.03374},]A;archivePrefix={arXiv}，]A;primaryClass={cs.LG}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEvalPlus.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HumanEval is a dataset for evaluating the functional correctness of code synthesis from natural language docstrings. It consists of 164 hand-written programming problems in Python, each with a function signature, docstring, body, and several unit tests. The problems cover a range of topics, such as language comprehension, reasoning, algorithms, and simple mathematics. The dataset is designed to measure the problem-solving capabilities of large language models trained on code, such as Codex.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HumanEval 是数据集，用于从自然语言文档字符串评估代码合成的功能正确性。它由 164 个用 Python 手写的编程问题组成，每个问题都有函数签名、文档字符串、正文和几个单元测试。这些问题涵盖了一系列主题，例如语言理解、推理、算法和简单数学。该数据集旨在衡量在代码上训练的大型语言模型(例如 Codex)解决问题的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEvalPlus.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HumanEvalPlus]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HumanEvalPlus]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.HumanEvalPlus.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/openai/human-eval",]A;"arxiv": "https://arxiv.org/abs/2107.03374"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/openai/human-eval",]A;"arxiv": "https://arxiv.org/abs/2107.03374"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.IFEval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={Instruction-Following Evaluation for Large Language Models},]A;author={Jeffrey Zhou, Tianjian Lu, Swaroop Mishra, Siddhartha Brahma, Sujoy Basu, Yi Luan, Denny Zhou, Le Hou},]A;year={2023},]A;eprint={2311.07911},]A;archivePrefix={arXiv},]A;primaryClass={cs.LG}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={大型语言模型的指令遵循评估}，]A;作者={Jeffrey Zhou、Tianjian Lu、Swaroop Mishra、Siddhartha Brahma、Sujoy Basu、Yi Luan、Denny Zhou、Le Hou}，]A;year={2023},]A;eprint={2311.07911}，]A;archivePrefix={arXiv}，]A;primaryClass={cs.LG}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.IFEval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[IFEval is a dataset that tests the capability of models to clearly follow explicit instructions. The models are tested on their ability to strictly follow formatting instructions rather than the actual contents generated, allowing strict and rigorous metrics to be used. It focuses on a set of "verifiable instructions" such as "write in more than 400 words" and "mention the keyword of AI at least 3 times". There are 25 types of those verifiable instructions and 541 prompts, with each prompt containing one or more verifiable instructions. We use "strict_accuracy" as the primary metric for IFEval, which is defined as the percentage of prompts such that the corresponding response strictly satisfies all verifiable instructions. We also include a secondary metric called "loose_accuracy" that can be found in the AML experiment. The "loose_accuracy" only requires potential transformations of the model response to satisfy all verifiable instructions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[IFEval 是一个数据集，用于测试模型的功能以清楚地遵循显式说明。测试模型严格遵循格式指令而不是生成的实际内容的能力，从而允许使用严格的指标。它侧重于一组“可验证指令”，例如“写入超过 400 个字词”和“至少 3 次提及 AI 的关键字”。这些可验证指令有 25 种类型和 541 个提示，每个提示都包含一个或多个可验证指令。我们使用 "strict_accuracy" 作为 IFEval 的主要指标，该指标定义为提示的百分比，使相应的响应严格满足所有可验证指令。我们还包含一个名为 "loose_accuracy" 的辅助指标，该指标可在 AML 试验中找到。"loose_accuracy" 只需要模型响应的潜在转换才能满足所有可验证指令。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.IFEval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[IFEval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[IFEval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.IFEval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/papers/2311.07911",]A;"arxiv": "https://arxiv.org/abs/2311.07911"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/papers/2311.07911",]A;"arxiv": "https://arxiv.org/abs/2311.07911"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ImdbClassification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{maas-etal-2011-learning,]A;title = {Learning Word Vectors for Sentiment Analysis},]A;author = {Maas, Andrew L.  and]A;Daly, Raymond E.  and]A;Pham, Peter T.  and]A;Huang, Dan  and]A;Ng, Andrew Y.  and]A;Potts, Christopher},]A;editor = {Lin, Dekang  and]A;Matsumoto, Yuji  and]A;Mihalcea, Rada},]A;booktitle = {Proceedings of the 49th Annual Meeting of the Association for Computational Linguistics: Human Language Technologies},]A;month = jun,]A;year = {2011},]A;address = {Portland, Oregon, USA},]A;publisher = {Association for Computational Linguistics},]A;url = {https://aclanthology.org/P11-1015},]A;pages = {142--150},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{maas-etal-2011-learning,]A;title = {Learning Word Vectors for Sentiment Analysis},]A;author = {Maas, Andrew L.  and]A;Daly, Raymond E.  and]A;Pham, Peter T.  and]A;Huang, Dan  and]A;Ng, Andrew Y.  and]A;Potts, Christopher},]A;editor = {Lin, Dekang  and]A;Matsumoto, Yuji  and]A;Mihalcea, Rada},]A;booktitle = {Proceedings of the 49th Annual Meeting of the Association for Computational Linguistics: Human Language Technologies},]A;month = jun,]A;year = {2011},]A;address = {Portland, Oregon, USA},]A;publisher = {Association for Computational Linguistics},]A;url = {https://aclanthology.org/P11-1015},]A;pages = {142--150},}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ImdbClassification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Large Movie Review Dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大型影评数据集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ImdbClassification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ImdbClassification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ImdbClassification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ImdbClassification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/imdb",]A;"webpage": "http://www.aclweb.org/anthology/P11-1015"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/imdb",]A;"webpage": "http://www.aclweb.org/anthology/P11-1015"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LccSentimentClassification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{quasthoff-etal-2006-corpus,]A;title = {Corpus Portal for Search in Monolingual Corpora},]A;author = {Quasthoff, Uwe  and]A;  Richter, Matthias  and]A;  Biemann, Christian},]A;editor = {Calzolari, Nicoletta  and]A;Choukri, Khalid  and]A;Gangemi, Aldo  and]A;Maegaard, Bente  and]A;Mariani, Joseph  and]A;Odijk, Jan  and]A;Tapias, Daniel},]A;booktitle = {Proceedings of the Fifth International Conference on Language Resources and Evaluation ({LREC}{'}06)},]A;month = may,]A;year = {2006},]A;address = {Genoa, Italy},]A;publisher = {European Language Resources Association (ELRA)},]A;url = {http://www.lrec-conf.org/proceedings/lrec2006/pdf/641_pdf.pdf},]A;abstract = {A simple and flexible schema for storing and presenting monolingual language resources is proposed. In this format, data for 18 different languages is already available in various sizes. The data is provided free of charge for online use and download. The main target is to ease the application of algorithms for monolingual and interlingual studies.},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{quasthoff-etal-2006-corpus,]A;title = {Corpus Portal for Search in Monolingual Corpora},]A;author = {Quasthoff, Uwe  and]A; Richter, Matthias  and]A; Biemann, Christian},]A;editor = {Calzolari, Nicoletta  and]A;Choukri, Khalid  and]A;Gangemi, Aldo  and]A;Maegaard, Bente  and]A;Mariani, Joseph  and]A;Odijk, Jan  and]A;Tapias, Daniel},]A;booktitle = {Proceedings of the Fifth International Conference on Language Resources and Evaluation ({LREC}{'}06)},]A;month = may,]A;year = {2006},]A;address = {Genoa, Italy},]A;publisher = {European Language Resources Association (ELRA)},]A;url = {http://www.lrec-conf.org/proceedings/lrec2006/pdf/641_pdf.pdf},]A;abstract = {A simple and flexible schema for storing and presenting monolingual language resources is proposed.In this format, data for 18 different languages is already available in various sizes.The data is provided free of charge for online use and download.The main target is to ease the application of algorithms for monolingual and interlingual studies.},}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LccSentimentClassification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A simple and flexible schema for storing and presenting monolingual language resources is proposed. In this format, data for 18 different languages is already available in various sizes. The data is provided free of charge for online use and download. The main target is to ease the application of algorithms for monolingual and interlingual studies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提出了一种用来存储和呈现单语语言资源的简单而灵活的架构。已有 18 种不同语言的数据采用此格式以各种大小提供。数据是免费提供的，可供在线使用和下载。主要目标是简化算法在单语和语际研究中的应用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LccSentimentClassification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LccSentimentClassification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LccSentimentClassification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LccSentimentClassification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/DDSC/lcc",]A;"github": "https://github.com/fnielsen/lcc-sentiment"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/DDSC/lcc",]A;"github": "https://github.com/fnielsen/lcc-sentiment"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBench.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK},]A;author={Colin White, Samuel Dooley, Manley Roberts, Arka Pal, Benjamin Feuer, Siddhartha Jain, Ravid Shwartz-Ziv, Neel Jain, Khalid Saifullah, Sreemanti Dey, Shubh-Agrawal, Sandeep Singh Sandha, Siddartha Naidu, Chinmay Hegde, Yann LeCun, Tom Goldstein, Willie Neiswanger, Micah Goldblum},]A;year={2024},]A;eprint={2406.19314},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK}，]A;作者={Colin White、Samuel Dooley、Manley Roberts、Arka Pal、Benjamin Feuer、Siddhartha Jain、Ravid Shwartz-Ziv、Neel Jain、Khalid Saifullah、Sreemanti Dey、Shubh-Agrawal、Sandeep Singh Sandha、Siddartha Naidu、Chinmay Hegde、Yann LeCun、Tom Goldstein、Willie Neiswanger、Micah Goldblum}，]A;year={2024}，]A;eprint={2406.19314}，]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBench.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LiveBench is designed to limit potential contamination by releasing new questions monthly, as well as having questions based on recently-released datasets, arXiv papers, news articles, and IMDb movie synopses. Each question has verifiable, objective ground-truth answers, allowing hard questions to be scored accurately and automatically, without the use of an LLM judge. LiveBench currently contains a set of 18 diverse tasks across 6 categories, and we will release new, harder tasks over time. We update questions each month such that the benchmark completely refreshes every 6 months. The initial version was LiveBench-2024-06-24. This is coding tasks of the LiveBench release_date in ["2024-07-26", "2024-06-24"]5D;]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LiveBench 旨在通过每月发布新问题以及根据最近发布的数据集、arXiv 论文、新闻文章和 IMDb 电影摘要提出问题来限制潜在污染。每个问题都有可验证的、客观的基本事实答案，可以在不使用 LLM 判断的情况下自动准确地为难以解答的问题评分。LiveBench 目前包含一组跨 6 个类别的 18 个不同任务，我们将随时间推移发布新的、更难的任务。我们每月都会更新问题，以便基准测试每 6 个月完全刷新一次。初始版本为 LiveBench-2024-06-24。这是 ["2024-07-26", "2024-06-24"]5D; 中 LiveBench release_date 的编码任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBench.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LiveBench (code task)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LiveBench (代码任务)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBench.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchEasy.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK},]A;author={Colin White, Samuel Dooley, Manley Roberts, Arka Pal, Benjamin Feuer, Siddhartha Jain, Ravid Shwartz-Ziv, Neel Jain, Khalid Saifullah, Sreemanti Dey, Shubh-Agrawal, Sandeep Singh Sandha, Siddartha Naidu, Chinmay Hegde, Yann LeCun, Tom Goldstein, Willie Neiswanger, Micah Goldblum},]A;year={2024},]A;eprint={2406.19314},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK}，]A;作者={Colin White、Samuel Dooley、Manley Roberts、Arka Pal、Benjamin Feuer、Siddhartha Jain、Ravid Shwartz-Ziv、Neel Jain、Khalid Saifullah、Sreemanti Dey、Shubh-Agrawal、Sandeep Singh Sandha、Siddartha Naidu、Chinmay Hegde、Yann LeCun、Tom Goldstein、Willie Neiswanger、Micah Goldblum}，]A;year={2024}，]A;eprint={2406.19314}，]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchEasy.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Livecode bench easy set of problems. LiveBench is designed to limit potential contamination by releasing new questions monthly, as well as having questions based on recently-released datasets, arXiv papers, news articles, and IMDb movie synopses. Each question has verifiable, objective ground-truth answers, allowing hard questions to be scored accurately and automatically, without the use of an LLM judge. LiveBench currently contains a set of 18 diverse tasks across 6 categories, and we will release new, harder tasks over time. We update questions each month such that the benchmark completely refreshes every 6 months. The initial version was LiveBench-2024-06-24. This is coding tasks of the LiveBench release_date in ["2024-07-26", "2024-06-24"]5D;]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Livecode 工作台简单问题集。LiveBench 旨在通过每月发布新问题以及根据最近发布的数据集、arXiv 论文、新闻文章和 IMDb 电影摘要提出问题来限制潜在污染。每个问题都有可验证的、客观的基本事实答案，可以在不使用 LLM 判断的情况下自动准确地为难以解答的问题评分。LiveBench 目前包含一组跨 6 个类别的 18 个不同任务，我们将随时间推移发布新的、更难的任务。我们每月都会更新问题，以便基准测试每 6 个月完全刷新一次。初始版本为 LiveBench-2024-06-24。这是 ["2024-07-26", "2024-06-24"]5D; 中 LiveBench release_date 的编码任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchEasy.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LiveBench (Easy)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LiveBench (简单)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchEasy.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchMedium.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK},]A;author={Colin White, Samuel Dooley, Manley Roberts, Arka Pal, Benjamin Feuer, Siddhartha Jain, Ravid Shwartz-Ziv, Neel Jain, Khalid Saifullah, Sreemanti Dey, Shubh-Agrawal, Sandeep Singh Sandha, Siddartha Naidu, Chinmay Hegde, Yann LeCun, Tom Goldstein, Willie Neiswanger, Micah Goldblum},]A;year={2024},]A;eprint={2406.19314},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={ LIVEBENCH: A CHALLENGING,CONTAMINATION-LIMITED LLM BENCHMARK}，]A;作者={Colin White、Samuel Dooley、Manley Roberts、Arka Pal、Benjamin Feuer、Siddhartha Jain、Ravid Shwartz-Ziv、Neel Jain、Khalid Saifullah、Sreemanti Dey、Shubh-Agrawal、Sandeep Singh Sandha、Siddartha Naidu、Chinmay Hegde、Yann LeCun、Tom Goldstein、Willie Neiswanger、Micah Goldblum}，]A;year={2024}，]A;eprint={2406.19314}，]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchMedium.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Livecodebench set of problems of medium difficult. LiveBench is designed to limit potential contamination by releasing new questions monthly, as well as having questions based on recently-released datasets, arXiv papers, news articles, and IMDb movie synopses. Each question has verifiable, objective ground-truth answers, allowing hard questions to be scored accurately and automatically, without the use of an LLM judge. LiveBench currently contains a set of 18 diverse tasks across 6 categories, and we will release new, harder tasks over time. We update questions each month such that the benchmark completely refreshes every 6 months. The initial version was LiveBench-2024-06-24. This is coding tasks of the LiveBench release_date in ["2024-07-26", "2024-06-24"]5D;]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[中等难度的 Livecodebench 问题集。LiveBench 旨在通过每月发布新问题以及根据最近发布的数据集、arXiv 论文、新闻文章和 IMDb 电影摘要提出问题来限制潜在污染。每个问题都有可验证的、客观的基本事实答案，可以在不使用 LLM 判断的情况下自动准确地为难以解答的问题评分。LiveBench 目前包含一组跨 6 个类别的 18 个不同任务，我们将随时间推移发布新的、更难的任务。我们每月都会更新问题，以便基准测试每 6 个月完全刷新一次。初始版本为 LiveBench-2024-06-24。这是 ["2024-07-26", "2024-06-24"]5D; 中 LiveBench release_date 的编码任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchMedium.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LiveBench (Medium)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LiveBench (中等)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.LiveCodeBenchMedium.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://huggingface.co/datasets/livebench/coding",]A;"webpage": "https://livebench.ai/",]A;"arxiv": "https://arxiv.org/pdf/2406.19314"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MATH.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendrycksmath2021,]A;title={Measuring Mathematical Problem Solving With the MATH Dataset},]A;author={Dan Hendrycks and Collin Burns and Saurav Kadavath and Akul Arora and Steven Basart and Eric Tang and Dawn Song and Jacob Steinhardt},]A;journal={NeurIPS},]A;year={2021}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendrycksmath2021,]A;标题={使用 MATH 数据集衡量数学问题解答},]A;作者={Dan Hendrycks 和 Collin Burns 和 Saurav Kadavath 和 Akul Arora 和 Steven Basart 和 Eric Tang 和 Dawn Song 和 Jacob Steinhardt},]A;期刊={NeurIPS},]A;年份={2021}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MATH.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MATH is a new dataset of 12,500 challenging competition mathematics problems. Each problem in MATH has a full step-by-step solution which can be used to teach models to generate answer derivations and explanations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MATH 是包含 12,500 个具有挑战性的竞赛数学问题的新数据集。 MATH 中的每个问题都有完整的分步解决方案，可用于教授模型生成答案推导和解释。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MATH.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MATH]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数学]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MATH.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/math/blob/main/README.md",]A;"arxiv": "https://arxiv.org/pdf/2103.03874.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/math/blob/main/README.md",]A;"arxiv": "https://arxiv.org/pdf/2103.03874.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPP.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{austin2021programsynthesislargelanguage,]A;title={Program Synthesis with Large Language Models},]A;author={Jacob Austin and Augustus Odena and Maxwell Nye and Maarten Bosma and Henryk Michalewski and David Dohan and Ellen Jiang and Carrie Cai and Michael Terry and Quoc Le and Charles Sutton},]A;year={2021},]A;eprint={2108.07732},]A;archivePrefix={arXiv},]A;primaryClass={cs.PL},]A;url={https://arxiv.org/abs/2108.07732}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{{{2021programsynthesislargelanguage，]A;title={Program Synthesis with Large Language Models}，]A;author={并具有 Augustus Odena 和 Nye 以及 Maarten Bosma 和 Quok Michalewski 以及 David Dohan 和 David Dohan 江，以及 David Cai 和 Michael Michael and Quoc Le 和参历 Le 和参阅者}，]A;year={2021}，]A;eprint={2108.07732}，]A;archivePrefix={arXiv}，]A;primaryClass={cs.PL}，]A;url={https://arxiv.org/abs/2108.07732}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPP.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mostly Basic Python Problems Dataset (MBPP) consists of around 1,000 crowd-sourced Python programming problems, designed to be solvable by entry level programmers, covering programming fundamentals, standard library functionality, and so on. Each problem consists of a task description, code solution and 3 automated test cases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大多数基本 Python 问题数据集 (MBPP) 包含大约 1,000 个众源 Python 编程问题，旨在被入门级程序员搁置，包括编程基础知识、标准库功能等。每个问题都包含任务说明、代码解决方案和 3 个自动测试用例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPP.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MBPP]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MBPP]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPP.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/google-research-datasets/mbpp",]A;"github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"arxiv": "https://arxiv.org/abs/2108.07732"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[“hugging_face”： "https://huggingface.co/datasets/google-research-datasets/mbpp"，]A;“github”： "https://github.com/google-research/google-research/tree/master/mbpp"，]A;“arxiv”： "https://arxiv.org/abs/2108.07732"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPPPlus.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{title={Program Synthesis with Large Language Models},]A;author={Jacob Austin, Augustus Odena, Maxwell Nye, Maarten Bosma, Henryk Michalewski, David Dohan, Ellen Jiang, Carrie Cai, Michael Terry, Quoc Le, Charles Sutton},]A;year={2021},]A;eprint={2108.07732},]A;archivePrefix={arXiv}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{title={使用大型语言模型进行程序合成},]A;作者={Jacob Austin、Augustus Odena、Maxwell Nye、Maarten Bosma、Henryk Michalewski、David Dohan、Ellen Jiang、Carrie Cai、Michael Terry、Quoc Le、Charles Sutton}，]A;year={2021},]A;eprint={2108.07732}，]A;archivePrefix={arXiv}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPPPlus.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Mostly Basic Python Problems Dataset (MBPP) consists of high-quality crowd-sourced Python programming problems, designed to be solvable by entry-level programmers, covering programming fundamentals, standard library functionality, and so on. Each problem consists of a task description, code solution and 3 automated test cases. The creators provide two sets - mbpp.jsonl which is the full dataset consisting of around 1000 examples, and sanitized_mbpp.json - which is a subset of around 500 examples which the authors verified by hand. We use this smaller, high-quality subset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大多数基本的 Python 问题数据集(MBPP)由高质量众包 Python 编程问题组成，旨在由入门级程序员解决，涵盖编程基础知识、标准库功能等。每个问题都由任务说明、代码解决方案和 3 个自动测试用例组成。创建者提供两个集: mbpp.jsonl，这是由大约 1000 个示例组成的完整数据集；sanitized_mbpp.json，这是作者手动验证的大约 500 个示例的子集。我们使用这个较小的高质量子集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPPPlus.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MBPPPLUS]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MBPPPLUS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MBPPPlus.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"hugging_face": "https://huggingface.co/datasets/mbpp",]A;"arxiv": "https://arxiv.org/pdf/2108.07732.pdf"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google-research/google-research/tree/master/mbpp",]A;"hugging_face": "https://huggingface.co/datasets/mbpp",]A;"arxiv": "https://arxiv.org/pdf/2108.07732.pdf"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringP2P.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{scialom2020mlsum,]A;title={MLSUM: The Multilingual Summarization Corpus},]A;author={Scialom, Thomas and Dray, Paul-Alexis and Lamprier, Sylvain and Piwowarski, Benjamin and Staiano, Jacopo},]A;journal={arXiv preprint arXiv:2004.14900},]A;year={2020}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{scialom2020mlsum,]A;title={MLSUM: The Multilingual Summarization Corpus},]A;author={Scialom, Thomas and Dray, Paul-Alexis and Lamprier, Sylvain and Piwowarski, Benjamin and Staiano, Jacopo},]A;journal={arXiv preprint arXiv:2004.14900},]A;year={2020}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringP2P.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering of newspaper article contents and titles from MLSUM dataset. Clustering of 10 sets on the newpaper article topics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对 MLSUM 数据集中的报纸文章内容和标题进行聚类分析。对报纸文章主题的 10 个组进行聚类分析。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringP2P.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLSUMClusteringP2P]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MLSUMClusteringP2P]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringP2P.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mlsum"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mlsum"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringS2S.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{scialom2020mlsum,]A;title={MLSUM: The Multilingual Summarization Corpus},]A;author={Scialom, Thomas and Dray, Paul-Alexis and Lamprier, Sylvain and Piwowarski, Benjamin and Staiano, Jacopo},]A;journal={arXiv preprint arXiv:2004.14900},]A;year={2020}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{scialom2020mlsum,]A;title={MLSUM: The Multilingual Summarization Corpus},]A;author={Scialom, Thomas and Dray, Paul-Alexis and Lamprier, Sylvain and Piwowarski, Benjamin and Staiano, Jacopo},]A;journal={arXiv preprint arXiv:2004.14900},]A;year={2020}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringS2S.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering of newspaper article contents and titles from MLSUM dataset. Clustering of 10 sets on the newpaper article topics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对 MLSUM 数据集中的报纸文章内容和标题进行聚类分析。对报纸文章主题的 10 个组进行聚类分析。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringS2S.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MLSUMClusteringS2S]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MLSUMClusteringS2S]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MLSUMClusteringS2S.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mlsum"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mlsum"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Humanities.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendryckstest2021,]A; title={Measuring Massive Multitask Language Understanding},]A; author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt}, ]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendryckstest2021,]A;标题={衡量大规模多任务语言理解},]A;作者={Dan Hendrycks 和 Collin Burns 和 Steven Basart 和 Andy Zou 和 Mantas Mazeika 和 Dawn Song 和 Jacob Steinhardt},]A;期刊={国际表征学习大会 (ICLR) 论文集},]A;年份={2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Humanities.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Massive Multitask Language Understanding (MMLU) benchmark is a set of multiple choice questions covering 57 different task domains, from a wide spectrum of disciplines including Humanities, Social Sciences, STEM, and others. This dataset record is specific to the Humanities subset of the MMLU benchmark. The difficulty of questions ranges from elementary level knowledge to advanced professional expertise. Subjects include high school european history, high school us history, high school world history, international law, jurisprudence, prehistory, and professional law.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大规模多任务语言理解 (MMLU) 基准是一组多项选择题，涵盖 57 个不同的任务领域，涉及人文、社会科学、STEM 等广泛学科。此数据集记录特定于 MMLU 基准的人文子集。问题的难度范围从初级知识到高级专业知识。科目包括高中欧洲史、高中美国史、高中世界史、国际法、法理学、史前史和专业法。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Humanities.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU Humanities]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU 人文学科]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Humanities.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Other.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendryckstest2021,]A;title={Measuring Massive Multitask Language Understanding},]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendryckstest2021,]A;标题={衡量大规模多任务语言理解},]A;作者={Dan Hendrycks 和 Collin Burns 和 Steven Basart 和 Andy Zou 和 Mantas Mazeika 和 Dawn Song 和 Jacob Steinhardt},]A;期刊={国际表征学习大会 (ICLR) 论文集},]A;年份={2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Other.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Massive Multitask Language Understanding (MMLU) benchmark is a set of multiple choice questions covering 57 different task domains, from a wide spectrum of disciplines including Humanities, Social Sciences, STEM, and others.  This dataset record is specific to the "other" subset of the MMLU benchmark. The difficulty of questions ranges from elementary level knowledge to advanced professional expertise. Subjects include anatomy, business ethics, clinical knowledge, college medicine, global facts, human aging, management, marketing, medical genetics, miscellaneous, nutrition, professional accounting, professional medicine, and virology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大规模多任务语言理解 (MMLU) 基准是一组多项选择题，涵盖 57 个不同的任务领域，涉及人文、社会科学、STEM 等广泛学科。此数据集记录特定于 MMLU 基准的“其他”子集。问题的难度范围从初级知识到高级专业知识。科目包括解剖学、商业道德、临床知识、大学医学、全球事实、人类衰老、管理、市场营销、医学遗传学、杂项、营养学、专业会计、专业医学和病毒学。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Other.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU Other]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU 其他]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Other.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Pro.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{wang2024mmluprorobustchallengingmultitask,]A;title={MMLU-Pro: A More Robust and Challenging Multi-Task Language Understanding Benchmark},]A;author={Yubo Wang and Xueguang Ma and Ge Zhang and Yuansheng Ni and Abhranil Chandra and Shiguang Guo and Weiming Ren and Aaran Arulraj and Xuan He and Ziyan Jiang and Tianle Li and Max Ku and Kai Wang and Alex Zhuang and Rongqi Fan and Xiang Yue and Wenhu Chen},]A;year={2024},]A;eprint={2406.01574},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL},]A;url={https://arxiv.org/abs/2406.01574}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{wang2024mmluprorobustchallengingmultitask，]A;title={MMLU-Pro: 一个更可靠且更具挑战性的多任务语言理解基准}，]A;author={Yubo Wang、Xueguang Ma、Ge Zhang、Yuansheng Ni、Abhranil Chandra、Shiguang Guo、Weiming Ren、Aaran Arulraj、Xuan He、Ziyan Jiang、Tianle Li、Max Ku、Kai Wang、Alex Zhuang、Rongqi Fan、Xiang Yue、Wenhu Chen}，]A;year={2024}，]A;eprint={2406.01574}，]A;archivePrefix={arXiv}，]A;primaryClass={cs.CL}，]A;url={https://arxiv.org/abs/2406.01574}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Pro.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU-Pro Dataset is built on Massive Multitask Language Understanding (MMLU) dataset, MMLU-Pro dataset is a more robust and challenging massive multi-task understanding dataset tailored to more rigorously benchmark large language models' capabilities. This dataset contains 12K complex questions across various disciplines.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU-Pro 数据集基于大规模多任务语言理解 (MMLU) 数据集构建，MMLU-Pro 数据集是一个更加可靠且具有挑战性的大型多任务理解数据集，定制为更严格地检验大型语言模型的功能。此数据集包含跨各个专业的 12K 复杂问题。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Pro.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU-Pro]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU-Pro]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Pro.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/TIGER-Lab/MMLU-Pro",]A;"arxiv": "https://arxiv.org/abs/2406.01574"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[“hugging_face”： "https://huggingface.co/datasets/TIGER-Lab/MMLU-Pro"，]A;“arxiv”： "https://arxiv.org/abs/2406.01574"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Social_Sciences.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendryckstest2021,]A;title={Measuring Massive Multitask Language Understanding},]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendryckstest2021,]A;标题={衡量大规模多任务语言理解},]A;作者={Dan Hendrycks 和 Collin Burns 和 Steven Basart 和 Andy Zou 和 Mantas Mazeika 和 Dawn Song 和 Jacob Steinhardt},]A;期刊={国际表征学习大会 (ICLR) 论文集},]A;年份={2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Social_Sciences.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Massive Multitask Language Understanding (MMLU) benchmark is a set of multiple choice questions covering 57 different task domains, from a wide spectrum of disciplines including Humanities, Social Sciences, STEM, and others. This dataset record is specific to the Social Sciences subset of the MMLU benchmark. The difficulty of questions ranges from elementary level knowledge to advanced professional expertise. Subjects include econometrics, high school geography, high school government and politics, high school macroeconomics, high school microeconomics, high school psychology, human sexuality, professional psychology, public relations, security studies, sociology, and us foreign policy.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大规模多任务语言理解 (MMLU) 基准是一组多项选择题，涵盖 57 个不同的任务领域，涉及人文、社会科学、STEM 等广泛学科。此数据集记录特定于 MMLU 基准的社会科学子集。问题的难度范围从初级知识到高级专业知识。科目包括计量经济学、高中地理、高中政府与政治、高中宏观经济学、高中微观经济学、高中心理学、人类性行为、职业心理学、公共关系、安全研究、社会学和美国外交政策。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Social_Sciences.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU Social Sciences]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU 社会科学]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Social_Sciences.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Stem.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{hendryckstest2021,]A;title={Measuring Massive Multitask Language Understanding},]A;author={Dan Hendrycks and Collin Burns and Steven Basart and Andy Zou and Mantas Mazeika and Dawn Song and Jacob Steinhardt},]A;journal={Proceedings of the International Conference on Learning Representations (ICLR)},]A;year={2021}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{hendryckstest2021,]A;标题={衡量大规模多任务语言理解},]A;作者={Dan Hendrycks 和 Collin Burns 和 Steven Basart 和 Andy Zou 和 Mantas Mazeika 和 Dawn Song 和 Jacob Steinhardt},]A;期刊={国际表征学习大会 (ICLR) 论文集},]A;年份={2021}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Stem.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Massive Multitask Language Understanding (MMLU) benchmark is a set of multiple choice questions covering 57 different task domains, from a wide spectrum of disciplines including Humanities, Social Sciences, STEM, and others. This dataset record is specific to the Science, Technology, Engineering, and Mathematics (STEM) subset of the MMLU benchmark. The difficulty of questions ranges from elementary level knowledge to advanced professional expertise. Subjects include abstract algebra, astronomy, college biology, college chemistry, college computer science, college mathematics, college physics, computer security, conceptual physics, electrical engineering, elementary mathematics, high school biology, high school chemistry, high school computer science, high school mathematics, high school physics, high school statistics, and machine learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大规模多任务语言理解 (MMLU) 基准是一组多项选择题，涵盖 57 个不同的任务领域，涉及人文、社会科学、STEM 等广泛学科。此数据集记录特定于 MMLU 基准的科学、技术、工程和数学 (STEM) 子集。问题的难度范围从初级知识到高级专业知识。科目包括抽象代数、天文学、大学生物学、大学化学、大学计算机科学、大学数学、大学物理、计算机安全、概念物理、电子工程、初等数学、高中生物、高中化学、高中计算机科学、高中数学、高中物理、高中统计学和机器学习。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Stem.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MMLU STEM]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MMLU STEM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MMLU_Stem.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/hendrycks/test/blob/master/README.md",]A;"arxiv": "https://arxiv.org/abs/2009.03300"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringP2P.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{Adelani2023MasakhaNEWS,]A;title={MasakhaNEWS: News Topic Classification for African languages},]A;author={David Ifeoluwa Adelani and  Marek Masiak and  Israel Abebe Azime and  Jesujoba Oluwadara Alabi and  Atnafu Lambebo Tonja and  Christine Mwase and  Odunayo Ogundepo and  Bonaventure F. P. Dossou and  Akintunde Oladipo and  Doreen Nixdorf and  Chris Chinenye Emezue and  Sana Sabah al-azzawi and  Blessing K. Sibanda and  Davis David and  Lolwethu Ndolela and  Jonathan Mukiibi and  Tunde Oluwaseyi Ajayi and  Tatiana Moteu Ngoli and  Brian Odhiambo and  Abraham Toluwase Owodunni and  Nnaemeka C. Obiefuna and  Shamsuddeen Hassan Muhammad and  Saheed Salahudeen Abdullahi and  Mesay Gemeda Yigezu and  Tajuddeen Gwadabe and  Idris Abdulmumin and  Mahlet Taye Bame and  Oluwabusayo Olufunke Awoyomi and  Iyanuoluwa Shode and  Tolulope Anu Adelani and  Habiba Abdulganiy Kailani and  Abdul-Hakeem Omotayo and  Adetola Adeeko and  Afolabi Abeeb and  Anuoluwapo Aremu and  Olanrewaju Samuel and  Clemencia Siro and  Wangari Kimotho and  Onyekachi Raphael Ogbu and  Chinedu E. Mbonu and  Chiamaka I. Chukwuneke and  Samuel Fanijo and  Jessica Ojo and  Oyinkansola F. Awosan and  Tadesse Kebede Guge and  Sakayo Toadoum Sari and  Pamela Nyatsine and  Freedmore Sidume and  Oreen Yousuf and  Mardiyyah Oduwole and  Ussen Kimanuka and  Kanda Patrick Tshinu and  Thina Diko and  Siyanda Nxakama and   Abdulmejid Tuni Johar and  Sinodos Gebre and  Muhidin Mohamed and  Shafie Abdi Mohamed and  Fuad Mire Hassan and  Moges Ahmed Mehamed and  Evrard Ngabire and  and Pontus Stenetorp},]A;journal={ArXiv},]A;year={2023},]A;volume={}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{Adelani2023MasakhaNEWS,]A;title={MasakhaNEWS: News Topic Classification for African languages},]A;author={David Ifeoluwa Adelani and  Marek Masiak and  Israel Abebe Azime and  Jesujoba Oluwadara Alabi and  Atnafu Lambebo Tonja and  Christine Mwase and  Odunayo Ogundepo and  Bonaventure F. P. Dossou and  Akintunde Oladipo and  Doreen Nixdorf and  Chris Chinenye Emezue and  Sana Sabah al-azzawi and  Blessing K. Sibanda and  Davis David and  Lolwethu Ndolela and  Jonathan Mukiibi and  Tunde Oluwaseyi Ajayi and  Tatiana Moteu Ngoli and  Brian Odhiambo and  Abraham Toluwase Owodunni and  Nnaemeka C. Obiefuna and  Shamsuddeen Hassan Muhammad and  Saheed Salahudeen Abdullahi and  Mesay Gemeda Yigezu and  Tajuddeen Gwadabe and  Idris Abdulmumin and  Mahlet Taye Bame and  Oluwabusayo Olufunke Awoyomi and  Iyanuoluwa Shode and  Tolulope Anu Adelani and  Habiba Abdulganiy Kailani and  Abdul-Hakeem Omotayo and  Adetola Adeeko and  Afolabi Abeeb and  Anuoluwapo Aremu and  Olanrewaju Samuel and  Clemencia Siro and  Wangari Kimotho and  Onyekachi Raphael Ogbu and  Chinedu E. Mbonu and  Chiamaka I. Chukwuneke and  Samuel Fanijo and  Jessica Ojo and  Oyinkansola F. Awosan and  Tadesse Kebede Guge and  Sakayo Toadoum Sari and  Pamela Nyatsine and  Freedmore Sidume and  Oreen Yousuf and  Mardiyyah Oduwole and  Ussen Kimanuka and  Kanda Patrick Tshinu and  Thina Diko and  Siyanda Nxakama and   Abdulmejid Tuni Johar and  Sinodos Gebre and  Muhidin Mohamed and  Shafie Abdi Mohamed and  Fuad Mire Hassan and  Moges Ahmed Mehamed and  Evrard Ngabire and  and Pontus Stenetorp},]A;journal={ArXiv},]A;year={2023},]A;volume={}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringP2P.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering of news article headlines from MasakhaNEWS dataset. Clustering of 10 sets on the news article label.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对来自 Masakha NEWS 数据集的新闻文章标题进行聚类分析。对新闻文章标签的 10 个组进行聚类分析。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringP2P.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MasakhaNEWSClusteringP2P]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MasakhaNEWSClusteringP2P]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringP2P.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/masakhane/masakhanews"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/masakhane/masakhanews"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringS2S.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{Adelani2023MasakhaNEWS,]A;title={MasakhaNEWS: News Topic Classification for African languages},]A;author={David Ifeoluwa Adelani and  Marek Masiak and  Israel Abebe Azime and  Jesujoba Oluwadara Alabi and  Atnafu Lambebo Tonja and  Christine Mwase and  Odunayo Ogundepo and  Bonaventure F. P. Dossou and  Akintunde Oladipo and  Doreen Nixdorf and  Chris Chinenye Emezue and  Sana Sabah al-azzawi and  Blessing K. Sibanda and  Davis David and  Lolwethu Ndolela and  Jonathan Mukiibi and  Tunde Oluwaseyi Ajayi and  Tatiana Moteu Ngoli and  Brian Odhiambo and  Abraham Toluwase Owodunni and  Nnaemeka C. Obiefuna and  Shamsuddeen Hassan Muhammad and  Saheed Salahudeen Abdullahi and  Mesay Gemeda Yigezu and  Tajuddeen Gwadabe and  Idris Abdulmumin and  Mahlet Taye Bame and  Oluwabusayo Olufunke Awoyomi and  Iyanuoluwa Shode and  Tolulope Anu Adelani and  Habiba Abdulganiy Kailani and  Abdul-Hakeem Omotayo and  Adetola Adeeko and  Afolabi Abeeb and  Anuoluwapo Aremu and  Olanrewaju Samuel and  Clemencia Siro and  Wangari Kimotho and  Onyekachi Raphael Ogbu and  Chinedu E. Mbonu and  Chiamaka I. Chukwuneke and  Samuel Fanijo and  Jessica Ojo and  Oyinkansola F. Awosan and  Tadesse Kebede Guge and  Sakayo Toadoum Sari and  Pamela Nyatsine and  Freedmore Sidume and  Oreen Yousuf and  Mardiyyah Oduwole and  Ussen Kimanuka and  Kanda Patrick Tshinu and  Thina Diko and  Siyanda Nxakama and   Abdulmejid Tuni Johar and  Sinodos Gebre and  Muhidin Mohamed and  Shafie Abdi Mohamed and  Fuad Mire Hassan and  Moges Ahmed Mehamed and  Evrard Ngabire and  and Pontus Stenetorp},]A;journal={ArXiv},]A;year={2023},]A;volume={}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{Adelani2023MasakhaNEWS,]A;title={MasakhaNEWS: News Topic Classification for African languages},]A;author={David Ifeoluwa Adelani and  Marek Masiak and  Israel Abebe Azime and  Jesujoba Oluwadara Alabi and  Atnafu Lambebo Tonja and  Christine Mwase and  Odunayo Ogundepo and  Bonaventure F. P. Dossou and  Akintunde Oladipo and  Doreen Nixdorf and  Chris Chinenye Emezue and  Sana Sabah al-azzawi and  Blessing K. Sibanda and  Davis David and  Lolwethu Ndolela and  Jonathan Mukiibi and  Tunde Oluwaseyi Ajayi and  Tatiana Moteu Ngoli and  Brian Odhiambo and  Abraham Toluwase Owodunni and  Nnaemeka C. Obiefuna and  Shamsuddeen Hassan Muhammad and  Saheed Salahudeen Abdullahi and  Mesay Gemeda Yigezu and  Tajuddeen Gwadabe and  Idris Abdulmumin and  Mahlet Taye Bame and  Oluwabusayo Olufunke Awoyomi and  Iyanuoluwa Shode and  Tolulope Anu Adelani and  Habiba Abdulganiy Kailani and  Abdul-Hakeem Omotayo and  Adetola Adeeko and  Afolabi Abeeb and  Anuoluwapo Aremu and  Olanrewaju Samuel and  Clemencia Siro and  Wangari Kimotho and  Onyekachi Raphael Ogbu and  Chinedu E. Mbonu and  Chiamaka I. Chukwuneke and  Samuel Fanijo and  Jessica Ojo and  Oyinkansola F. Awosan and  Tadesse Kebede Guge and  Sakayo Toadoum Sari and  Pamela Nyatsine and  Freedmore Sidume and  Oreen Yousuf and  Mardiyyah Oduwole and  Ussen Kimanuka and  Kanda Patrick Tshinu and  Thina Diko and  Siyanda Nxakama and   Abdulmejid Tuni Johar and  Sinodos Gebre and  Muhidin Mohamed and  Shafie Abdi Mohamed and  Fuad Mire Hassan and  Moges Ahmed Mehamed and  Evrard Ngabire and  and Pontus Stenetorp},]A;journal={ArXiv},]A;year={2023},]A;volume={}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringS2S.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering of news article headlines from MasakhaNEWS dataset. Clustering of 10 sets on the news article label.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对来自 Masakha NEWS 数据集的新闻文章标题进行聚类分析。对新闻文章标签的 10 个组进行聚类分析。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringS2S.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MasakhaNEWSClusteringS2S]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MasakhaNEWSClusteringS2S]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MasakhaNEWSClusteringS2S.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/masakhane/masakhanews"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/masakhane/masakhanews"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MultiNLI.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@InProceedings{N18-1101,]A;author = {Williams, Adina]A;and Nangia, Nikita]A;and Bowman, Samuel},]A;title = {A Broad-Coverage Challenge Corpus for ]A;Sentence Understanding through Inference},]A;booktitle = {Proceedings of the 2018 Conference of ]A;the North American Chapter of the ]A;Association for Computational Linguistics:]A;Human Language Technologies, Volume 1 (Long]A;Papers)},]A;year = {2018},]A;publisher = {Association for Computational Linguistics},]A;pages = {1112--1122},]A;location = {New Orleans, Louisiana},]A;url = {http://aclweb.org/anthology/N18-1101}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@InProceedings{N18-1101,]A;author = {Williams, Adina]A;and Nangia, Nikita]A;and Bowman, Samuel},]A;title = {A Broad-Coverage Challenge Corpus for]A;Sentence Understanding through Inference},]A;booktitle = {Proceedings of the 2018 Conference of]A;the North American Chapter of the]A;Association for Computational Linguistics:]A;Human Language Technologies, Volume 1 (Long]A;Papers)},]A;year = {2018},]A;publisher = {Association for Computational Linguistics},]A;pages = {1112--1122},]A;location = {New Orleans, Louisiana},]A;url = {http://aclweb.org/anthology/N18-1101}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MultiNLI.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Multi-Genre Natural Language Inference (MultiNLI) corpus is a crowd-sourced dataset of 433k sentence pairs annotated with textual entailment information, which served as the basis for the shared task of the RepEval 2017 Workshop at EMNLP in Copenhagen (https://repeval2017.github.io/shared/). This corpus is modeled after the Stanford NLI Corpus (SNLI), but differs in that covers a range of genres of spoken and written text, and supports a distinctive cross-genre generalization evaluation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多流派自然语言推理(MultiNLI)语料库是一个包含 433k 句子对的众包数据集，这些句子对带有文本蕴含信息注释，是哥本哈根 EMNLP 大会上的 2017 RepEval 研讨会共享任务的基础(https://repeval2017.github.io/shared/)。此语料库以斯坦福大学 NLI 语料库(SNLI)为模型，但不同之处在于涵盖了口语和书面文本的一系列流派，且支持独特的跨流派通用化评估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MultiNLI.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MultiNLI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MultiNLI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.MultiNLI.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["webpage": "https://cims.nyu.edu/~sbowman/multinli/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["webpage": "https://cims.nyu.edu/~sbowman/multinli/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NaturalQuestions.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{47761,]A;title={Natural Questions: a Benchmark for Question Answering Research},]A;author={Tom Kwiatkowski and Jennimaria Palomaki and Olivia Redfield and Michael Collins and Ankur Parikh and Chris Alberti and Danielle Epstein and Illia Polosukhin and Matthew Kelcey and Jacob Devlin and Kenton Lee and Kristina N. Toutanova and Llion Jones and Ming-Wei Chang and Andrew Dai and Jakob Uszkoreit and Quoc Le and Slav Petrov},]A;year={2019},]A;journal={Transactions of the Association of Computational Linguistics}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{47761,]A;标题={自然问题: 问答研究的基准},]A;作者={Tom Kwiatkowski 和 Jennimaria Palomaki 和 Olivia Redfield 和 Michael Collins 和 Ankur Parikh 和 Chris Alberti 和 Danielle Epstein 和 Illia Polosukhin 和 Matthew Kelcey 和 Jacob Devlin 和 Kenton Lee 和 Kristina N. Toutanova 和 Llion Jones 和 Ming-Wei Chang 和 Andrew Dai 和 Jakob Uszkoreit 和 Quoc Le 和 Slav Petrov},]A;年份={2019},]A;期刊={计算语言学协会汇刊}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NaturalQuestions.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The NQ corpus contains questions from real users, and it requires QA systems to read and comprehend an entire Wikipedia article that may or may not contain the answer to the question. The inclusion of real user questions, and the requirement that solutions should read an entire page to find the answer, cause NQ to be a more realistic and challenging task than prior QA datasets.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NQ 语料库包含来自真实用户的问题，它要求 QA 系统阅读并理解整篇维基百科文章，该文章可能包含也可能不包含问题的答案。由于包含真实的用户问题，以及要求解决方案应阅读整个页面以找到答案，因此 NQ 成为比之前的 QA 数据集更现实、更具挑战性的任务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NaturalQuestions.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NaturalQuestions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NaturalQuestions]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NaturalQuestions.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/google-research-datasets/natural-questions",]A;"hugging_face": "https://huggingface.co/datasets/natural_questions",]A;"webpage": "https://ai.google.com/research/NaturalQuestions"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/google-research-datasets/natural-questions",]A;"hugging_face": "https://huggingface.co/datasets/natural_questions",]A;“网页”: "https://ai.google.com/research/NaturalQuestions"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NoData.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NoData.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NoData.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This dataset does not have a description yet. Please check back later.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此数据集尚无说明。请稍后返回查看。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.NoData.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.OpenBookQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{OpenBookQA2018,]A;title={Can a Suit of Armor Conduct Electricity? A New Dataset for Open Book Question Answering},]A;author={Todor Mihaylov and Peter Clark and Tushar Khot and Ashish Sabharwal},]A;booktitle={EMNLP},]A;year={2018}]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{OpenBookQA2018,]A;标题={一套盔甲能导电吗? 用于开卷问答的新数据集},]A;作者={Todor Mihaylov 和 Peter Clark 和 Tushar Khot 和 Ashish Sabharwal},]A;booktitle={EMNLP},]A;年份={2018}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.OpenBookQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OpenBookQA is a dataset of 5,957 multiple-choice questions that require reasoning over a collection of 1,326 elementary science facts and additional external knowledge. The questions are designed to test the understanding of a broad range of topics and phenomena, and are not answerable from the provided facts alone. The questions cover various aspects of language understanding, such as coreference, word sense disambiguation, and logical inference. The dataset is modeled after open book exams, where students are allowed to consult a set of facts, but have to apply them to novel situations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OpenBookQA 是包含 5,957 个多项选择题的数据集，需要对 1,326 个基本科学事实和其他外部知识的集合进行推理。这些问题旨在测试对广泛主题和现象的理解，不能仅根据提供的事实进行解答。这些问题涵盖语言理解的各个方面，例如共指、词义消歧和逻辑推理。该数据集根据开卷考试建模，其中学生可以查阅一组事实，但必须将它们应用到新的情况中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.OpenBookQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OpenBookQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OpenBookQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.OpenBookQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/openbookqa",]A;"arxiv": "https://arxiv.org/abs/1809.02789"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/openbookqa",]A;"arxiv": "https://arxiv.org/abs/1809.02789"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Opusparcus.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{opusparcus-v1_en,]A;author={Mathias Creutz},]A;year={2018-01-01},]A;title={{Opusparcus: Open Subtitles Paraphrase Corpus for Six Languages (version 1.0)}},]A;publisher={Kielipankki},]A;type={data set},]A;url={http://urn.fi/urn:nbn:fi:lb-2018021221},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{opusparcus-v1_en,]A;author={Mathias Creutz},]A;year={2018-01-01},]A;title={{Opusparcus: Open Subtitles Paraphrase Corpus for Six Languages (version 1.0)}},]A;publisher={Kielipankki},]A;type={data set},]A;url={http://urn.fi/urn:nbn:fi:lb-2018021221},}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Opusparcus.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Opusparcus is a paraphrase corpus for six European language: German, English, Finnish, French, Russian, and Swedish. The paraphrases consist of subtitles from movies and TV shows.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Opusparcus 是以下六种欧洲语言的释义语料库: 德语、英语、芬兰语、法语、俄语和瑞典语。这些释义由电影和电视节目中的字幕组成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Opusparcus.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Opusparcus]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Opusparcus]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Opusparcus.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/GEM/opusparcus",]A;"webpage": "https://metashare.csc.fi/repository/browse/opusparcus-open-subtitles-paraphrase-corpus-for-six-languages-version-10/****************************************************************"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/GEM/opusparcus",]A;"webpage": "https://metashare.csc.fi/repository/browse/opusparcus-open-subtitles-paraphrase-corpus-for-six-languages-version-10/****************************************************************"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.PIQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{Bisk2020,]A;author = {Yonatan Bisk and Rowan Zellers and Ronan Le Bras and Jianfeng Gao and Yejin Choi},]A;title = {PIQA: Reasoning about Physical Commonsense in Natural Language},]A;booktitle = {Thirty-Fourth AAAI Conference on Artificial Intelligence},]A;year = {2020},]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{Bisk2020,]A;作者 = {Yonatan Bisk 和 Rowan Zellers 和 Ronan Le Bras 和 Jianfeng Gao 和 Yejin Choi},]A;标题 = {PIQA: 自然语言中物理常识的推理},]A;booktitle = {第三十四届 AAAI 人工智能会议},]A;年份 = {2020}]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.PIQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PIQA is a dataset of 16,000 multiple-choice questions that require physical commonsense reasoning to be answered correctly. The questions are inspired by instructables.com, a website that provides users with instructions on how to build, craft, bake, or manipulate objects using everyday materials. The questions focus on everyday situations with a preference for atypical solutions, and challenge state-of-the-art natural language understanding systems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PIQA 是包含 16,000 个多项选择题的数据集，需要物理常识推理才能正确回答。这些问题的灵感来自于 instructables.com，该网站为用户提供如何使用日常材料构建、制作、烘焙或操作物体的说明。这些问题侧重于日常情况，偏爱非典型解决方案，并挑战最先进的自然语言理解系统。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.PIQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PIQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PIQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.PIQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/piqa",]A;"arxiv": "https://arxiv.org/abs/1911.11641",]A;"webpage": "https://yonatanbisk.com/piqa/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/piqa",]A;"arxiv": "https://arxiv.org/abs/1911.11641",]A;“网页”: "https://yonatanbisk.com/piqa/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuAC.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{choi-etal-2018-quac,]A;title="{Q}u{AC}: Question Answering in Context",]A;author="Choi, Eunsol  and He, He andIyyer, Mohit  andYatskar, Mark  andYih, Wen-tau  andChoi, Yejin  andLiang, Percy  andZettlemoyer, Luke",]A;booktitle="Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing",]A;month=oct # "-" # nov,]A;year = "2018",]A;address="Brussels, Belgium",]A;publisher="Association for Computational Linguistics",]A;url="https://www.aclweb.org/anthology/D18-1241",]A;doi="10.18653/v1/D18-1241",]A;pages="2174--2184",]A;abstract="We present QuAC, a dataset for Question Answering in Context that contains 14K information-seeking QA dialogs (100K questions in total). The dialogs involve two crowd workers: (1) a student who poses a sequence of freeform questions to learn as much as possible about a hidden Wikipedia text, and (2) a teacher who answers the questions by providing short excerpts from the text. QuAC introduces challenges not found in existing machine comprehension datasets: its questions are often more open-ended, unanswerable, or only meaningful within the dialog context, as we show in a detailed qualitative evaluation. We also report results for a number of reference models, including a recently state-of-the-art reading comprehension architecture extended to model dialog context. Our best model underperforms humans by 20 F1, suggesting that there is significant room for future work on this data. Dataset, baseline, and leaderboard available at url http://quac.ai."}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{choi-etal-2018-quac,]A;标题="{Q}u{AC}: 上下文中的问答",]A;作者="Choi, Eunsol 和 He, He andIyyer, Mohit 和 Yatskar, Mark 和 Yih, Wen-tau 和 Choi, Yejin 和 Liang, Percy 和 Zettlemoyer, Luke",]A;booktitle="2018 年自然语言处理经验方法会议论文集",]A;月份=oct # "-" # nov,]A;年份 = "2018",]A;地址="比利时布鲁塞尔",]A;出版商="计算语言学协会",]A;url="https://www.aclweb.org/anthology/D18-1241",]A;doi="10.18653/v1/D18-1241",]A;pages="2174--2184",]A;摘要="我们展示了 QuAC，它是用于上下文中的问答的数据集，其中包含 14K 个信息查找 QA 对话(总共 100K 个问题)。对话涉及两名众包工作者: (1) 一名学生提出一系列自由格式问题，以尽可能了解隐藏的维基百科文本，(2) 一名教师提供文本中的简短摘录以回答问题。QuAC 带来了在现有计算机理解数据集中找不到的挑战: 其问题通常更开放、无法回答或仅在对话上下文中有意义，正如我们在详细的定性评估中所示。我们还报告了许多参考模型的结果，包括最近扩展到模型对话上下文的最先进的阅读理解架构。我们最佳模型的表现比人类低 20 F1，这表明未来在此数据上的工作还有很大的空间。数据集、基线和排行榜可在 URL http://quac.ai 上获取。"}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuAC.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question Answering in Context is a dataset for modeling, understanding, and participating in information seeking dialog. Data instances consist of an interactive dialog between two crowd workers: (1) a student who poses a sequence of freeform questions to learn as much as possible about a hidden Wikipedia text, and (2) a teacher who answers the questions by providing short excerpts (spans) from the text. QuAC introduces challenges not found in existing machine comprehension datasets: its questions are often more open-ended, unanswerable, or only meaningful within the dialog context.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上下文中的问答是数据集，用于建模、理解和参与信息查找对话。数据实例由两名众包工作者之间的交互式对话组成: (1) 一名学生提出一系列自由格式问题，以尽可能了解隐藏的维基百科文本，(2) 一名教师提供文本中的简短摘录(段落)以回答问题。QuAC 带来了在现有计算机理解数据集中找不到的挑战: 其问题通常更开放、无法回答或仅在对话上下文中有意义。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuAC.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[QuAC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[QuAC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuAC.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/quac",]A;"arxiv": "https://arxiv.org/abs/1808.07036",]A;"webpage": "https://quac.ai"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/quac",]A;"arxiv": "https://arxiv.org/abs/1808.07036",]A;“网页”: "https://quac.ai"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuoraRetrieval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuoraRetrieval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[QuoraRetrieval is based on questions that are marked as duplicates on the Quora platform. Given a question, find other (duplicate) questions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[QuoraRetrieval 基于在 Quora 平台上标记为重复的问题。给定问题后，查找其他(重复)问题。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuoraRetrieval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[QuoraRetrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[QuoraRetrieval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.QuoraRetrieval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/quora",]A;"webpage": "https://quoradata.quora.com/First-Quora-Dataset-Release-Question-Pairs"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/quora",]A;"webpage": "https://quoradata.quora.com/First-Quora-Dataset-Release-Question-Pairs"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SIQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{sap2019socialiqa,]A;title={SocialIQA: Commonsense Reasoning about Social Interactions},]A;author={Maarten Sap and Hannah Rashkin and Derek Chen and Ronan LeBras and Yejin Choi},]A;year={2019},]A;eprint={1904.09728},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{sap2019socialiqa,]A;title={SocialIQA: Commonsense Reasoning about Social Interactions},]A;author={Maarten Sap and Hannah Rashkin and Derek Chen and Ronan LeBras and Yejin Choi},]A;year={2019},]A;eprint={1904.09728},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SIQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We introduce Social IQa: Social Interaction QA, a new question-answering benchmark for testing social commonsense intelligence. Contrary to many prior benchmarks that focus on physical or taxonomic knowledge, Social IQa focuses on reasoning about people’s actions and their social implications. For example, given an action like "Jesse saw a concert" and a question like "Why did Jesse do this?", humans can easily infer that Jesse wanted "to see their favorite performer" or "to enjoy the music", and not "to see what's happening inside" or "to see if it works". The actions in Social IQa span a wide variety of social situations, and answer candidates contain both human-curated answers and adversarially-filtered machine-generated candidates. Social IQa contains over 37,000 QA pairs for evaluating models’ abilities to reason about the social implications of everyday events and situations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们引入了 Social IQa: 社交互动 QA，这是一种用于测试社交常识智力的新问答基准。与之前许多侧重于物理或分类知识的基准相反，Social IQa 侧重于推理人们的行为及其社会影响。例如，给出“Jesse 看了一场音乐会”这样的动作和“Jesse 为什么这样做?”这样的问题，人们可以很容易推断出 Jesse 想要“看最喜欢的表演者”或“享受音乐”，而不是“看看里面发生了什么”或“看看它是否有效”。 Social IQa 中的动作涉及各种社交场合，候选答案既包含人工策划的答案，也包含经过对抗过滤的、机器生成的候选答案。 Social IQa 包含超过 37,000 个 QA 对，用于评估模型推理日常活动和情况的社会影响的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SIQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SIQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SIQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SIQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/social_i_qa",]A;"arxiv": "https://arxiv.org/pdf/1904.09728.pdf",]A;"webpage": "https://leaderboard.allenai.org/socialiqa/submissions/get-started"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/social_i_qa",]A;"arxiv": "https://arxiv.org/pdf/1904.09728.pdf",]A;“网页”: "https://leaderboard.allenai.org/socialiqa/submissions/get-started"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.STSBenchmark.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text" UsrLk="true">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.STSBenchmark.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Semantic Textual Similarity Benchmark (STSbenchmark) dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[语义文本相似度基准(STSbenchmark)数据集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.STSBenchmark.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[STSBenchmark]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[STSBenchmark]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.STSBenchmark.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/stsbenchmark-sts"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/stsbenchmark-sts"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SciDocsRR.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{specter2020cohan,]A;title={SPECTER: Document-level Representation Learning using Citation-informed Transformers},]A;author={Arman Cohan and Sergey Feldman and Iz Beltagy and Doug Downey and Daniel S. Weld},]A;booktitle={ACL},]A;year={2020}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{specter2020cohan,]A;title={SPECTER: Document-level Representation Learning using Citation-informed Transformers},]A;author={Arman Cohan and Sergey Feldman and Iz Beltagy and Doug Downey and Daniel S. Weld},]A;booktitle={ACL},]A;year={2020}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SciDocsRR.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ranking of related scientific papers based on their title.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根据标题对相关科学论文进行排序。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SciDocsRR.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SciDocsRR]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SciDocsRR]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SciDocsRR.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/scidocs-reranking",]A;"webpage": "https://allenai.org/data/scidocs"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/scidocs-reranking",]A;"webpage": "https://allenai.org/data/scidocs"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{2016arXiv160605250R,]A;author = {{Rajpurkar}, Pranav and {Zhang}, Jian and {Lopyrev}, Konstantin and {Liang}, Percy},]A;title = "{SQuAD: 100,000+ Questions for Machine Comprehension of Text}",]A;journal = {arXiv e-prints},]A;year = 2016,]A;eid = {arXiv:1606.05250},]A;pages = {arXiv:1606.05250},]A;archivePrefix = {arXiv},]A;eprint = {1606.05250},}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{2016arXiv160605250R,]A;作者 = {{Rajpurkar}, Pranav 和 {Zhang}, Jian 和 {Lopyrev}, Konstantin 和 {Liang}, Percy},]A;标题 = "{SQuAD: 100,000+ 个文本机器理解问题}",]A;期刊 = {arXiv e-prints},]A;年份 = 2016,]A;eid = {arXiv:1606.05250},]A;pages = {arXiv:1606.05250},]A;archivePrefix = {arXiv},]A;eprint = {1606.05250},}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stanford Question Answering Dataset (SQuAD) is a reading comprehension dataset, consisting of questions posed by crowdworkers on a set of Wikipedia articles, where the answer to every question is a segment of text, or span, from the corresponding reading passage, or the question might be unanswerable.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[斯坦福问答数据集 (SQuAD) 是阅读理解数据集，由众包工作者对一组维基百科文章提出的问题组成，其中每个问题的答案是相应阅读段落中的一段文本或段落，否则问题可能无法回答。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[squad]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[squad]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/squad",]A;"arxiv": "https://arxiv.org/pdf/1606.05250.pdf",]A;"webpage": "https://rajpurkar.github.io/SQuAD-explorer"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/squad",]A;"arxiv": "https://arxiv.org/pdf/1606.05250.pdf",]A;“网页”: "https://rajpurkar.github.io/SQuAD-explorer"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad_v2.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@inproceedings{rajpurkar-etal-2018-know,]A;title = "Know What You Don{'}t Know: Unanswerable Questions for {SQ}u{AD}",]A;author = "Rajpurkar, Pranav and Jia, Robin and Liang, Percy",]A;editor = "Gurevych, Iryna  and Miyao, Yusuke",]A;booktitle = "Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers)",]A;month = "jul",]A;year = "2018",]A;address = "Melbourne, Australia",]A;publisher = "Association for Computational Linguistics",]A;url = "https://aclanthology.org/P18-2124",]A;doi = "10.18653/v1/P18-2124",]A;pages = "784--789",]A;eprint="1806.03822",]A;archivePrefix="arXiv",]A;primaryClass="cs.CL"]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@inproceedings{rajpurkar-etal-2018-know,]A;title = "了解你不知道的事情: {SQ}u{AD} 的无法解答的问题",]A;author = "Rajpurkar、Pranav 和 Jia、Robin 和 Liang、Percy",]A;editor = "Gurevych、Iryna 和 Miyao、Yusuke",]A;booktitle = "计算语言学协会第 56 届年会论文集(第 2 卷: 短论文)",]A;month = "7 月",]A;year = "2018 年",]A;address = "澳大利亚墨尔本",]A;publisher = "计算语言学协会",]A;url = "https://aclanthology.org/P18-2124",]A;doi = "10.18653/v1/P18-2124",]A;pages = "784--789",]A;eprint="1806.03822",]A;archivePrefix="arXiv",]A;primaryClass="cs.CL"]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad_v2.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stanford Question Answering Dataset (SQuAD) is a reading comprehension dataset, consisting of questions posed by crowdworkers on a set of Wikipedia articles, where the answer to every question is a segment of text, or span, from the corresponding reading passage, or the question might be unanswerable. SQuAD 2.0 combines the 100,000 questions in SQuAD1.1 with over 50,000 unanswerable questions written adversarially by crowdworkers to look similar to answerable ones. To do well on SQuAD2.0, systems must not only answer questions when possible, but also determine when no answer is supported by the paragraph and abstain from answering. We have used 20% of rows in validation rows sampled at random to compute the benchmark results.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[斯坦福问答数据集(SQuAD)是阅读理解数据集，由众包工作者对一组维基百科文章提出的问题组成，其中每个问题的答案是相应阅读段落中的一段文本或段落，否则问题可能无法回答。SQuAD 2.0 将 SQuAD1.1 中的 100,000 个问题与众包工作者对抗性编写的 50,000 多个无法回答的问题相结合，使其看起来与可回答的问题相似。为了在 SQuAD2.0 上表现出色，系统不仅必须尽可能回答问题，而且还必须确定何时段落不支持任何答案并放弃回答。我们使用了随机抽样的验证行中的 20% 来计算基准结果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad_v2.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[squad_v2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[squad_v2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Squad_v2.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/rajpurkar/squad_v2",]A;"arxiv": "https://arxiv.org/abs/1806.03822",]A;"webpage": "https://rajpurkar.github.io/SQuAD-explorer"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/rajpurkar/squad_v2",]A;"arxiv": "https://arxiv.org/abs/1806.03822",]A;"webpage": "https://rajpurkar.github.io/SQuAD-explorer"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SummEval.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{fabbri2020summeval,]A;title={SummEval: Re-evaluating Summarization Evaluation},]A;author={Fabbri, Alexander R and Kryscinski, Wojciech and McCann, Bryan and Xiong, Caiming and Socher, Richard and Radev, Dragomir},]A;journal={arXiv preprint arXiv:2007.12626},]A;year={2020}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{fabbri2020summeval,]A;title={SummEval: Re-evaluating Summarization Evaluation},]A;author={Fabbri, Alexander R and Kryscinski, Wojciech and McCann, Bryan and Xiong, Caiming and Socher, Richard and Radev, Dragomir},]A;journal={arXiv preprint arXiv:2007.12626},]A;year={2020}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SummEval.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News Article Summary Semantic Similarity Estimation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新闻文章摘要语义相似度估算。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SummEval.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[SummEval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[SummEval]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.SummEval.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/summeval"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/summeval"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Tatoeba.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{Artetxe_2019,]A;title={Massively Multilingual Sentence Embeddings for Zero-Shot Cross-Lingual Transfer and Beyond},]A;volume={7},]A;ISSN={2307-387X},]A;url={http://dx.doi.org/10.1162/tacl_a_00288},]A;DOI={10.1162/tacl_a_00288},]A;journal={Transactions of the Association for Computational Linguistics},]A;publisher={MIT Press - Journals},]A;author={Artetxe, Mikel and Schwenk, Holger},]A;year={2019},]A;month=nov, pages={597–610} }]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{Artetxe_2019,]A;title={Massively Multilingual Sentence Embeddings for Zero-Shot Cross-Lingual Transfer and Beyond},]A;volume={7},]A;ISSN={2307-387X},]A;url={http://dx.doi.org/10.1162/tacl_a_00288},]A;DOI={10.1162/tacl_a_00288},]A;journal={Transactions of the Association for Computational Linguistics},]A;publisher={MIT Press - Journals},]A;author={Artetxe, Mikel and Schwenk, Holger},]A;year={2019},]A;month=nov, pages={597–610} }]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Tatoeba.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[1,000 English-aligned sentence pairs for each language based on the Tatoeba corpus]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于 Tatoeba 语料库针对每种语言有 1,000 个英语对齐的句子对]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Tatoeba.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tatoeba]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Tatoeba]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.Tatoeba.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/tatoeba-bitext-mining",]A;"github": "https://github.com/facebookresearch/LASER/tree/main/data/tatoeba/v1"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/tatoeba-bitext-mining",]A;"github": "https://github.com/facebookresearch/LASER/tree/main/data/tatoeba/v1"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ToxicConversationsClassification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{jigsaw-unintended-bias-in-toxicity-classification,]A;author = {cjadams, Daniel Borkan, inversion, Jeffrey Sorensen, Lucas Dixon, Lucy Vasserman, nithum},]A;title = {Jigsaw Unintended Bias in Toxicity Classification},]A;publisher = {Kaggle},]A;year = {2019},]A;url = {https://kaggle.com/competitions/jigsaw-unintended-bias-in-toxicity-classification}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{jigsaw-unintended-bias-in-toxicity-classification,]A;author = {cjadams, Daniel Borkan, inversion, Jeffrey Sorensen, Lucas Dixon, Lucy Vasserman, nithum},]A;title = {Jigsaw Unintended Bias in Toxicity Classification},]A;publisher = {Kaggle},]A;year = {2019},]A;url = {https://kaggle.com/competitions/jigsaw-unintended-bias-in-toxicity-classification}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ToxicConversationsClassification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Collection of comments from the Civil Comments platform together with annotations if the comment is toxic or not.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[收集来自 Civil comments 平台的评论，以及注明评论是有害的注释。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ToxicConversationsClassification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ToxicConversationsClassification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ToxicConversationsClassification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.ToxicConversationsClassification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/toxic_conversations_50k",]A;"webpage": "https://www.kaggle.com/competitions/jigsaw-unintended-bias-in-toxicity-classification/overview"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/toxic_conversations_50k",]A;"webpage": "https://www.kaggle.com/competitions/jigsaw-unintended-bias-in-toxicity-classification/overview"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TriviaQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@article{2017arXivtriviaqa,]A;author = {{Joshi}, Mandar and {Choi}, Eunsol and {Weld}, Daniel and {Zettlemoyer}, Luke},]A;title = "{triviaqa: A Large Scale Distantly Supervised Challenge Dataset for Reading Comprehension}",]A;journal = {arXiv e-prints},]A;year = 2017,]A;eid = {arXiv:1705.03551},]A;pages = {arXiv:1705.03551},]A;archivePrefix = {arXiv},]A;eprint = {1705.03551},]A;}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@article{2017arXivtriviaqa,]A;作者 = {{Joshi}, Mandar 和 {Choi}, Eunsol 和 {Weld}, Daniel 和 {Zettlemoyer}, Luke},]A;标题 = "{triviaqa: 用于阅读理解的大规模远程监督挑战数据集}",]A;期刊 = {arXiv e-prints},]A;年份 = 2017,]A;eid = {arXiv:1705.03551},]A;pages = {arXiv:1705.03551},]A;archivePrefix = {arXiv},]A;eprint = {1705.03551},]A;}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TriviaQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TriviaqQA is a reading comprehension dataset containing over 650K question-answer-evidence triples. TriviaqQA includes 95K question-answer pairs authored by trivia enthusiasts and independently gathered evidence documents, six per question on average, that provide high quality distant supervision for answering the questions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TriviaqQA 是阅读理解数据集，包含超过 650K 个问答证据三元组。TriviaqQA 包括由知识爱好者创作的 95K 个问答对和独立收集的证据文档(平均每个问题 6 个)，为回答问题提供高质量的远程监督。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TriviaQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TriviaQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TriviaQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TriviaQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/mandarjoshi90/triviaqa",]A;"hugging_face": "https://huggingface.co/datasets/trivia_qa",]A;"webpage": "http://nlp.cs.washington.edu/triviaqa/"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/mandarjoshi90/triviaqa",]A;"hugging_face": "https://huggingface.co/datasets/trivia_qa",]A;“网页”: "http://nlp.cs.washington.edu/triviaqa/"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQA.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{lin2021truthfulqa,]A;title={TruthfulQA: Measuring How Models Mimic Human Falsehoods},]A;author={Stephanie Lin and Jacob Hilton and Owain Evans},]A;year={2021},]A;eprint={2109.07958},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{lin2021truthfulqa,]A;标题={TruthfulQA: 衡量模型如何模拟人类谎言},]A;作者={Stephanie Lin 和 Jacob Hilton 和 Owain Evans},]A;年份={2021},]A;eprint={2109.07958},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQA.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TruthfulQA is a benchmark to measure whether a language model is truthful in generating answers to questions. The benchmark comprises 817 questions that span 38 categories, including health, law, finance and politics. Questions are crafted so that some humans would answer falsely due to a false belief or misconception. To perform well, models must avoid generating false answers learned from imitating human texts.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TruthfulQA 是衡量语言模型在生成问题的答案时是否真实的基准。该基准包含 817 个问题，涉及 38 个类别，包括健康、法律、财务和政治。问题经过精心制作，使一些人类会由于错误信念或误解而做出错误的回答。为了表现良好，模型必须避免生成从模仿人类文本学到的错误答案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQA.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TruthfulQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TruthfulQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQA.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/sylinrl/TruthfulQA",]A;"hugging_face": "https://huggingface.co/datasets/truthful_qa",]A;"arxiv": "https://arxiv.org/abs/2109.07958"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/sylinrl/TruthfulQA",]A;"hugging_face": "https://huggingface.co/datasets/truthful_qa",]A;"arxiv": "https://arxiv.org/abs/2109.07958"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQATextGen.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{lin2021truthfulqa,]A;title={TruthfulQA: Measuring How Models Mimic Human Falsehoods},]A;author={Stephanie Lin and Jacob Hilton and Owain Evans},]A;year={2021},]A;eprint={2109.07958},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{lin2021truthfulqa,]A;标题={TruthfulQA: 衡量模型如何模拟人类谎言},]A;作者={Stephanie Lin 和 Jacob Hilton 和 Owain Evans},]A;年份={2021},]A;eprint={2109.07958},]A;archivePrefix={arXiv},]A;primaryClass={cs.CL}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQATextGen.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TruthfulQA is a benchmark to measure whether a language model is truthful in generating answers to questions. The benchmark comprises 817 questions that span 38 categories, including health, law, finance and politics. Questions are crafted so that some humans would answer falsely due to a false belief or misconception. To perform well, models must avoid generating false answers learned from imitating human texts.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TruthfulQA 是衡量语言模型在生成问题的答案时是否真实的基准。该基准包含 817 个问题，涉及 38 个类别，包括健康、法律、财务和政治。问题经过精心制作，使一些人类会由于错误信念或误解而做出错误的回答。为了表现良好，模型必须避免生成从模仿人类文本学到的错误答案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQATextGen.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TruthfulQA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TruthfulQA]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TruthfulQATextGen.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["github": "https://github.com/sylinrl/TruthfulQA",]A;"hugging_face": "https://huggingface.co/datasets/truthful_qa",]A;"arxiv": "https://arxiv.org/abs/2109.07958"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["github": "https://github.com/sylinrl/TruthfulQA",]A;"hugging_face": "https://huggingface.co/datasets/truthful_qa",]A;"arxiv": "https://arxiv.org/abs/2109.07958"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TweetSentimentExtractionClassification.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@misc{tweet-sentiment-extraction,]A;author = {Maggie, Phil Culliton, Wei Chen},]A;title = {Tweet Sentiment Extraction},]A;publisher = {Kaggle},]A;year = {2020},]A;url = {https://kaggle.com/competitions/tweet-sentiment-extraction}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@misc{tweet-sentiment-extraction,]A;author = {Maggie, Phil Culliton, Wei Chen},]A;title = {Tweet Sentiment Extraction},]A;publisher = {Kaggle},]A;year = {2020},]A;url = {https://kaggle.com/competitions/tweet-sentiment-extraction}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TweetSentimentExtractionClassification.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[With all of the tweets circulating every second it is hard to tell whether the sentiment behind a specific tweet will impact a company, or a person's, brand for being viral (positive), or devastate profit because it strikes a negative tone. Capturing sentiment in language is important in these times where decisions and reactions are created and updated in seconds.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由于所有的推文每秒都在传播，很难判断一条特定推文背后的情绪是否会因具有较强的感染性(积极)而影响公司或个人的品牌，或者因具有负面语气而带来损失。在这个决策与反应迅速且易变的时代，捕获语言中的情绪非常重要。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TweetSentimentExtractionClassification.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TweetSentimentExtractionClassification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TweetSentimentExtractionClassification]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.TweetSentimentExtractionClassification.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/tweet_sentiment_extraction",]A;"webpage": "https://www.kaggle.com/competitions/tweet-sentiment-extraction/overview"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/mteb/tweet_sentiment_extraction",]A;"webpage": "https://www.kaggle.com/competitions/tweet-sentiment-extraction/overview"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.WinoGrande.Citation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[@InProceedings{ai2:winogrande,]A;title = {WinoGrande: An Adversarial Winograd Schema Challenge at Scale},]A;authors={Keisuke, Sakaguchi and Ronan, Le Bras and Chandra, Bhagavatula and Yejin, Choi},]A;year={2019}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[@InProceedings{ai2:winogrande,]A;标题 = {WinoGrande: 大规模维诺格拉德模式挑战赛},]A;作者={Keisuke, Sakaguchi 和 Ronan, Le Bras 和 Chandra, Bhagavatula 和 Yejin, Choi},]A;年份={2019}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.WinoGrande.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[WinoGrande is a large-scale dataset of 44k problems, inspired by the original Winograd Schema Challenge (Levesque, Davis, and Morgenstern 2011), but adjusted to improve the scale and robustness against the dataset-specific bias. Formulated as a fill-in-a-blank task with binary options, the goal is to choose the right option for a given sentence which requires commonsense reasoning. The key steps of the dataset construction consist of (1) a carefully designed crowdsourcing procedure, followed by (2) systematic bias reduction using a novel AfLite algorithm that generalizes human-detectable word associations to machine-detectable embedding associations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[WinoGrande 是包含 44k 个问题的大型数据集，受原始威诺格拉德模式挑战(Levesque、Davis 和 Morgenstern 2011)的启发，但进行了调整以提高针对数据集特定偏差的规模和可靠度。制定为具有二元选项的填空任务，目标是为需要常识推理的给定句子选择正确的选项。数据集构造的关键步骤包括 (1) 精心设计的众包程序，(2) 使用新颖的 AfLite 算法系统地减少偏差，该算法将人类可检测的词汇联想推广为机器可检测的嵌入联想。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.WinoGrande.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[WinoGrande]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[WinoGrande]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.AllDatasetDetails.WinoGrande.Links" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/winogrande",]A;"arxiv": "https://arxiv.org/abs/1907.10641",]A;"webpage": "https://leaderboard.allenai.org/winogrande/submissions/get-started"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA["hugging_face": "https://huggingface.co/datasets/winogrande",]A;"arxiv": "https://arxiv.org/abs/1907.10641",]A;“网页”: "https://leaderboard.allenai.org/winogrande/submissions/get-started"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.DatasetCitation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Citation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[引文]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.DatasetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.DatasetLinks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Links]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[链接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.DatasetName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DatasetDetails.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据集详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployNotSupported" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments are not enabled for this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型未启用部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployNotSupportedWithoutRegistration" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registration is required to use this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要注册才能使用此模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployOrFinetuneModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy or fine-tune model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署或微调模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy or finetune model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentBlockedByAdminPolicy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model cannot be deployed due to a policy in your subscription. Contact your admin to request access.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由于订阅中的策略，无法部署此模型。请与管理员联系以请求访问权限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deployments" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentsTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI model deployments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 模型部署]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service model deployments]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DirectFromAzureDisabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Direct from Azure models can only be deployed through AI Foundry.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仅可通过 AI Foundry 部署直接来自 Azure 的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.Close" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.InstallDependencies" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[1. Install dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[1. 安装依赖项]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.InstallIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install Azure Identity:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装 Azure 标识:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.InstallSDK" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the SDK using pip:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 pip 安装 SDK:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.RunCode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[2. Run code to download the model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[2. 运行代码以下载模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadDialog.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownloadModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FinetuneInvalidRegion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure model fine-tune is not available in your workspace's region. Supported regions include: {{region}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 模型微调在你的工作区所在区域中不可用。支持的区域包括: {{region}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型准确度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Average" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型流畅性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型基准]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.ModelNames" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model names]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model relevance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型相关性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Score" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ChartTitles.Similarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型相似性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准确度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Bitext_mining" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bitext mining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[双语文本挖掘]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Clustering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聚类分析]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Coherence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Dataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Fluency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流利度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Groundedness" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Groundedness]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根基性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Model" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[型号]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.ModelVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Pair_classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pair classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配对分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Primary metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[主要指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Relevance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相关性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Reranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新排序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Result" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Result]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[结果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Retrieval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[检索]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Similarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相似度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Sts" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[STS]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[STS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[汇总]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Columns.Task" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.Common.Done" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Done]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.Accuracy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准确度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.F1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1 score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.MeanAveragePrecision" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MAP]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[地图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.NdcgAt10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NDCG at 10]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NDCG@10]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.Precision" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Average precision]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[平均精度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.SpearmanCorrelation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Spearman]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.EmbeddingsMetricDetails.VMeasure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 度量值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.LeaderboardListViewBase.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.LeaderboardListViewBase.MetricScatterChartPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There seems to be nothing here. Adjust filter configuration to view metric distribution.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此处似乎没有任何内容。调整筛选器配置以查看指标分布。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Accuracy.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We present accuracy scores at the dataset and the model levels. At the dataset level, the score is the average value of an accuracy metric computed over all examples in the dataset. The accuracy metric used is exact-match in all cases except for the HumanEval dataset which uses a pass@1 metric. Exact match simply compares model generated text with the correct answer according to the dataset, reporting one if the generated text matches the answer exactly and zero otherwise. Pass@1 measures the proportion of model solutions that pass a set of unit tests in a code generation task. At the model level, the accuracy score is the average of the dataset-level accuracies for each model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们在数据集和模型级别显示准确度分数。在数据集级别，该分数是对数据集中所有示例计算的准确度指标的平均值。除了使用 pass@1指标的 HumanEval 数据集外，使用的准确度指标在所有情况下都完全匹配。完全匹配只是根据数据集将模型生成的文本与正确答案进行比较，如果生成的文本与答案完全匹配，则报告 1；否则，报告 0。Pass@1 度量在代码生成任务中通过一组单元测试的模型解决方案的比例。在模型级别，准确度分数是每个模型的数据集级别准确度的平均值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Accuracy.Question" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What is accuracy?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[什么是准确度?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Accuracy.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy scores are presented at the dataset and the model levels. At the dataset level, the score is the average value of an accuracy metric computed over all examples in the dataset. {1} about accuracy.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准确度得分在数据集和模型级别上显示。在数据集级别，分数是针对数据集中所有示例计算的准确度指标的平均值。{1} 准确性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Accuracy.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准确度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Back" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Back to select metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[返回以选择指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.BiTextMining.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inputs are two sets of sentences from two different languages. For each sentence in the first set, the best match in the second set needs to be found. The matches are commonly translations. The provided model is used to embed each sentence and the closest pairs are found via cosine similarity. F1 serves as the main metric for bitext mining. Accuracy, precision and recall are also computed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入是两种不同语言的两组句子。对于第一组中的每个句子，都需要找到第二组中的最佳匹配。匹配项通常是翻译内容。提供的模型用于嵌入每个句子，并将通过余弦相似度找到最接近的句子对。F1 将用作位双语文本挖掘的主要指标。还会计算准确度、精准率和召回率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.BiTextMining.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[F1 Score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.BiTextMining.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The F1 score is the weighted mean of the precision and recall, where an F1 score reaches its best value at 1 (perfect precision and recall) and worst at 0.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[F1 分数是精准率和召回率的加权平均值，其中 F1 分数的最佳值为 1 (完美精准率和召回率)，最差值为 0。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.BiTextMining.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BiTextMining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BiTextMining]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Classification.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A train and test set are embedded with the provided model. The train set embeddings are used to train a logistic regression classifier with 100 maximum iterations, which is scored on the test set. The main metric is accuracy with average precision and F1 additionally provided.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[训练和测试集将与提供的模型一起嵌入。训练集嵌入用于训练最大迭代次数为 100 次的逻辑回归分类器，该分类器在测试集上进行评分。主要指标是具有平均精准率的准确度和额外提供的 F1。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Classification.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准确度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Classification.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Accuracy measures how often a machine learning model correctly predicts the outcome. It is calculated by dividing the number of correct predictions by the total number of predictions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准确度衡量的是机器学习模型正确预测结果的频率。它的计算方式为，用正确预测的数量除以预测的总数。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Classification.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Clustering.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Given a set of sentences or paragraphs, the goal is to group them into meaningful clusters. A mini-batch k-means model with batch size 32 and k equal to the number of different labels (Pedregosa et al., 2011) is trained on the embedded texts. The model is scored using v-measure (Rosenberg and Hirschberg, 2007). Vmeasure does not depend on the cluster label, thus the permutation of labels does not affect the score.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[给定一组句子或段落，目标是将它们分组为有意义的聚类。批大小为 32 且 k 等于不同标签数量的微型批处理 k 均值模型(Pedregosa 等人，2011 年)将在嵌入的文本上进行训练。模型将使用 v 度量值进行评分(Hirschberg 和 Hirschberg，2007 年)。V 度量值不依赖于聚类标签，因此标签的排列不会影响分数。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Clustering.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 度量值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Clustering.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[V-measure is a metric used to evaluate the quality of clustering. It is calculated as the harmonic mean of homogeneity (each cluster contains only members of a single class) and completeness (all members of a given class are assigned to the same cluster), ensuring a balance between the two for a meaningful score. Possible score lies between 0 and 1. Score of 1 stands for perfectly complete labeling.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[V 度量值是用于评估聚类分析质量的指标。它计算为同质性(每个聚类仅包含单个类的成员)和完整性(给定类的所有成员都分配给同一个聚类)的调和均值，从而确保两者之间的平衡以获得有意义的分数。可能的分数介于 0 和 1 之间。分数 1 代表完全完整的标记。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Clustering.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聚类分析]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.CalculationAnswer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence metric is calculated by asking another GPT model to evaluate on the output of your model using the below prompt:]A;]A;System:]A;You are an AI assistant. You will be given the definition of an evaluation metric for assessing the quality of an answer in a question-answering task. Your job is to compute an accurate evaluation score using the provided evaluation metric.]A;]A;User:]A;Coherence of an answer is measured by how well all the sentences fit together and sound naturally as a whole. Consider the overall quality of the answer when evaluating coherence. Given the question and answer, score the coherence of answer between one to five stars using the following rating scale:]A;One star: the answer completely lacks coherence]A;Two stars: the answer mostly lacks coherence]A;Three stars: the answer is partially coherent]A;Four stars: the answer is mostly coherent]A;Five stars: the answer has perfect coherency]A;]A;This rating value should always be an integer between 1 and 5. So the rating produced should be 1 or 2 or 3 or 4 or 5.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过要求另一个 GPT 模型使用以下提示对模型的输出进行评估来计算一致性指标:]A;]A;系统:]A;你是 AI 助手。你将获得评估指标的定义，用于评估问题回答任务中答案的质量。你的作业是使用提供的评估指标计算准确的评估分数。]A;]A;用户:]A;答案的一致性通过所有句子的适应程度和整体自然的声音来度量。在评估并行时考虑答案的总体质量。根据问题和答案，使用以下分级(1 到 5 星)对答案的一致性评分:]A;1 星:答案完全缺少一致性]A;2 星:答案大多缺少一致性]A;3 星:答案部分一致]A;4 星:答案大多一致]A;5 星:答案一致性完美]A;]A;此分级值应始终为介于 1 和 5 之间的整数。因此生成的分级应为 1、2、3、4 或 5。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.CalculationQuestion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How is our coherence score calculated?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如何计算我们的一致性分数?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence evaluates how well the language model can produce output that flows smoothly, reads naturally, and resembles human-like language.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性度量语言模型能产生流利性、自然阅读以及类似于人类语言的输出的效果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.Question" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What is coherence?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[什么是一致性?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence evaluates how well the language model can produce output that flows smoothly, reads naturally, and resembles human-like language. {1} about coherence.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性度量语言模型能产生流利性、自然阅读以及类似于人类语言的输出的效果。{1}关于一致性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Coherence.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coherence]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一致性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Ellipsis" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[…]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[…]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.CalculationAnswer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency metric is calculated by asking another GPT model to evaluate on the output of your model using the below prompt:]A;]A;System:]A;You are an AI assistant. You will be given the definition of an evaluation metric for assessing the quality of an answer in a question-answering task. Your job is to compute an accurate evaluation score using the provided evaluation metric.]A;]A;User:]A;Fluency measures the quality of individual sentences in the answer, and whether they are well-written and grammatically correct. Consider the quality of individual sentences when evaluating fluency. Given the question and answer, score the fluency of the answer between one to five stars using the following rating scale:]A;One star: the answer completely lacks fluency]A;Two stars: the answer mostly lacks fluency]A;Three stars: the answer is partially fluent]A;Four stars: the answer is mostly fluent]A;Five stars: the answer has perfect fluency]A;]A;This rating value should always be an integer between 1 and 5. So the rating produced should be 1 or 2 or 3 or 4 or 5.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过要求另一个 GPT 模型使用以下提示对模型的输出进行评估来计算流利度指标:]A;]A;系统:]A;你是 AI 助手。你将获得评估指标的定义，用于评估问题回答任务中答案的质量。你的作业是使用提供的评估指标计算准确的评估分数。]A;]A;用户:]A;流利度用于度量答案中单个句子的质量，以及这些句子是否编写得当且语法正确。在计算流利度时考虑单个句子的质量。根据问题和答案，使用以下分级(1 到 5 星)对答案的流利度评分:]A;1 星:答案完全缺少流利度]A;2 星:答案大多数缺少流利度]A;3 星:答案部分流利]A;4 星:答案大多流利]A;5 星:答案流利度完美]A;]A;此分级值应始终为介于 1 和 5 之间的整数。因此生成的分级应为 1、2、3、4 或 5。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.CalculationQuestion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How is our fluency score calculated?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们的流利度分数是如何计算的?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency evaluates the language proficiency of a generative AI's predicted answer. It assesses how well the generated text adheres to grammatical rules, syntactic structures, and appropriate usage of vocabulary, resulting in linguistically correct and natural-sounding responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流畅性可评估生成 AI 预测答案的语言熟练程度。它评估生成的文本如何遵循语法规则、语法结构以及词汇的适当用法，从而产生语言正确且自然发音的响应。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.Question" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What is fluency?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[什么是流利度?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency measures the language proficiency of a generative AI's predicted answer. {1} about fluency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测量生成式 AI 预测答案的语言熟练程度。{1}关于 fluency。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Fluency.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fluency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流利度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.CalculationAnswer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity metric is calculated by asking another GPT model to evaluate on the output of your model using the below prompt:]A;]A;System:]A;You are an AI assistant. You will be given the definition of an evaluation metric for assessing the quality of an answer in a question-answering task. Your job is to compute an accurate evaluation score using the provided evaluation metric.]A;]A;User:]A;Equivalence, as a metric, measures the similarity between the predicted answer and the correct answer. If the information and content in the predicted answer is similar or equivalent to the correct answer, then the value of the Equivalence metric should be high, else it should be low. Given the question, correct answer, and predicted answer, determine the value of Equivalence metric using the following rating scale:]A;One star: the predicted answer is not at all similar to the correct answer]A;Two stars: the predicted answer is mostly not similar to the correct answer]A;Three stars: the predicted answer is somewhat similar to the correct answer]A;Four stars: the predicted answer is mostly similar to the correct answer]A;Five stars: the predicted answer is completely similar to the correct answer]A;]A;This rating value should always be an integer between 1 and 5. So the rating produced should be 1 or 2 or 3 or 4 or 5.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相似度指标是通过使用以下提示要求另一个 GPT 模型对模型的输出进行评估来计算的:]A;]A;系统:]A;你是一个 AI 助手。你将获得评估指标的定义，用于评估问答任务中答案的质量。你的工作是使用提供的评估指标计算准确的评估分数。]A;]A;用户:]A;“等效性”作为一个指标衡量预测答案与正确答案之间的相似度。如果预测答案中的信息和内容与正确答案类似或等效，则“等效性”指标的值应较高，否则应较低。给定问题、正确答案和预测答案后，使用以下等级量表确定“等效性”指标值:]A;一星: 预测答案与正确答案完全不相似]A;两星: 预测答案与正确答案大多不相似]A;三星: 预测答案与正确答案有些相似]A;四星: 预测答案与正确答案大部分相似]A;五星: 预测答案与正确答案完全相似]A;]A;此分级值应始终为介于 1 和 5 之间的整数。因此，生成的分级应为 1、2、3、4 或 5。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.CalculationQuestion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How is our similarity score calculated?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如何计算相似度分数?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity is a measure that quantifies the similarity between a ground truth sentence (or document) and the prediction sentence generated by an AI model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相似度是一种度量标准，用于量化基本事实句子(或文档)与 AI 模型生成的预测句之间的相似度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.Question" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What is similarity?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[什么是相似度?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity measures the similarity between a source data (ground truth) sentence and the generated response by a GPT-based AI model. {1} about similarity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测量源数据(实况)句子与基于 GPT 的 AI 模型生成的响应之间的相似性。{1}关于相似性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.GPTSimilarity.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相似度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.MetricDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指标说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Overview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Overview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[概述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PairClassification.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A pair of text inputs is provided and a label needs to be assigned. Labels are typically binary variables denoting duplicate or paraphrase pairs. The two texts are embedded and their distance is computed with various metrics (cosine similarity, dot product, euclidean distance, manhattan distance). Using the best binary threshold accuracy, average precision, f1, precision and recall are computed. The average precision score based on cosine similarity is the main metric.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供了一对文本输入，且需要分配标签。标签通常是表示重复或释义对的二进制变量。系统将嵌入这两段文本，并使用各种指标(余弦相似度、点积、欧氏距离、曼哈顿距离)计算它们之间的距离。使用最佳二进制阈值准确度，计算平均精准率、f1、精准率和召回率。基于余弦相似度的平均精准率分数是主要指标。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PairClassification.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Precision]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[精准率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PairClassification.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Precision measures the model's ability to identify instances of a particular class correctly.Precision shows how often an ML model is correct when predicting the target class. Pros: Works well with imbalanced classes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[精准率衡量的是模型正确标识特定类的实例的能力。精准率将显示 ML 模型在预测目标类时正确的频率。优点: 适用于不均衡的类。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PairClassification.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pair Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配对分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Primary Metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[主要指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Reranking.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inputs are a query and a list of relevant and irrelevant reference texts. The aim is to rank the results according to their relevance to the query. The model is used to embed the references which are then compared to the query using cosine similarity. The resulting ranking is scored for each query and averaged across all queries. Metrics are mean MRR@k and MAP with the latter being the main metric.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入是一个查询，亦是一个包含相关和不相关的引用文本的列表。目的是根据结果与查询的相关性对结果进行排序。该模型用于嵌入引用，然后使用余弦相似度将引用与查询进行比较。生成的排序将针对每个查询进行评分，并针对所有查询求平均值。指标是均值 MRR@k 和 MAP，且后者是主要指标。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Reranking.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MAP]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[地图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Reranking.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mean Average Precision (MAP) evaluates the quality of ranking and recommender systems. It measures both the relevance of suggested items and how good the system is at placing more relevant items at the top. Values can range from 0 to 1. The higher the MAP, the better the system can place relevant items high in the list.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[均值平均精准率(MAP)将评估排序和推荐系统的质量。它将同时衡量建议项的相关性，以及系统在将更多相关项放在顶部方面的表现。值的范围可以是 0 到 1。MAP 越高，系统就越能将相关项放在列表的顶部。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Reranking.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新排序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Retrieval.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Each dataset consists of a corpus, queries and a mapping for each query to relevant documents from the corpus. The aim is to find these relevant documents. The provided model is used to embed all queries and all corpus documents and similarity scores are computed using cosine similarity. After ranking the corpus documents for each query based on the scores, nDCG@k, MRR@k, MAP@k, precision@k and recall@k are computed for several values of k. nDCG@10 serves as the main metric. MTEB reuses datasets and evaluation from BEIR (Thakur et al., 2021).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每个数据集都包含一个语料库、多个查询和一个映射，后者用于将每个查询映射到语料库中的相关文档。目的是查找这些相关文档。提供的模型用于嵌入所有查询和所有语料库文档，并且相似度分数使用余弦相似度进行计算。根据分数对每个查询的语料库文档进行排序后，将计算 nDCG@k、MRR@k、MAP@k、precision@k 和recall@k 以获得多个 k 值。nDCG@10 将用作主要指标。MTEB 将重复使用 BEIR 中的数据集和评估(Thakur 等人，2021 年)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Retrieval.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NDCG at K=10]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[K=10 时的 NDCG]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Retrieval.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Normalized Discounted Cumulative Gain (NDCG) evaluates the quality of information retrieval systems. NDCG helps measure a machine learning algorithm's ability to sort items based on relevance. It compares rankings to an ideal order where all relevant items are at the top of the list. K is a user-assigned parameter that defines the cutoff point (list length) while evaluating ranking quality. For example, if k=10, ndcg_at_10, will look at top-10 items.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[归一化折损累计增益(NDCG)将评估信息检索系统的质量。NDCG 可帮助衡量机器学习算法根据相关性对各项进行排序的能力。它会将排序与所有相关项均位于列表顶部的理想顺序进行比较。K 是用户分配的参数，用于定义评估排序质量时的截止点(列表长度)。例如，如果 k=10 (ndcg_at_10)，将查看前 10 项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Retrieval.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[检索]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.STS.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Given a sentence pair the aim is to determine their similarity. Labels are continuous scores with higher numbers indicating more similar sentences. The provided model is used to embed the sentences and their similarity is computed using various distance metrics. Distances are benchmarked with ground truth similarities using Pearson and Spearman correlations. Spearman correlation based on cosine similarity serves as the main metric.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[给定一个句子对，目的是确定其相似度。标签是连续分数，数字越高表示句子越相似。提供的模型用于嵌入句子，并使用各种距离指标计算其相似度。距离将使用皮尔逊和斯皮尔曼相关性以基本事实相似度为基准来进行度量。基于余弦相似度的斯皮尔曼相关性将用作主要指标。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.STS.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[斯皮尔曼等级相关系数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.STS.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation based on cosine similarity is the main metric for STS (Semantic Textual Similarity) and Summarization Embedding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于余弦相似度的斯皮尔曼相关性是 STS (语义文本相似度)和摘要嵌入任务的主要指标。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.STS.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Semantic Textual Similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[语义文本相似度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.SeeMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See more.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更多。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Summarization.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A set of human-written and machine-generated summaries are provided. The aim is to score the machine summaries. The provided model is first used to embed all summaries. For each machine summary embedding, distances to all human summary embeddings are computed. The closest score (e.g. highest cosine similarity) is kept and used as the model’s score of a single machine-generated summary. Pearson and Spearman correlations with ground truth human assessments of the machine-generated summaries are computed. Like for STS, Spearman correlation based on cosine similarity serves as the main metric. (Reimers et al., 2016).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供了一组人工编写和计算机生成的摘要。目标是对计算机摘要进行评分。提供的模型首先用于嵌入所有摘要。对于每个计算机摘要嵌入，将计算与所有人工摘要嵌入的距离。最接近的分数(例如最高的余弦相似度)将保留并用作单个计算机生成的摘要的模型分数。计算了计算机生成的摘要的基本事实人工评估的皮尔逊和斯皮尔曼相关性。与 STS 类似，基于余弦相似度的斯皮尔曼相关性将用作主要指标。(Reimers 等人，2016 年)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Summarization.PrimaryMetric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[斯皮尔曼等级相关系数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Summarization.ShortDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Spearman correlation based on cosine similarity is the main metric for STS (Semantic Textual Similarity) and Summarization Embedding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于余弦相似度的斯皮尔曼相关性是 STS (语义文本相似度)和摘要嵌入任务的主要指标。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Summarization.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[汇总]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.TaskDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[任务描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.MetricsDetails.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about available metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解可用指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ModelDisplayName.ToggleAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Toggle to collapse and expand model name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[切换以折叠和展开模型名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.NoDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No Dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无数据集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Dataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.DatasetScore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dataset score]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据集分数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Footer.SecondaryButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Done]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Metric" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metric]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Model" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[型号]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.NShots" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[N-Shots]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[N-快照]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.EvaluationData.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.FewShotData.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Few shot data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[少样本数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.Ratio" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ratio]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[纵横比]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.Style" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Style]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[样式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Sampling.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sampling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[采样]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.SubTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about how these metrics were generated on a specific dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解如何在特定数据集上生成这些指标。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Tasks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Leaderboard.ScoreDetails.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Score details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分数详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LlamaFinetuneInvalidVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model version is not available for fine-tune. Choose a newer version than {version}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型版本不可用于微调。选择高于 {version} 的版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.FineTune.Cta" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscribe and train]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[订阅和训练]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.FineTune.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customize the pre-trained model to understand your data to generate more accurate predictions with fine-tuned models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义预先训练的模型以了解数据，以使用细化模型生成更准确的预测。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Customize the pretrained model to understand your data to generate more accurate predictions with finetuned models.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.FineTune.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune with your data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用数据进行微调]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Finetune with your data]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.Cta" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscribe and deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[订阅和部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provision an inference API within seconds through Models as a Service. Explore the model in the playground. Pay only for tokens consumed. Fine-tune the model using hosted fine-tuning, without the need to create and manage compute.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过模型即服务，只需几秒钟即可预配推理 API。在操场中探索模型。仅为所使用的令牌付费。使用托管微调对模型进行微调，无需创建和管理计算。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Provision an inference API within seconds through Models as a Service. Explore the model in the playground. Pay only for tokens consumed. Fine-tune the model using hosted fine-tuning, without needing to create and manage compute.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.DescriptionNoFT" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provision an inference API within seconds through Models as a Service. Explore the model in the playground. Pay only for tokens consumed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过模型即服务，只需几秒钟即可预配推理 API。在操场中探索模型。仅为所使用的令牌付费。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go inference APIs and Hosted Fine-tuning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即用即付推理 API 和托管微调]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Paygo inference APIs and Hosted Fine-tuning]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.PayGo.TitleNoFT" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless APIs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无服务器 API]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Pay-as-you-go inference APIs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.Pricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[定价]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.AdCards.SignUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign-up for the wait list]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注册等待列表]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.CTA.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quickly deploy fine-tune models with your own data, without managing any infrastructure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用自己的数据快速部署微调模型，而无需管理任何基础结构。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.CTA.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go plan]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即用即付计划]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.DeployDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy the model using pay-as-you-go managed service]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用即用即付托管服务部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.FinetuneAsPlatformDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune the model using compute from your workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用工作区的计算微调模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.FinetuneAsPlatformTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微调]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.FinetuneDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune the model using pay-as-you-go managed service]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用即用即付托管服务微调模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaS.PayAsYouGo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即用即付]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaaSNotSupported" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless deployments are not enabled for this model in this Azure Cloud region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此 Azure 云区域中没有为此模型启用无服务器部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MetricDetails.MetricDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MetricDetails.MetricName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelID" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelIDTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reference this model ID when deploying the model in code]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在代码中部署模型时引用此模型 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.BrowseLeaderboards" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse leaderboards]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.Cost" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.ModelLeaderboard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model leaderboards]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型排行榜]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.ModelLeaderboardDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See what models are performing best in different criteria. {link} about our scoring methodology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解哪些模型在不同条件下表现最佳。关于评分方法的 {link}。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[See what models are performing best in different criteria.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.Quality" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.Safety" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Safety]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ModelLeaderboard.Throughput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Throughput]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[吞吐量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NotesHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More details from the provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[来自提供者的更多详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Benchmarks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基准]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.DownloadButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.ErrorResponseMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unknown error downloading blob.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载 blob 时出现未知错误。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.ErrorResponseTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[错误]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.LoadArtifactGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while loading artifacts]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加载项目时出错]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Artifacts.ScheduleZipGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while sending zip schedule request]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发送压缩计划请求时出错]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.AttributesHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Attributes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[属性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.DataDriftHeader_Preview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data drift detector (Preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据偏移检测器(预览)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Datasets.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[datasets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Datasets.NoDatasetsForModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No datasets are associated with this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有与此模型关联的数据集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.ConfirmButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Proceed to workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[前往工作区]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.DeploymentStartedSuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{deploymentName} deployment started successfully]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功启动 {deploymentName} 部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.NoWorkspaces" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are no workspaces in this subscription you have access to or that are in the registry region list.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此订阅中没有你有权访问或位于注册表区域列表中的工作区。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.Project.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Projects are easy-to-manage containers for your work—and the key to collaboration, organization, and connecting data and other services.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[项目是适合你的工作的易于管理的容器，是展开协作、进行管理和连接数据和其他服务的关键。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.Project.SelectCreateProject" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select or create a project]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择或创建项目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.Project.SelectCreateProject1RP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a project to work with]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择要处理的项目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.SelectWorkspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择工作区]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.SpecifyWorkspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specify the workspace where you want to deploy this model. Your last used workspace is selected by default.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指定要在其中部署此模型的工作区。默认情况下会选择上次使用的工作区。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Deploy.SuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Success]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Endpoints.DeployModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Endpoints.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Endpoints.NoEndpointsMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This model is not deployed to any endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型未部署到任何终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.ModelNotFoundError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model was not found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.NoSubscriptionError.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you recently created a subscription, it may take a few minutes to appear here. {link}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果最近创建了订阅，则可能需要几分钟才可在此处显示。{link}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.NoSubscriptionError.Link" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a new subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建新订阅]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.NoSubscriptionError.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No active subscriptions are found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到活动订阅]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Errors.UpdatingModelGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while updating the model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新模型时出错]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.ArchiveDialog" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Archiving this asset will hide it by default from list queries. You can still continue to reference and use an archived model in your workflows.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[默认情况下，存档此资产会将其从列表查询中隐藏。仍可继续在工作流中引用和使用已存档模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployAOAIMenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy the model using an Azure OpenAI resource]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure OpenAI 资源部署模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy the model using an Azure OpenAI Service resource]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployAOAIMenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV1MenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy to a web service (only for models based on frameworks)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署到 Web 服务(仅适用于基于框架的模型)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV1MenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Web service]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Web 服务]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy to web service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2BatchMenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy the model using the batch endpoint wizard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用批处理终结点向导部署模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy the model using the new batch endpoint wizard]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2BatchMenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批处理终结点]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy to batch endpoint]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2MenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy the model using the real-time endpoint wizard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用实时终结点向导部署模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy the model using the new real-time endpoint wizard]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2MenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Real-time endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实时终结点]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy to real-time endpoint]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2QuickMenuItemDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy a model with preconfigured parameters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用预配置参数部署模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deploy an MLflow model in one step with preconfigured parameters]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2QuickMenuItemDescriptionMLflow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy an MLflow model in one step with preconfigured parameters]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用预配置参数在一个步骤中部署 MLflow 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.DeployV2QuickMenuItemTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Real-time endpoint (quick)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实时终结点(快速)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.FilterLatestVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show latest versions only]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仅显示最新版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.ItemType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[model version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.List.ViewArchived" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Include archived]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包括已存档]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.HourlyBased" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hourly-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于每小时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.MeterRateHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rate per GPU per hour]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每小时每个 GPU 的速率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.SurchargeInfo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Surcharge amount depends on compute configuration. Azure Compute charges also apply. The deployment runs on-demand and is billed hourly. To stop the billing, you must delete the deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[附加费额取决于计算配置。Azure 计算费用也适用。部署将按需运行并按小时计费。要停止计费，必须删除该部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.SurchargeMeterHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Surcharge on the managed compute deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[托管计算部署的附加费]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaPSurchargePricing.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[定价]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.BaseModelInference" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Base model inference]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本模型推理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.InputTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input (per 1k token)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入(每 1000 个令牌)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.MaaSPaygoHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless API pay-as-you-go]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无服务器 API 即用即付]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.NotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[N/A]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[N/A]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.OutputTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output (per 1k token)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出(每 1000 个令牌)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.MaaSPricing.TokenBased" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于令牌]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Delete.ModelDeleteSuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model {modelName} deleted successfully]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功删除模型 {modelName}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Delete.ModelDeleteWarningMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model {modelName} can not be deleted because it is currently being used in one or more services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法删除模型 {modelName}，因为当前正在一个或多个服务中使用该模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Delete.ModelDeletedSuccessMessageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Success]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Delete.ModelDeletedWarningMessageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Warning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[警告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Description.EditLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Description.EditPanelTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit model description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑模型说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.AlertEmailAddresses" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Alert email addresses]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[警报电子邮件地址]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.ComputeTarget" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute target]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算目标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.DriftThreshold" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drift threshold]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[偏移阈值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.Features" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.Frequency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Frequency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[频率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.Interval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Interval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[间隔]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.ScoringEndpoints" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Scoring endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评分终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.StartDate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start date]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开始日期]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.DriftProperties.State" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[State]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[状态]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Errors.DeletingModelGenericError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while deleting the model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除模型时出错]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.ColumnNames.CreatedOn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.ColumnNames.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.ColumnNames.PredictionType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prediction type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[预测类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.ColumnNames.SensitiveFeatures" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sensitive features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[敏感特征]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model fairness assessments]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型公平性评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.HelpText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To create a new fairness assessment see the {link}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要创建新的公平性评估，请参阅 {link}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.HelpTextLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[fairness documentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公平性文档]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Fairness.NoFairnessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No model fairness assessments associated with this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有与此模型相关联的模型公平性评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.HiddenLayerScanned.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HiddenLayer Model Scanner did not detect any vulnerabilities, embedded code or integrity issues with the model artifacts.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HiddenLayer 模型扫描程序未检测到模型项目存在任何漏洞、嵌入代码或完整性问题。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[HiddenLayer Model Scanner identifies cybersecurity risks in AI models. A SAFE result from the scanner indicates no vulnerabilities, embedded code, or integrity issues.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.HiddenLayerScanned.ReadMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read More]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[阅读更多]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.HiddenLayerScanned.ReadMoreDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{learnMoreLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{learnMoreLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.HiddenLayerScanned.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Verified by HiddenLayer Model Scanner]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 HiddenLayer 模型扫描程序验证]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Scanned and attested SAFE by HiddenLayer]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.List.DeleteButtonName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.List.DeleteButtonNoPermission" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have the proper role based access to delete a model. Missing permission: {permission}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你没有删除模型的基于角色的合适权限。缺少权限: {permission}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.List.DeleteDialogItemType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.List.DeleteDialogText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting models is a permanent action that cannot be undone. Are you sure you wish to proceed?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除模型是永久性操作，无法撤消。确定要继续吗?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.AssetId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Asset ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[资产 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.CreatedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.CreatedByJob" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by job]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由作业创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.CreatedByModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由模型创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.CreatedOn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.RunId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Job (Run ID)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作业(运行 ID)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.Type" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.ModelProperties.Version" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Properties" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[属性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.PropertiesHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[属性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Property" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[property]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[属性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Stages.Stage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[暂存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Stages.StageTooltipContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AzureML supports three stages out of the box: Development, Production, Archived to allow users to indicate the readiness of an asset and manage asset's lifecycle. {learnMoreLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure ML 现成支持三个阶段。开发、生产、存档，以允许用户指示资产的就绪情况和管理资产的生命周期。{learnMoreLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Stages.StageTooltipContentLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Model.Update.ModelUpdateErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There was an error while updating the model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新此模型时出错]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.CreatedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.FeatureSet" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Feature set]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.FeatureStore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Feature store]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能存储区]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.Tags" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标记]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.ColumnNames.Version" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.EntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[feature sets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.FetchError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while fetching model feature set lineage information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提取模型功能集世系信息时出错]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelFeatureSets.NoFeaturesMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No feature sets are associated with this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有与此模型关联的功能集]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.ModelsEntityNamePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.AddToRegistry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Share model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共享模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.AddToRegistryAOAIModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sharing models is not supported for Azure OpenAI models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 模型不支持共享模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sharing models is not supported for Azure OpenAI Service models]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.AddToRegistryPrivateWorkspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sharing models is not supported for workspaces that are configured with private endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用专用终结点配置的工作区不支持共享模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.CreateSubscriptionText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a subscription to deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建要部署的订阅]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DeployButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DeployButtonTextZeroOnboarding" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用此模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DeployButtonTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay-as-you-go is available in the following regions: [{regions}]5D;. This workspace's region is [{region}]5D;.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即用即付在以下区域中可用: [{regions}]5D;。此工作区的区域为 [{region}]5D;。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DeployButtonTooltipAnonymous" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to view the pricing and deploy this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登录以查看定价并部署此模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to deploy a model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DownloadButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download all]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[全部下载]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.DownloadModelNotificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model {modelName} artifacts]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型 {modelName} 项目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.ErrorLoadMarketplace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unable to load marketplace settings for pay-as-you-go]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法加载即用即付的市场设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.EvaluateButtonDisabledJobWriteTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To evaluate a model, you must have {rbacAction} permission]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要评估模型，必须具有 {rbacAction} 权限]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.EvaluateButtonDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation is not supported for this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型不支持评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.EvaluateButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FailedToFetchJobDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An error occurred while fetching job details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提取作业详细信息时出错]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FinetuneAoaiButtonDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tuning is not supported for this model in this region]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此区域中的此模型不支持微调]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FinetuneButtonDisabledJobWriteTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To fine-tune a model, you must have {rbacAction} permission]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要微调模型，必须具有 {rbacAction} 权限]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[To finetune a model, you must have {rbacAction} permission]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FinetuneButtonDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tuning is not supported for this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此模型不支持微调]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Finetuning is not supported for this model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.FinetuneButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tune]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微调]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Finetune]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.MAAPTemporaryDisabledError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine tuning with managed compute is coming soon]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即将推出使用托管计算进行微调的功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.RefreshButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刷新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.RegisterButtonText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注册]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.RegisterButtonTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注册模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.RegisterButtonTitleAOAIModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Registering models is not supported for Azure OpenAI models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 模型不支持注册模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Registering models is not supported for Azure OpenAI Service models]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Pivot.UseInPromptFlowText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use in Prompt flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在提示流中使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Subscription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[订阅]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.CtaDescriptionV2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To try out {modelName}, you'll need an Azure account. Sign in now if you already have an account, or sign up to create a new one.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要试用 {modelName}，则需要一个 Azure 帐户。如果你已有帐户，请立即登录，否则，请注册以创建新帐户。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.CtaTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Access more features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[访问更多功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.CtaTitleV2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in for seamless model deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登录以实现无缝模型部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignIn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeploy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock the potential of Azure AI deployment capabilities. Gain access by signing in now to seamlessly deploy your models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[释放 Azure AI 部署功能的潜力。立即登录以无缝部署模型，从而获得访问权限。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to deploy this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployDl" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock the potential of Azure AI capabilities. Sign in to download or seamlessly deploy this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[释放 Azure AI 功能的潜力。登录以下载或无缝部署此模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to download or deploy this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployDlFt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock the potential of Azure AI capabilities. Sign in to download, fine-tune, or seamlessly deploy this model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[释放 Azure AI 功能的潜力。登录以下载、微调或无缝部署此模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to download, deploy, or fine-tune this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployDlFtTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to do more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登录以执行更多操作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployDlTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to download or deploy this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登录以下载或部署此模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployFt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock the potential of Azure AI capabilities. Sign in now to fine-tune or seamlessly deploy your models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[释放 Azure AI 功能的潜力。立即登录以微调或无缝部署模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sign in to deploy or fine-tune this model.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployFtTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to fine-tune and deploy models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登录以微调和部署模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignInDeployTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in for seamless model deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登录以实现无缝模型部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Unauth.SignUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign up for an Azure account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注册 Azure 帐户]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Pages.Details.Workspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作区]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SignInToDeploy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign in to deploy this model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[登录以部署此模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.DataMediaLanguages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data, media and languages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据、媒体和语言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Evaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More details from model provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[来自模型提供程序的更多详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Inputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.ModelVersions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model Versions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Outputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Pricing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pricing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[定价]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.SupportedDataTypes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Supported data types]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支持的数据类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.SupportedLanguages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Supported languages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支持的语言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Transparency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Transparency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透明度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Simplified.Undisclosed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不可用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.FinetuningTask" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fine-tuning task: {task}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[微调任务: {task}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Finetuning task: {task}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.Languages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Languages: {language}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[语言: {language}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.License" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[License: {license}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[许可证: {license}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.MinInferenceSku" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Minimum inferencing sku: {sku}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最小推理 SKU: {sku}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stickers.Task" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Task: {task}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[任务: {task}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Transparency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Transparency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[透明度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewLicense" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View license]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看许可证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";here" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此处]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
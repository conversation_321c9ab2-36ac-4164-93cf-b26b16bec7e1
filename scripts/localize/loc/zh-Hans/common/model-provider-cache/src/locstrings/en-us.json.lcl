﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\1\s\common\model-provider-cache\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-CN" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";Announcements.AI21.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI21's cost-effective model optimized for long context, now available on Azure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI21 经济高效的模型已针对长上下文进行了优化，现在可在 Azure 上使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AI21.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mamba-based models with unrivaled efficiency, latency, and long context handling.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于 Mamba 的模型，在效率、延迟和长上下文处理上无与伦比。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AI21.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI21 Jamba-Instruct is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这里有 AI21 Jamba-Instruct!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AI21.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet Jamba 1.5 Large + Jamba 1.5 Mini]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Meet Jamba 1.5 Large + Jamba 1.5 Mini]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AIStudioStatic.BlogLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AIStudioStatic.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore models from Meta, Mistral, and more, and get access to new features like Azure AI Services.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览 Meta、Mistral 等模型，并获取对新功能(如 Azure AI 服务)的访问权限。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Discover a catalog of thousands of large and small models from providers including {Meta}, {Cohere}, {Mistral}, and more]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.AIStudioStatic.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a project to get more models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建项目以获取更多模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Get access to even more models]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.ActionControl.CheckOutModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check out model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[签出模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.ActionControl.CheckOutModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check out models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[签出模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.ActionControl.ExploreMoreModels" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore more models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览更多模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.ActionControl.TryLimitedAccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try limited access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[尝试有限的访问权限]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latest version of GPT-4o, the most advanced multimodal model from OpenAI, is now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最新版本的 GPT-4o (OpenAI 最先进的多模态模型)现已发布]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[OpenAI's most cost effective model, GPT-4o mini is now available on Azure AI Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latest multi-modal models include gpt-4.1 (now live) and gpt-4.1-mini/gpt-4.1-nano coming soon]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最新的多模式模型包括 gpt-4.1 (现已发布)和即将推出的 gpt-4.1-mini/gpt-4.1-nano]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText11" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[o3 and o4-mini offer significantly enhanced reasoning, quality, and performance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o3 和 o4-mini 显著提升了推理、质量和性能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText12" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create high quality images for industry use cases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建面向行业用例的高质量图像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The o1 series feature an enhanced reasoning abilities to solve science and coding problems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o1 系列具有增强的推理能力，可解决科学和编码问题。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The o1 series feature an enhanced reasoning abilities to solve science and coding problems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o1 系列具有增强的推理能力，可解决科学和编码问题。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ GPT-4o realtime audio, now 60% lower cost with improved voice quality and input reliability]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ GPT-4o 实时音频，现在 60% 更低的成本，提高了语音质量和输入可靠性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build with advanced audio processing and asynchronous speech generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用高级音频处理和异步语音生成生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[o3-mini includes the o1 features with significant cost-efficiencies for scenarios requiring high performance.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o3-mini 包括对需要高性能的方案具有显著的成本效率的 o1 功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced audio at a fraction of the cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以部分成本为分数的高级音频]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText8" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The latest GPT model that excels at diverse text and image tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最新的 GPT 模型，擅长处理各种文本和图像任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.SubText9" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create your agent with computer-use-preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 computer-use-preview 创建代理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try improved GPT-4o]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[尝试改进的 GPT-4o]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Introducing GPT-4o mini]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title10" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[gpt-4.1 Model Series]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[gpt-4.1 模型系列]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title11" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlock Enterprise Agent workflows with o3 and o4-mini]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 o3 和 o4-mini 解锁 Enterprise Agent 工作流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title12" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing gpt-image-1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Gpt-image-1 简介]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Introducing Gpt-image-1]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experience the {o1} models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[体验 {o1} 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[o1 is generally available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o1 已正式发布]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Realtime Audio Updates]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实时音频汇报]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[4o Audio Preview is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此处为 4o 音频预览！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[o3-mini is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[o3-mini 在这里！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[4o Mini Audio is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此处为 4o 微型音频！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title8" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing GPT-4.5 Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT-4.5 预览版简介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Aoai.Title9" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Responses API with CUA]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 CUA 的响应 API]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Bria.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A text-to-image model trained exclusively on licensed data with legal liability coverage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用许可数据专门训练且具有法律责任覆盖的文本到图像模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Bria.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Announcing {Bria23Fast}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[宣布 {Bria23Fast}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Announcing {Bria32Fast}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Cohere} Rerank, the leading AI model for reranking, is now available on Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Cohere} Rerank 是用于重新排名的领先 AI 模型，现在可在 Azure 上使用]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Cohere's Enterprise AI models include Command R, Command R+, and Embed v3.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere's collection now includes Command R 08-2024 and Command R+ 08-2024.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere 的集合现在包含 Command R 08-2024 和 Command R+ 08-2024。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multimodal capabilities for {Embed3} now available in Cohere's collection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用于 {Embed3} 的多模式功能现已在 Cohere 的集合中提供]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A powerful and efficient model for enhancing search systems across 100 languages.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一款功能强大且高效的模型，可跨 100 种语言增强搜索系统。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere’s Command A and Embed 4 offer max performance with minimal compute.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere 的命令 A 和嵌入 4 以最少的计算提供最大性能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Cohere} Rerank is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Cohere} Rerank 来到了这里!]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[{Cohere} models are here!]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News from Cohere!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere 资讯!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News from Cohere!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere 资讯!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere Rerank v3.5 on Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 上的 Cohere Rerank v3.5]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Cohere.Title5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing Command A and Embed 4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[命令 A 和嵌入 4 简介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Core42.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Jais} from {Core42}, available first on Azure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Core42} 的 {Jais} 首先在 Azure 上推出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Core42.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Jais} from {Core42} now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[现已推出 {Core42} 的 {Jais}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Deci.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing {DeciLM}, {DeciCoder} and {DeciDiffusion}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{DeciLM}、{DeciCoder} 和 {DeciDiffusion} 简介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Deci.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{DeciAI} Generative AI models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{DeciAI} 生成式 AI 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A highly capable reasoning model designed to excel at science and coding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一种高度支持的原因模型，旨在突出处理科学和编码任务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We present DeepSeek-V3, a strong Mixture-of-Experts (MoE) language model with 671B total parameters with 37B activated for each token.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们推出了 DeepSeek-V3，这是一款强大的专家混合(MoE)语言模型，其参数总计 671B，每个令牌激活 37B。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DeepSeek-V3-0324 demonstrates notable improvements over its predecessor, DeepSeek-V3, in several key aspects.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-V3-0324 在多个关键方面相较于其前身 DeepSeek-V3 展示了显著的改进。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing DeepSeek-R1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-R1 简介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DeepSeek-V3 is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-V3 正式发布!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.DeepSeek.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DeepSeek-V3-0324 is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-V3-0324 正式发布!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Gretel.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate production-quality synthetic data optimized for AI and machine learning development.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成为 AI 和机器学习开发优化的生产质量合成数据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Gretel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Gretel Navigator Tabular is now available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Gretel Navigator 表格现已可用！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meta's next generation model, {Llama31405B} available on Azure now]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Meta 的下一代模型 {Llama31405B} 现已在 Azure 上可用]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Serverless APIs for Meta-Llama-3-8B-Instruct and Meta-Llama-3-70B-Instruct models]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try out Meta's latest {Llama32} SLMs and image reasoning models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[试用 Meta 最新的 {Llama32} SLM 和图像推理模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Llama32} 11B Vision Instruct and 90B Vision Instruct are here for your image reasoning use cases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Llama32} 11B Vision Instruct 和 90B Vision Instruct 可用于图像推理用例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhanced reasoning, math, and instruction following with performance comparable to Llama 3.1 405B.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增强了推理、数学和指令，其性能可与 Llama 3.1 405B 相比较。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build highly personalized experiences for every use case at a lower cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以更低的成本为每个用例构建高度个性化的体验]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet {Llama31405B}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[认识 {Llama31405B}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Build the future of AI with {Meta} {LlaMA3}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meta {Llama32} models are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Meta {Llama32} 模型就在此处!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Llama 3.3 from Meta]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[来自 Meta 的 Llama 3.3]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Llama.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing Llama 4 models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama 4 模型简介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Large (2407) and Nemo, Mistral AI's latest models, now available in Azure AI Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral AI 的最新模型 Large (2407) 和 Nemo 现已在 Azure AI Foundry 中可用]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Large (2407) and Nemo, Mistral AI's latest models, now available in Azure AI Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ministral 3B provide a compute-efficient and low-latency solution.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Ministral 3B 提供高效计算的低延迟解决方案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experience the power of new Mistral Large 24.11 with improved function calling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过改进的函数调用体验新 Mistral Large 24.11 的功能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empowering Developers, Transforming Code]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为开发人员助力，正在转换代码]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhance Mistral AI models' performance with customization.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用自定义功能增强 Mistral AI 模型的性能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhanced Mistral Small 3 with multimodal capabilities and a 128k context length. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增强了多模态功能和 128k 上下文长度的 Mistral Small 3。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.SubText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Document conversion to markdown with interleaved images and text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文档转换为带交错图像和文本的 Markdown]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral Large (2407) & Nemo are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral Large (2407) 和 Nemo 来到了这里!]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[{MistralSmall} is now available!]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New SLM from Mistral]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[来自 Mistral 的新 SLM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News from Mistral AI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[来自 Mistral AI 的新闻]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Codestral 2501 from Mistral AI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[来自 Mistral AI 的 Codestral 2501]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral Finetuning available for 2411/NeMo/3B]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[适用于 2411/NeMo/3B 的 Mistral Finetuning]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[News from Mistral AI: Mistral Small 3.1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[来自 Mistral AI：Mistral Small 3.1 的消息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Mistral.Title7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral OCR 25.03]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[错误 OCR 25.03]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Muse.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unleash creativity with Muse! Explore how to generate diverse, consistent, and innovative 3D gameplay!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[借着第三种创造力来释放创造力！探索如何生成多样、一致且创新的 3D 游戏！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Muse.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse is now available on AI Foundry!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[现在 AI Foundry 上有可用的用户！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.NTTDATA.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{tsuzumi} is a lightweight, fine-tuneable model with strong Japanese capabilities!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{tsuzumi} 是一个轻量级、可微调的模型，具有强大的日语功能!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.NTTDATA.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{tsuzumi} is now available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{tsuzumi} 现已推出!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nixtla.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TimeGEN-1 the timeseries forecasting model, available first on Azure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TimeGEN-1 时序预测模型，首先在 Azure 上提供]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nixtla.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Nixtla's TimeGEN-1 is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Nixtla 的 TimeGEN-1 就在这里！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Production-ready models, optimized for performance, hosted by {AzureAI}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生产就绪模型，针对性能进行了优化，由 {AzureAI} 托管]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TCO and performance optimized models to power your AI applications]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TCO 和性能优化模型，可为 AI 应用程序提供支持]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{NVIDIA} AI foundation models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{NVIDIA} AI 基础模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Nvidia.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{NVIDIANIM} now available on {AIFoundry}!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{NVIDIANIM} 现已在 {AIFoundry} 上提供!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.OpenAI.SubTextopenai-050825-KA" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Push the open model frontier with gpt-oss-120b and gpt-oss-20b, released under the permissive Apache 2.0 license.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过在宽松的 Apache 2.0 许可证下发布的 gpt-oss-120b 和 gpt-oss-20b，推动开放模型的前沿。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.OpenAI.Titleopenai-050825-KA" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Announcing new gpt-oss models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[宣布推出新的 gpt-oss 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft's latest {Phi3} SLMs offer groundbreaking performance at a small size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 最新的 {Phi3} SLM 提供了小规模的突破性的性能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft's latest {Phi35} MoE and Mini models now support 20+ languages.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的最新 {Phi35} MoE 和微型模型现在支持 20 多种语言。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4 14B, a highly capable model for low latency scenarios]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4 14B，适用于低延迟方案的高度支持模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[First multimodal SLM with 3 inputs (text, audio, image) in a unified architecture.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[首个采用统一体系结构，提供 3 种输入方式(文本、音频、图像)的多模式 SLM。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhanced quality, reasoning, efficiency, and speed, all packed into a compact size.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[轻量设计，带来更优质量、更强推理、更高效率和更高速度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Improving harm protections with competitive reasoning capabilities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在使用竞争牲推理功能来改进损害防护。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.SubText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Post-trained to address output limitations with better safety performance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[经过后训练，可解决输出限制问题并提高安全性能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Phi3} Finetuning is available!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{Phi3} Finetuning 已发布!]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[{Phi3} Serverless APIs are now available!]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{Phi35} models are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[现已推出 {Phi35} 模型！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4 is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4 在这里！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-Multimodal is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-Multimodal 同步推出!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-Mini is here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-Mini 正式发布!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A new reasoning model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新的推理模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Phi.Title7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MAI-DS-R1 reasoning model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MAI-DS-R1 推理模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Sdaia.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ALLaM-2-7B is here! A robust 7B LLM model crafted to boost Arabic language technology]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ALLaM-2-7B 来了!这是一个可靠的 7B LLM 模型，旨在提升阿拉伯语技术]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.Sdaia.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ALLaM-2-7B: latest Arabic LLM]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ALLaM-2-7B: 最新的阿拉伯语 LLM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovGPT4o.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPT-4o, the highest performing multimodal model from OpenAI, now available on {Azure}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT-4o 是 OpenAI 中性能最高的多模式模型，现可用于 {Azure}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovGPT4o.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GPT-4o is available on {Azure}!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT-4o 在 {Azure} 上可用！]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovLlama31.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A collection of pretrained and instruction turned generative text models in 8 and 70B sizes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一组预训练的指令转换了大小为 8 到 70B 的生成文本模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovLlama31.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet {LlaMA31} series]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 {LlaMA31} 系列]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovPhi3.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft's {Phi3} SLMs offer groundbreaking performance at a small size.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的 {Phi3} SLM 提供了小规模的突破性性能提升。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.SovPhi3.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore {Phi3} models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览 {Phi3} 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.SubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{StabilityAI} models available first on {Azure}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{StabilityAI} 模型首先在 {Azure} 上推出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.SubText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stability AI’s most advanced model delivers professional-grade image generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stability AI 的最高级模型提供专业级图像生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{StabilityAI} models are here!]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[现已推出 {StabilityAI} 模型!]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.StabilityAI.Title2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stable Diffusion 3.5 Large is now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Stable Dusion 3.5 Large is now available]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.SubTextaoai-022725" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The latest GPT model that excels at diverse text and image tasks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最新的 GPT 模型，擅长处理各种文本和图像任务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.SubTextopenai-240625-AO" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[codex-mini is a fine-tuned o4-mini model for fast, precise CLI automation, scripting, and repo refactoring]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[codex-mini 是一种经过微调的 o4-mini 模型，旨在实现快速、精准的 CLI 自动化、脚本编写和存储库重构]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.SubTextopenai-270525-Kz" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate up to 20s 1080p videos with Sora and its unique API: text-to-video now, image-to-video coming soon.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Sora 及其独特的 API 生成最长 20 秒的 1080p 视频: 文本转视频功能现已上线，图像转视频功能即将推出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.Titleaoai-022725" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing GPT-4.5 Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GPT-4.5 预览版简介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.Titleopenai-240625-AO" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[codex-mini: Fast, Scalable Code Generation for the CLI Era]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[codex-mini: 面向 CLI 时代的快速、可扩展代码生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.aoai.Titleopenai-270525-Kz" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Sora]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.blackforestlabs.SubTextblackforestlabs-230725-jn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Black Forest Labs foundational image generation and editing models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Black Forest Labs 基础映像生成和编辑模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.blackforestlabs.Titleblackforestlabs-230725-jn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[FLUX.1 Kontext [pro]5D; and FLUX1.1 [pro]5D; are now available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[FLUX.1 Kontext [pro]5D; 和 FLUX1.1 [pro]5D; 现已推出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.deepseek.SubTextdeepseek-030625-AP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Improved reasoning, fewer hallucinations, better coding and functions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[改进了推理、减少了幻觉，并提升了编码和函数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.deepseek.Titledeepseek-030625-AP" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing DeepSeek-R1-0528]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DeepSeek-R1-0528 简介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.mistral.SubTextmistralai-120525-op" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral Medium 3 delivers state-of-the-art performance at lower cost]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral Medium 3 以更低成本提供了最先进的性能]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.mistral.Titlemistralai-120525-op" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral Medium 3 (25.05)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral Medium 3 (25.05)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.phi.SubTextmicrosoft-160525-rb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Route prompts to different models to help save cost while maintaining quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将提示路由到不同的模型，以帮助节省成本，同时保持质量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.phi.SubTextmicrosoft-300425-jp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Small models with the power to reason, solve, and unlock complex understanding]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有推理、解决和解锁复杂理解能力的小型模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.phi.Titlemicrosoft-160525-rb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing Model Router]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型路由器简介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.phi.Titlemicrosoft-300425-jp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New Phi reasoning models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新的 Phi 推理模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.xai.SubTextxai-180525-Pu" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A model suite blending superior reasoning with extensive pre-training knowledge.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将卓越的推理与广泛的预训练知识混合在一起的模型套件。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Announcements.xai.Titlexai-180525-Pu" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Introducing Grok 3]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Grok 3 简介]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.common.section_1_client_creation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To create a client with the OpenAI SDK using an API key, initialize the client by passing your API key to the SDK's configuration. This allows you to authenticate and interact with OpenAI's services seamlessly:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用 API 密钥通过 OpenAI SDK 创建客户端，请将 API 密钥传递给 SDK 的配置以初始化客户端。这使你能够无缝地对 OpenAI 的服务进行身份验证和交互:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For OpenAI API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于 OpenAI API 终结点，请部署模型以生成终结点 URL 和 API 密钥，从而针对服务进行身份验证。在此示例中，终结点和密钥是包含终结点 URL 和 API 密钥的字符串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.csharp.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://www.nuget.org/packages/OpenAI-DotNet/). For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-dotnet) and [samples]5D;(https://github.com/openai/openai-dotnet).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://www.nuget.org/packages/OpenAI-DotNet/). 有关 Azure OpenAI SDK 的其他信息，请参阅完整 [documentation]5D;(https://github.com/openai/openai-dotnet) 和 [samples]5D;(https://github.com/openai/openai-dotnet).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.java.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about  Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-java) and [samples]5D;(https://github.com/openai/openai-java).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是一些用例的示例代码片段。有关 Azure OpenAI SDK 的其他信息，请参阅完整 [documentation]5D;(https://github.com/openai/openai-java) 和 [samples]5D;(https://github.com/openai/openai-java).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.javascript.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-node) and [samples]5D;(https://github.com/openai/openai-node).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是一些用例的示例代码片段。有关 Azure OpenAI SDK 的其他信息，请参阅完整 [documentation]5D;(https://github.com/openai/openai-node) 和 [samples]5D;(https://github.com/openai/openai-node).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.python.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-python) and [samples]5D;(https://github.com/openai/openai-python).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是一些用例的示例代码片段。有关 Azure OpenAI SDK 的其他信息，请参阅完整 [documentation]5D;(https://github.com/openai/openai-python) 和 [samples]5D;(https://github.com/openai/openai-python).]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.AoaiSDK.python.install_sdk" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure Open AI SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 pip 安装 Azure Open AI SDK (要求: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 凭据进行身份验证]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create a personal access token]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not need to give any permissions to the token. Note that the token will be sent to a Microsoft service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无需向令牌授予任何权限。请注意，该令牌将发送到 Microsoft 服务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the code snippets below, create an environment variable to set your token as the key for the client code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下面的代码片段，请创建一个环境变量，将令牌设置为客户端代码的密钥。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using bash:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果使用的是 bash:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_3c" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authorization is easiest using [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity). It finds the best credential to use in its running environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity) 授权最简单。它会找到在其运行环境中使用的最佳凭据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're in powershell:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果使用的是 powershell:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_4c" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Set the values of the client ID, tenant ID, and client secret of the AAD application as environment variables: `AZURE_CLIENT_ID`, `AZURE_TENANT_ID`, `AZURE_CLIENT_SECRET`.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将 AAD 应用程序的客户端 ID、租户 ID 和客户端密码的值设置为环境变量：`AZURE_CLIENT_ID`、`AZURE_TENANT_ID`、`AZURE_CLIENT_SECRET`。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Set the values of the client ID, tenant ID, and client secret of the AAD application as environment variables: AZURE_CLIENT_ID, AZURE_TENANT_ID, AZURE_CLIENT_SECRET.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_1_d_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using Windows command prompt:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果使用的是 Windows 命令提示符:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装依赖项]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run a basic code sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行基本代码示例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_3_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a basic call to the chat completion API. The call is synchronous.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此示例演示了对聊天补全 API 的基本调用。调用是同步的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore more samples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览更多示例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a multi-turn conversation with the chat completion API. When using the model for a chat application, you'll need to manage the history of that conversation and send the latest messages to the model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此示例演示了使用聊天补全 API 的多回合对话。将模型用于聊天应用程序时，需要管理该对话的历史记录并将最新消息发送到模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a better user experience, you will want to stream the response of the model so that the first token shows up early and you avoid waiting for long responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了获得更好的用户体验，你需要流式传输模型的响应，以便尽早显示第一个标记，避免等待较长的响应。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4_s_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run a multi-turn conversation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行多回合对话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_4_s_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stream the output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流式传输输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Going beyond rate limits]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[超出速率限制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_5_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rate limits for the playground and free API usage are intended to help you experiment with models and prototype your AI application. For use beyond those limits, and to bring your application to scale, you must provision resources from an Azure account, and authenticate from there. You don't need to change anything else in your code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[操场的速率限制和免费 API 用量旨在帮助你试验模型并制作 AI 应用程序的原型。如果用量超出这些限制，并且要扩展你的应用程序，则你必须通过 Azure 帐户预配资源，并在那里进行身份验证。无需更改代码中的其他任何内容。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The rate limits for the playground and free API usage are intended to help you experiment with models and prototype your AI application. For use beyond those limits, and to bring your application to scale, you must provision resources from an Azure account, and authenticate from there. You don't need to change anything else in your code. Use this link to discover how to go beyond the free tier limits in Azure AI.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.section_5_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure hosted. AI powered, can make mistakes. Subject to [Product Terms]5D;(https://aka.ms/amlprodexps/terms) & [Privacy Statement]5D;(https://aka.ms/amlprodexps/privacy). Not intended for production/sensitive data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 托管。由 AI 提供支持，可能会出错。受 [产品条款]5D;(https://aka.ms/amlprodexps/terms) 和 [隐私声明]5D;(https://aka.ms/amlprodexps/privacy) 约束。不适用于生产/敏感数据。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure hosted. AI powered, can make mistakes. . Subject to Product Terms & Privacy Statement. Not intended for production/sensitive data.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.common.title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Started]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开始使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.csharp.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package). For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[指向 NuGet 包的链接和一些示例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package)。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples)。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The link to the NuGet package and some examples. For additional information about Azure AI Inference SDK, see full documentation and samples.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.csharp.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for C# supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[适用于 C# 的 Azure SDK 支持 Azure 标识包，使其可轻松从 Microsoft 标识平台获取凭据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.csharp.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [dotnet runtime]5D;(https://dotnet.microsoft.com/en-us/download/dotnet/)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装 [dotnet 运行时]5D;(https://dotnet.microsoft.com/en-us/download/dotnet/)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Install dotnet runtime]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.csharp.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open power shell at the desired project folder, restore the environment and run the program.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在所需的项目文件夹中打开 power shell，还原环境并运行程序。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.curl.mistral_ocr_callout" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: Only base64 data is supported, document url or image url is not supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 仅支持 base64 数据，不支持文档 URL 或图像 URL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.curl.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将以下内容粘贴到 shell 中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.curl.section_3_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Call the chat completion API and pass the chat history:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[调用聊天补全 API 并传递聊天历史记录:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.curl.section_3_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is an example of calling the endpoint and streaming the response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这是调用终结点并流式传输响应的一个示例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/java/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/java/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/java/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/java/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 凭据进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for Java supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[适用于 Java 的 Azure SDK 支持 Azure 标识包，使其可轻松从 Microsoft 标识平台获取凭据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication with AAD requires adding azure identity package. To install, add this in your pom.xml:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 AAD 进行身份验证需要添加 Azure 标识包。若要安装，请在 pom.xml 中添加此项：]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To install, add this <dependency> in your maven pom.xml:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要安装，请在 maven pom.xml 中添加此 <dependency>:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.java.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a sample.java file and run as a package, for instance:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于下面的每个代码片段，请将内容复制到 sample.java 文件中，并以包的形式运行，例如:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 凭据进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以通过 [Azure 标识库]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity) 使用 Azure Active Directory 进行身份验证。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the @azure/identity package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下面显示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) 提供程序或 Azure SDK 提供的其他凭据提供程序，请安装 @azure/标识包：]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [Node.js]5D;(https://nodejs.org/).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装 [Node.js]5D;(https://nodejs.org/)。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Install Node.js.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy the following lines of text and save them as a file package.json inside your folder.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[复制以下文本行，并将其另存为你的文件夹中的文件 package.json。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: @azure/core-sse is only needed when you stream the chat completions response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 仅当流式传输聊天补全响应时，才需要 @azure/core-sse。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open a terminal window in this folder and run npm install.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开此文件夹中的终端窗口并运行 npm install。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.javascript.section_2_d_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a file sample.js and run with node sample.js.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于下面的每个代码片段，请将内容复制到文件 sample.js 中，并使用 node sample.js 运行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 凭据进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以通过 [Azure 标识库]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 使用 Azure Active Directory 进行身份验证。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下面显示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) 提供程序或向 Azure SDK 提供的其他凭据提供程序，请安装 [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 程序包：]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.ChatCompletion.python.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure AI Inference SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 pip 安装 Azure AI 推理 SDK(需要: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To authenticate with the model you will need to set up an Azure production key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用模型进行身份验证，需要设置 Azure 生产密钥。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not need to give any permissions to the token. Note that the token will be sent to a Microsoft service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无需向令牌授予任何权限。请注意，该令牌将发送到 Microsoft 服务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the code snippets below, create an environment variable to set your token as the key for the client code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下面的代码片段，请创建一个环境变量，将令牌设置为客户端代码的密钥。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_3c" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authorization is easiest using [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity). It finds the best credential to use in its running environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity) 授权最简单。它会找到在其运行环境中使用的最佳凭据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using bash:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果使用的是 bash:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_4c" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Set the values of the client ID, tenant ID, and client secret of the AAD application as environment variables: `AZURE_CLIENT_ID`, `AZURE_TENANT_ID`, `AZURE_CLIENT_SECRET`.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将 AAD 应用程序的客户端 ID、租户 ID 和客户端密码的值设置为环境变量：`AZURE_CLIENT_ID`、`AZURE_TENANT_ID`、`AZURE_CLIENT_SECRET`。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Set the values of the client ID, tenant ID, and client secret of the AAD application as environment variables: AZURE_CLIENT_ID, AZURE_TENANT_ID, AZURE_CLIENT_SECRET.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're in powershell:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果使用的是 powershell:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_1_d_6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using Windows command prompt:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果使用的是 Windows 命令提示符:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装依赖项]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run a basic code sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行基本代码示例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_3_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a call to embeddings API.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此示例演示了对嵌入 API 的调用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_3_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[It is leveraging the Azure AI model inference endpoint and your AAD token. The call is synchronous.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[它利用 Azure AI 模型推理终结点和 AAD 令牌。调用是同步的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Going beyond rate limits]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[超出速率限制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_4_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The rate limits for the playground and free API usage are intended to help you experiment with models and prototype your AI application. For use beyond those limits, and to bring your application to scale, you must provision resources from an Azure account. You don't need to change anything else in your code. Use this link to discover how to go beyond the free tier limits in Azure AI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[操场的速率限制和免费 API 用量旨在帮助你试验模型并制作 AI 应用程序的原型。若要超出这些限制使用以及若要使应用程序缩放，必须从 Azure 帐户预配资源。无需更改代码中的其他任何内容。使用此链接了解如何在 Azure AI 中超出免费层限制。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.section_4_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure hosted. AI powered, can make mistakes. Subject to [Product Terms]5D;(https://www.microsoft.com/licensing/terms/productoffering/MicrosoftAzure/MCA) & [Privacy Statement]5D;(https://www.microsoft.com/licensing/terms/product/PrivacyandSecurityTerms/MCA). Not intended for production/sensitive data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 托管。由 AI 提供支持，可能会出错。受 [产品条款]5D;(https://www.microsoft.com/licensing/terms/productoffering/MicrosoftAzure/MCA) 和 [隐私声明]5D;(https://www.microsoft.com/licensing/terms/product/PrivacyandSecurityTerms/MCA) 约束。不适用于生产/敏感数据。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure hosted. AI powered, can make mistakes. . Subject to Product Terms & Privacy Statement. Not intended for production/sensitive data.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.common.title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Started]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开始使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package). For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[指向 NuGet 包的链接和一些示例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package)。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 凭据进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for C# supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[适用于 C# 的 Azure SDK 支持 Azure 标识包，使其可轻松从 Microsoft 标识平台获取凭据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install dotnet runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装 dotnet 运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.csharp.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open power shell at the desired project folder, restore the environment and run the program.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在所需的项目文件夹中打开 power shell，还原环境并运行程序。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 凭据进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以通过 [Azure 标识库]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity) 使用 Azure Active Directory 进行身份验证。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the @azure/identity package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下面显示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) 提供程序或 Azure SDK 提供的其他凭据提供程序，请安装 @azure/标识包：]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [Node.js]5D;(https://nodejs.org/)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装 [Node.js]5D;(https://nodejs.org/)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Install Node.js.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy the following lines of text and save them as a file package.json inside your folder.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[复制以下文本行，并将其另存为你的文件夹中的文件 package.json。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: @azure/core-sse is only needed when you stream the chat completions response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 仅当流式传输聊天补全响应时，才需要 @azure/core-sse。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open a terminal window in this folder and run npm install.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开此文件夹中的终端窗口并运行 npm install。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.javascript.section_2_d_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a file sample.js and run with node sample.js.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于下面的每个代码片段，请将内容复制到文件 sample.js 中，并使用 node sample.js 运行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Azure Active Directory 凭据进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以通过 [Azure 标识库]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 使用 Azure Active Directory 进行身份验证。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下面显示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) 提供程序或向 Azure SDK 提供的其他凭据提供程序，请安装 [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 程序包：]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Embeddings.python.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure AI Inference SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 pip 安装 Azure AI 推理 SDK(需要: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ChatCompletion.common.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using API Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 API 密钥进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ChatCompletion.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For Serverless API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于无服务器 API 终结点，部署模型以生成终结点 URL 和 API 密钥，以针对服务进行身份验证。在此示例终结点和密钥中，是包含终结点 URL 和 API 密钥的字符串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ChatCompletion.common.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The API endpoint URL and API key can be found on the Deployments + Endpoint page once the model is deployed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型后，可以在“部署 + 终结点”页上找到 API 终结点 URL 和 API 密钥。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ChatCompletion.common.section_1_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure inferencing package includes a client for Chat Completion, `ChatCompletionsClient`. A client can be authenticated using the API key. The code sample creates and authenticates a synchronous ChatCompletionsClient:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 推理包包括用于聊天完成“ChatCompletionsClient”的客户端。可以使用 API 密钥对客户端进行身份验证。代码示例创建并验证同步 ChatCompletionsClient：]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure inferencing package includes client for Chat Completion, `ChatCompletionsClient`. A client can be authenticated using the API key. The code sample create and authenticates a synchronous ChatCompletionsClient:]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.Embeddings.common.section_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication using API Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 API 密钥进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.Embeddings.common.section_1_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For Serverless API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于无服务器 API 终结点，部署模型以生成终结点 URL 和 API 密钥，以针对服务进行身份验证。在此示例终结点和密钥中，是包含终结点 URL 和 API 密钥的字符串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.Embeddings.common.section_1_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The API endpoint URL and API key can be found on the Deployments + Endpoint page once the model is deployed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型后，可以在“部署 + 终结点”页上找到 API 终结点 URL 和 API 密钥。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.Embeddings.common.section_1_d_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure inferencing package includes a client for Embeddings, `EmbeddingsClient`. A client can be authenticated using the API key. The code sample creates and authenticates a synchronous EmbeddingsClient:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 推理包包括用于嵌入的客户端“EmbeddingsClient”。可以使用 API 密钥对客户端进行身份验证。代码示例创建同步 EmbeddingsClient 并进行身份验证：]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure inferencing package includes client for Embeddings, `EmbeddingsClient`. A client can be authenticated using the API key. The code sample create and authenticates a synchronous EmbeddingsClient:]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ImageGeneration.curl.editing_prompt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Make this black and white]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将此项设为黑白]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ImageGeneration.curl.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To generate an image, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要生成图像，请将以下内容粘贴到 shell 中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.ImageGeneration.curl.section_2_d_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To edit an image, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要编辑图像，请将以下内容粘贴到 shell 中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSample.Serverless.VideoGeneration.curl.section_2_d_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To generate a video, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要生成视频，请将以下内容粘贴到 shell 中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1119903703" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [Node.js]5D;(https://nodejs.org/).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装 [Node.js]5D;(https://nodejs.org/)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1135713175" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the code snippets below, create an environment variable to set your token as the key for the client code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下面的代码片段，请创建一个环境变量，将令牌设置为客户端代码的密钥。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1147064527" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下面显示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity#defaultazurecredential) 提供程序或向 Azure SDK 提供的其他凭据提供程序，请安装 [azure-identity]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 程序包：]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1197875290" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://www.nuget.org/packages/OpenAI-DotNet/). For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-dotnet) and [samples]5D;(https://github.com/openai/openai-dotnet).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[NuGet 包及一些示例的链接]5D;(https://www.nuget.org/packages/OpenAI-DotNet/)。有关 Azure OpenAI SDK 的其他信息，请参阅完整的 [文档]5D;(https://github.com/openai/openai-dotnet)和 [示例]5D;(https://github.com/openai/openai-dotnet)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1348340131" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stream the output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流式传输输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1374737853" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for Java supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[适用于 Java 的 Azure SDK 支持 Azure 标识包，使其可轻松从 Microsoft 标识平台获取凭据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1488390251" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-java) and [samples]5D;(https://github.com/openai/openai-java).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure OpenAI SDK 的其他信息，请参阅完整的 [文档]5D;(https://github.com/openai/openai-java)和 [示例]5D;(https://github.com/openai/openai-java)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1506840677" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're in powershell:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果使用的是 powershell:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1527034862" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To edit an image, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要编辑图像，请将以下内容粘贴到 shell 中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1554734733" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Call the chat completion API and pass the chat history:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[调用聊天补全 API 并传递聊天历史记录:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_157578493" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To create a client with the OpenAI SDK using an API key, initialize the client by passing your API key to the SDK's configuration. This allows you to authenticate and interact with OpenAI's services seamlessly:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用 API 密钥通过 OpenAI SDK 创建客户端，请将 API 密钥传递给 SDK 的配置以初始化客户端。这使你能够无缝地对 OpenAI 的服务进行身份验证和交互:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1604730713" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure AI Inference SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 pip 安装 Azure AI 推理 SDK(需要: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1632773573" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authorization is easiest using [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity). It finds the best credential to use in its running environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 [DefaultAzureCredential]5D;(https://learn.microsoft.com/en-us/azure/developer/java/sdk/identity) 授权最简单。它会找到在其运行环境中使用的最佳凭据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1657218110" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[It is leveraging the Azure AI model inference endpoint and your AAD token. The call is synchronous.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[它利用 Azure AI 模型推理终结点和 AAD 令牌。调用是同步的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1675574254" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[4. Explore more samples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[4. 浏览更多示例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1723953779" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-node) and [samples]5D;(https://github.com/openai/openai-node).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure OpenAI SDK 的其他信息，请参阅完整的 [文档]5D;(https://github.com/openai/openai-node)和 [示例]5D;(https://github.com/openai/openai-node)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_176344427" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure OpenAI SDK, see full [documentation]5D;(https://github.com/openai/openai-python) and [samples]5D;(https://github.com/openai/openai-python).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure OpenAI SDK 的其他信息，请参阅完整的 [文档]5D;(https://github.com/openai/openai-python)和 [示例]5D;(https://github.com/openai/openai-python)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_1820008423" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To install, add this <dependency> in your maven pom.xml:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要安装，请在 maven pom.xml 中添加此 <dependency>:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2018945115" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using Windows command prompt:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果使用的是 Windows 命令提示符:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2092433665" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure SDK for C# supports an Azure Identity package, making it easy to get credentials from Microsoft identity platform.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[适用于 C# 的 Azure SDK 支持 Azure 标识包，使其可轻松从 Microsoft 标识平台获取凭据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2123457486" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure inferencing package includes a client for Chat Completion, `ChatCompletionsClient`. A client can be authenticated using the API key. The code sample creates and authenticates a synchronous ChatCompletionsClient:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 推理包中包括用于聊天完成的客户端 `ChatCompletionsClient`。可以使用 API 密钥对客户端进行身份验证。代码示例会创建同步 ChatCompletionsClient 并对其进行身份验证:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2193611328" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is an example of calling the endpoint and streaming the response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这是调用终结点并流式传输响应的一个示例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_22025242" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[2. Run a basic code sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[2. 运行基本代码示例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2375228290" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/java/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/java/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/java/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/java/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2396968480" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: Only base64 data is supported, document url or image url is not supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 仅支持 base64 数据，不支持文档 URL 或图像 URL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2457896902" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以通过 [Azure 标识库]5D;(https://github.com/Azure/azure-sdk-for-python/tree/azure-ai-inference_1.0.0b5/sdk/identity/azure-identity) 使用 Azure Active Directory 进行身份验证。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2510457272" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a sample.java file and run as a package, for instance:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于下面的每个代码片段，请将内容复制到 sample.java 文件中，并以包的形式运行，例如:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2568478869" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Note: @azure/core-sse is only needed when you stream the chat completions response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 仅当流式传输聊天补全响应时，才需要 @azure/core-sse。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2611584301" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[3. Explore more samples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[3. 浏览更多示例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2767097481" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [Node.js]5D;(https://nodejs.org/)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装 [Node.js]5D;(https://nodejs.org/)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2792274105" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To generate a video, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要生成视频，请将以下内容粘贴到 shell 中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2883618492" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the Azure Open AI SDK using pip (Requires: Python >=3.8):]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 pip 安装 Azure Open AI SDK (要求: Python >=3.8):]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_2896283457" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For OpenAI API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于 OpenAI API 终结点，请部署模型以生成终结点 URL 和 API 密钥，从而针对服务进行身份验证。在此示例中，终结点和密钥是包含终结点 URL 和 API 密钥的字符串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_294659216" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure inferencing package includes a client for Embeddings, `EmbeddingsClient`. A client can be authenticated using the API key. The code sample creates and authenticates a synchronous EmbeddingsClient:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 推理包中包括用于嵌入的客户端 `EmbeddingsClient`。可以使用 API 密钥对客户端进行身份验证。代码示例可创建同步的 EmbeddingsClient 并对其进行身份验证:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3030664098" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not need to give any permissions to the token. Note that the token will be sent to a Microsoft service.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无需向令牌授予任何权限。请注意，该令牌将发送到 Microsoft 服务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3090420596" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To use the [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) provider shown below, or other credential providers provided with the Azure SDK, please install the @azure/identity package:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要使用下面显示的 [DefaultAzureCredential]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity#defaultazurecredential) 提供程序或 Azure SDK 提供的其他凭据提供程序，请安装 @azure/标识包：]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3158250671" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a basic call to the chat completion API. The call is synchronous.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此示例演示了对聊天补全 API 的基本调用。调用是同步的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3163714926" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install dotnet runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装 dotnet 运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3164412691" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To generate an image, paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要生成图像，请将以下内容粘贴到 shell 中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3201082139" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication with AAD requires adding azure identity package. To install, add this in your pom.xml:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 AAD 进行身份验证需要添加 Azure 标识包。若要安装，请在 pom.xml 中添加此项：]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3282207501" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you're using bash:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果使用的是 bash:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3300085498" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open power shell at the desired project folder, restore the environment and run the program.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在所需的项目文件夹中打开 power shell，还原环境并运行程序。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3322922063" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open a terminal window in this folder and run npm install.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开此文件夹中的终端窗口并运行 npm install。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3352724043" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install [dotnet runtime]5D;(https://dotnet.microsoft.com/en-us/download/dotnet/)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装 [dotnet 运行时]5D;(https://dotnet.microsoft.com/en-us/download/dotnet/)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3507107346" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a call to embeddings API.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此示例演示了对嵌入 API 的调用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3548031495" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Paste the following into a shell]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将以下内容粘贴到 shell 中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_355307576" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/js/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/js/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3609311341" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[1. Authentication using API Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[1. 使用 API 密钥进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3686597116" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Started]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开始使用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3824329197" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can authenticate with Azure Active Directory using the [Azure Identity library]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以通过 [Azure 标识库]5D;(https://github.com/Azure/azure-sdk-for-js/tree/@azure-rest/ai-inference_1.0.0-beta.3/sdk/identity/identity) 使用 Azure Active Directory 进行身份验证。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3825307696" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[1. Authentication using Azure Active Directory Credentials]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[1. 使用 Azure Active Directory 凭据进行身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3826507031" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run a multi-turn conversation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行多回合对话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_3962477442" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Below are example code snippets for a few use cases. For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下面是几个用例的示例代码片段。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/python/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/python/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_4026133267" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For Serverless API Endpoints, deploy the Model to generate the endpoint URL and an API key to authenticate against the service. In this sample endpoint and key are strings holding the endpoint URL and the API Key.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于无服务器 API 终结点，请部署模型来生成终结点 URL 和 API 密钥，以针对服务进行身份验证。在此示例中，终结点和密钥是包含终结点 URL 和 API 密钥的字符串。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_4037492491" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a better user experience, you will want to stream the response of the model so that the first token shows up early and you avoid waiting for long responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了获得更好的用户体验，你需要流式传输模型的响应，以便尽早显示第一个标记，避免等待较长的响应。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_4118566957" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For each of the code snippets below, copy the content into a file sample.js and run with node sample.js.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于下面的每个代码片段，请将内容复制到文件 sample.js 中，并使用 node sample.js 运行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_4137497717" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy the following lines of text and save them as a file package.json inside your folder.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[复制以下文本行，并将其另存为你的文件夹中的文件 package.json。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_466750452" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Set the values of the client ID, tenant ID, and client secret of the AAD application as environment variables: `AZURE_CLIENT_ID`, `AZURE_TENANT_ID`, `AZURE_CLIENT_SECRET`.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将 AAD 应用程序的客户端 ID、租户 ID 和客户端密码的值设置为环境变量：`AZURE_CLIENT_ID`、`AZURE_TENANT_ID`、`AZURE_CLIENT_SECRET`。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_589593347" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[The link to the NuGet package and some examples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package). For additional information about Azure AI Inference SDK, see full [documentation]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference) and [samples]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[指向 NuGet 包的链接和一些示例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/package)。有关 Azure AI 推理 SDK 的其他信息，请参阅完整的 [文档]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/reference) 和 [示例]5D;(https://aka.ms/azsdk/azure-ai-inference/csharp/samples)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_60304132" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This sample demonstrates a multi-turn conversation with the chat completion API. When using the model for a chat application, you'll need to manage the history of that conversation and send the latest messages to the model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此示例演示了使用聊天补全 API 的多回合对话。将模型用于聊天应用程序时，需要管理该对话的历史记录并将最新消息发送到模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_692681155" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[2. Install dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[2. 安装依赖项]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_812828475" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[3. Run a basic code sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[3. 运行基本代码示例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CodeSampleAutogen.CodeSamples_Autogen_863404337" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The API endpoint URL and API key can be found on the Deployments + Endpoint page once the model is deployed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署模型后，可以在“部署 + 终结点”页面查找 API 终结点 URL 和 API 密钥。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.Benchmarks.ActionText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare with benchmarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与基准进行比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.Benchmarks.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Model benchmarks are integrated into model catalog for easier navigation. Compare benchmarks across models and datasets available in the industry to assess which one meets your business scenario.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型基准已集成到模型目录中，以便更轻松地导航。比较行业中可用的模型和数据集的基准，以评估哪一个符合你的业务方案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.Benchmarks.LearnMoreText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[How model benchmarks are scored]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如何对模型基准进行评分]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.Benchmarks.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New model benchmarks available now in model catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型目录中现已提供新的模型基准]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.MaaSPtuDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can now purchase, use and manage provisioned throughput units (PTUs) for your serverless API model deployments from Meta, Mistral, Hugging Face and others.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[现在，可以从 Meta、Mistral、Face 等购买、使用和管理无服务器 API 模型部署的预配吞吐量单位(PTU)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.MaaSPtuTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Now available: Serverless endpoint PTUs for Model Catalog]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[现在可用：模型目录的无服务器终结点 PTU]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.SpeechPlayground.ActionText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check out Speech service models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看语音服务模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.SpeechPlayground.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try out powerful AI Service capabilities for free and build customized solutions using Speech, Language, Vision, Content Safety, Translator, and Document Intelligence APIs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[免费试用功能强大的 AI 服务功能，并使用语音、语言、视觉、内容安全、翻译和文档智能 API 生成自定义解决方案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.SpeechPlayground.LearnMoreText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about AI Services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解 AI 服务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FeatureAnnouncements.SpeechPlayground.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore all the Azure AI Services now available in AI Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览 AI Foundry 中现已提供的所有Azure AI服务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.AI21.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI21's Jamba-Instruct offers a massive 70K context window and the best value for price for long context use cases]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI21 Jamba-Instruct 提供包含大型 70K 上下文窗口，并为长上下文用例提供最佳性价比。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.AOAI.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is powered by a diverse set of models with different capabilities, such as GPT-4o mini, GPT-4o, DALL-E 3 and Whisper.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 由一组具有不同功能的多样化模型提供支持，例如 GPT-4o mini、GPT-4o、DALL-E 3 和 Whisper。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is powered by a diverse set of models with different capabilities, such as GPT-4o mini, GPT-4o, DALL-E 3 and Whisper.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.AOAI.description2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is powered by a diverse set of models with different capabilities, such as o1 series, gpt-4o series, DALL-E 3 and Whisper.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 由一组具有不同功能的多样化模型提供支持，例如 o1 系列、gpt-4o 系列、DALL-E 3 和 Whisper。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is powered by a diverse set of models with different capabilities, such as o1 series, gpt-4o series, DALL-E 3 and Whisper.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Cohere.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cohere's suite of Enterprise AI models is available now including Command R, Command R+, Embed, and Rerank]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Cohere 的企业 AI 模型套件现已推出，包括 Command R、Command R+、Embed 和 Rerank]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Cohere’s suite of Enterprise AI models is available now including Command R, Command R+, Embed, and Rerank]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Gpt4.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI is powered by a diverse set of models with different capabilities, such as GPT-4o, GPT-4, DALL-E 3, and Whisper.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 由一组具有不同功能的多样化模型提供支持，例如 GPT-4o、GPT-4、DALL-E 3 和 Whisper。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service is powered by a diverse set of models with different capabilities, such as GPT-4o, GPT-4, DALL-E 3, and Whisper.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Jais.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[JAIS is the best bi-lingual Arabic-English conversational language model, curated to understand multiple Arabic dialects and cultural nuances.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[JAIS 是一流的双语阿拉伯语-英语对话语言模型，旨在了解多种阿拉伯语方言和文化差异。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Meta.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet Llama 3.1 405B Instruct, Meta's next generation model, used for synthetic data generation and distillation of smaller models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[认识一下 Llama 3.1 405B Instruct，它是 Meta 的下一代模型，用于生成合成数据和提取较小的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Meta.description2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet Meta's latest Llama 3.2 models, available now for image reasoning and SLM edge-mobile scenarios]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 Meta 最新的 Llama 3.2 模型，现在已可用于图像推理和 SLM 边缘移动方案]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Meta.description3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Meet Meta's latest groundbreaking models; Llama 3.2 11B Vision Instruct and 90B Vision Instruct models for image reasoning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 Meta 的最新突破性模型；用于图像推理的 Llama 3.2 11B Vision Instruct 和 90B Vision Instruct 模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Meet Meta's latest grounding breaking models; Llama 3.2 11B Vision Instruct and 90B Vision Instruct models for image reasoning.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Mistral.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mistral AI's latest collection of large-language models include Mistral Small, Large (2402), Large (2407), Mistral-7B, Nemo, and Mixtral-8x22B.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Mistral AI 的最新大型语言模型集合包括 Mistral Small、Large (2402)、Large (2407)、Mistral-7B、Nemo 和 Mixtral-8x22B。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Mistral AI’s latest collection of large-language models include Mistral Small, Large (2402), Large (2407), Mistral-7B, Nemo, and Mixtral-8x22B.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Nixtla.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TimeGEN-1 is a generative pre-trained forecasting and anomaly detection model for time series data, like sales, finance, weather and more.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TimeGEN-1 是一种生成式、预先训练的预测和异常情况检测模型，用于销售、财务、天气等时序数据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Homepage.Phi3.description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New Phi-3 series of models offers the powerful performance and flexibility of small language models for every computing use cases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新的 Phi-3 系列模型为每个计算用例提供了小型语言模型的强大性能和灵活性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.DownloadDemo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Link to demo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[链接到演示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.OpenInGithub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open in Github]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 GitHub 中打开]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.ReadAnnouncements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read announcements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[阅读公告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.ReadPaper" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read paper]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[阅读报纸]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.WatchVideo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Watch video]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[观看视频]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraBodyVideoAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Megan Stanley, Senior Researcher at Microsoft Research AI for Science, discusses Aurora, a groundbreaking model for weather forecasting that could revolutionize predictions and mitigation of extreme events, air pollution, and climate change.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Research AI for Science 的高级研究员 Megan Stanley 探讨了 Aurora，这是一款具有突破性的天气预报模型，有望彻底改变极端天气事件、空气污染和气候变化的预测与缓解。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora is a large-scale foundation model developed for atmospheric forecasting. By leveraging extensive atmospheric data, this model enhances our capacity to predict and mitigate the impacts of extreme weather events.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 是一种专为气象预测开发的大规模基础模型。通过利用广泛的气象数据，此模型增强了我们预测和缓解极端天气事件影响的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora is a foundation model, built on vast amounts of atmospheric data, that can significantly improve our ability to predict extreme weather events. Explore how this innovative model can enhance weather forecasting.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 是一种基础模型，基于大量气象数据构建，可显著提高我们预测极端天气事件的能力。探索这种创新模型如何增强天气预报。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Aurora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 Aurora]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeVideoDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Built on vast amounts of atmospheric data, this foundation model can significantly improve our ability to predict extreme weather events.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此基础模型基于大量气象数据构建，可显著提高我们预测极端天气事件的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora emerged from the recognition that traditional weather prediction models often fall short in capturing the rapid intensification and peak wind speeds that characterize extreme storms. Aurora’s innovative architecture has been trained on over a million hours of diverse weather and climate simulations, enabling it to excel in a broad spectrum of predictive tasks while achieving an impressive spatial resolution of 0.1° - approximately 11 km at the equator. This level of granularity enhances the accuracy of operational forecasts and confers an estimated computational speed advantage of around 5,000 times over conventional numerical weather-prediction systems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 的出现源于一个认知: 传统天气预测模型在捕捉极端风暴的快速增强和峰值风速特征时往往力不从心。Aurora 的创新体系结构基于超过百万小时的多样化天气和气候模拟进行训练，使其在广泛的预测任务中表现体系结构，同时实现了 0.1° 的显著空间分辨率(在赤道地区约等于 11 公里)。这种粒度水平提高了业务预报的准确性，并使计算速度较传统数值天气预报系统提升约 5,000 倍。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora’s capabilities extend beyond accuracy and efficiency; it showcases versatility in forecasting a variety of atmospheric variables, including temperature, wind speed, and air pollution levels. Built using a flexible 3D Swin Transformer architecture and incorporating Perceiver-based encoders and decoders, Aurora effectively processes heterogeneous input data and generates predictions across multiple resolutions. Utilizing extensive pretraining on diverse datasets and fine-tuning for specific tasks, Aurora discerns complex patterns in atmospheric data, often yielding noteworthy results even with limited training data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 的能力不仅限于准确性和效率，它还在预测多种大气变量(包括温度、风速和空气污染水平)方面展示了多功能性。Aurora 基于灵活的 3D Swin Transformer 体系结构构建，并整合了基于 Perceiver 的编码器和解码器，可有效处理异构输入数据，并生成多分辨率预测结果。通过使用多样化数据集进行广泛的预训练，并针对特定任务进行微调，Aurora 能够识别大气数据中的复杂模式，即使在训练数据有限的情况下，通常也能产生显著的结果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The significance of Aurora transcends performance metrics; its robust architecture and diverse pretraining illustrate how scale and data variety enhance atmospheric forecasting. By incorporating data from climate simulations, reanalysis products, and operational forecasts, Aurora builds a nuanced and generalizable understanding of atmospheric dynamics. Compared to leading specialized deep learning models, Aurora demonstrates the ability to surpass existing benchmarks, establishing it as a crucial tool for future environmental predictions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 的重要性远不止于性能指标，其稳健的体系结构和多样的预训练展示了规模和数据多样性如何增强大气预测。通过整合气候模拟、再分析产品和业务预报的数据，Aurora 建立了对大大气动力学的细致且可泛化的理解。与领先的专业深度学习模型相比，Aurora 展现出超越现有基准的能力，使其成为未来环境预测的关键工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[炫目极光]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Aurora model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 Aurora 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu samples functionally distinct protein conformations. a) Large-scale domain motions such as opening/closing, rotation, and repacking. b) Local unfolding or unbinding of parts of the protein. c) Formation of cryptic binding pockets that are not present in the apo ground state.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu 可对功能各异的蛋白质构象进行采样。a) 大规模的结构域运动，例如开/合、旋转和重新排列。b) 局部解折叠或解离蛋白质部分区域。c) 形成在脱辅基基态中不存在的隐性结合口袋。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu multi-conformation benchmark of local unfolding. For each case, the PDB structure used as folded state reference is shown in red, with the part can unfold highlighted. Energy landscapes show the empirical free energy sampled by the pre-trained (black) and fine-tuned (blue) model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu 局部解折叠多构象基准检验。对于每种情况，用作折叠态参考的 PD 结构以红色显示，其中能够发生解折叠的部分则突出显示。能量景观展示了由预训练模型(黑色)和微调模型(蓝色)采样的经验自由能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu-1 is a deep learning model that can generate thousands of protein structures per hour on a single graphics processing unit. It provides orders of magnitude greater computational efficiency compared to classical MD simulations, thereby opening the door to insights that have, until now, been out of reach.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu-1 是一种深度学习模型，每小时可在单个图形处理单元上生成数千个蛋白质结构。与古典 MD 模拟相比，它的计算效率要高出几个数量级，从而为获取到目前为止一直无法获取的见解开辟了道路。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A deep learning model that can generate thousands of protein structures per hour on a single GPU.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每小时可在单个 GPU 上生成数千个蛋白质结构的深度学习模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover BioEmu]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 BioEmu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From forming muscle fibers to protecting us from disease, proteins play an essential role in almost all biological processes in humans and other life forms alike. There has been extraordinary progress in recent years toward better understanding protein structures using deep learning, enabling the accurate prediction of protein structures from their amino acid sequences. However, predicting a single protein structure from its amino acid sequence is like looking at a single frame of a movie—it offers only a snapshot of a highly flexible molecule. Biomolecular Emulator-1 (BioEmu-1) is a deep-learning model that provides scientists with a glimpse into the rich world of different structures each protein can adopt, or structural ensembles, bringing us a step closer to understanding how proteins work. A deeper understanding of proteins enables us to design more effective drugs, as many medications work by influencing protein structures to boost their function or prevent them from causing harm.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从形成肌肉纤维到保护我们免受疾病侵害，蛋白质在人类以及其他各种生命形式的几乎所有生物过程中都发挥着至关重要的作用。近年来，通过使用深度学习，对于蛋白质结构方面的更深入理解已取得重大进展，从而能够根据蛋白质的氨基酸结构准确预测其结构。但根据氨基酸序列预测单个蛋白质结构就像观看电影中的一帧画面一样，因为它只提供了一个高度灵活多变的分子的快照。生物分子模拟器 1 (BioEmu-1)是一种深度学习模型，它让科学家得以初步了解每种蛋白质可能呈现的多种结构的丰富世界或结构集合，从而推动我们更深入理解蛋白质的作用机制。通过对蛋白质的更深入理解，我们能够设计更有效的药物，因为许多药物都是通过影响蛋白质结构来增强其药效或防止其产生危害。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu-1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu-1]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get BioEmu model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 BioEmu 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTryItOutGitHub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open BioEmu repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 BioEmu 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A scatter plot graph showing the average score of 11 benchmarks on the y-axis and memory footprint in gigabytes (GB) on the x-axis for various open-weight large language models (LLMs). The Pareto Frontier of Open-weight LLMs is indicated by a blue dashed line. Data points include Qwen2-5.3B, BitNet b1.58 2B (marked with a red star as an outlier with low memory footprint), Qwen2-5.1-5B, SmolLM2-1.7B, MiniCPM-2B, LLaMa-2-13B, Gemma-3-13B, and Qwen2-0.5-8B. The image shows a comparison of different large language models based on their performance and memory usage, highlighting which models are more efficient or powerful relative to their memory footprint. This is relevant for understanding trade-offs in model design and deployment efficiency in machine learning applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[关于各种开源权重大语言模型(LLM)的散点图，其中 y 轴为各种模型在 11 项基准检验中的平均分数，x 轴为其内存占用情况(GB)。蓝色虚线指示了开源权重 LLM 的 Pareto 边界。数据点包括 Qven2-5.3B、BitNet b1.58 2B (使用红色星号标记为内存占用较低的离群值)、Qven2-5.1-5B、SmolLM2-1.7B、MiniCPM-2B、LLaMa-2-13B、Gemma-3-13B 和 Qven2-0.5-8B。此图显示了不同大语言模型在性能和内存使用方面的比较情况，其中突出显示了相对于其内存占用更高效或更强大的模型。这与了解机器学习应用程序中模型设计和部署效率的权衡相关。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research, BitNet b1.58 2B4T is the first open-source, native 1-bit large language model (LLM) in which every parameter is ternary (i.e., -1, 0, 1), at a 2-billion parameter scale. Trained on a corpus of 4 trillion tokens, this model demonstrates that native 1-bit LLMs can achieve performance comparable to leading open-weight, full-precision models of similar size, while offering substantial advantages in computational efficiency, including substantially reduced memory footprint, energy consumption, and decoding latency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet b1.58 2B4T 由 Microsoft Research 开发，它是第一款开放源代码本机 1 位大语言模型(LLM)，其参数规模为 20 亿，且每个参数均可为三个值(即 -1、0、1)。此模型在 4 万亿个令牌的语料库上进行训练，表明原生 1 位大型语言模型能够实现与同等大小的领先开放权重全精度模型相当的性能，同时在计算效率方面具有显著优势，包括大幅减少内存占用、能耗和解码延迟。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research, BitNet b1.58 2B4T is the first open-source, native 1-bit large language model (LLM) at a 2-billion parameter scale.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet b1.58 2B4T 由 Microsoft Research 开发，是第一个 20 亿参数规模的开放源代码本机 1 位大语言模型(LLM)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about BitNet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解有关 BitNet 的详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft researchers have demonstrated that BitNet b1.58 2B4T achieves performance on par with leading open-weight, full-precision LLMs of similar size, while offering significant advantages in computational efficiency, including substantially reduced memory footprint, energy consumption, and decoding latency. To facilitate further research and adoption, the model weights have been released along with open-source inference implementations for both GPU and CPU architectures.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 研究人员已证明，BitNet b1.58 2B4T 在性能上可与同等大小的领先开放权重全精度大型语言模型(LLM)持平，同时在计算效率方面提供了显著的优势，包括大幅减少内存占用、能耗解码延迟。为了促进进一步研究和采用，模型权重已经与 GPU 和 CPU 体系结构的开放源代码推理实现一起发布。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BitNet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open BitNet repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开源 BitNet 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BitNet has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet 已发布用于研究目的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Large Language Models (LLMs) are widely used for coding, but they primarily operate coding tasks in a "static" (i.e., single-turn) manner, reflecting the type of data they have been trained on. We propose that LLMs could perform better by interactively exploring codebases. To support this, we introduce debug-gym—a lightweight textual environment with tools like pdb—that enables LLM agents to debug and generate code more effectively. This approach can also extend to other tasks that benefit from active information gathering.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大型语言模型 (LLM) 广泛用于编码，但它们主要以“静态”(即单轮次)方式执行编码任务，反映了它们训练时所基于的数据类型。我们建议 LLM 通过以交互方式探索代码库来提高性能。为了提供该支持，我们引入了 debug-gym (一种包含 pdb 等工具的轻型文本环境)，使 LLM 智能体能够更有效地调试和生成代码。此方法还可以扩展到受益于活动信息收集的其他任务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Debug-gym is an environment for training LLMs to debug software more like humans—interactively and by leveraging tools—taking a big step toward more capable, collaborative AI-assisted programming.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[debug-gym 是一种环境，用于训练 LLM 以更像人类一样的方式调试软件(交互式和利用工具)，从而向能力更高、协作性更好的 AI 辅助编程迈出一大步。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try Debug-gym]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[试用 debug-gym]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI coding tools are rapidly evolving, but their biggest limitation remains debugging—an essential and time-consuming part of software development. Debug-gym, a new open-source research environment from Microsoft, is designed to change that. It helps AI agents learn to debug code the way human developers do: interactively, iteratively, and with the right tools.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AI 编码工具正在快速发展，但它们的最大限制仍然是调试，这是软件开发中必不可少且耗时的一部分。debug-gym 是来自 Microsoft 的新开源研究环境，旨在改变这一现状。它可帮助 AI 智能体学习以人类开发人员的方式调试代码: 以交互方式、迭代方式和使用正确的工具进行调试。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike current AI systems that passively suggest fixes based on error messages, debug-gym teaches agents to access tools. For example, access to Python's pdb debugger, enables AI coding agents to set breakpoints, inspect variable values, navigate large codebases, and even write custom tests.  Debug-gym includes three coding benchmarks to measure LLM-based agents' performance in interactive debugging: Aider for simple function-level code generation, Mini-nightmare for short, hand-crafted buggy code examples, and SWE-bench for real-world coding problems requiring a comprehensive understanding of a large codebase and a solution in the format of a GitHub pull request. Integration with swe-smith is coming soon. Early experiments show promising improvements in agents' performance when they are given access to tools, especially on complex, real-world coding tasks. Microsoft invites researchers and developers to explore this next frontier in AI-assisted programming.  Try debug-gym and build smarter, more useful AI tools that don't just generate code, but also improve it.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与当前根据错误消息被动建议修复的 AI 系统不同，debug-gym 教授智能体如何访问工具。例如，对 Python 的 pdb 调试器的访问使 AI 编码智能体能够设置断点、检查变量值、浏览大型代码库，甚至编写自定义测试。 debug-gym 包含三个编码基准，用于衡量基于 LLM 的代理在交互式调试中的表现：Aider 用于简单的函数级代码生成，Mini-nightmare 用于短小的手工设计的错误代码示例，而 SWE-bench 用于需要全面理解大型代码库的实际编码问题和 GitHub 拉取请求格式的解决方案。与 swe-smith 的集成即将推出。早期试验表明，当智能体有权访问工具时(尤其是执行复杂的实际编码任务时)，智能体的性能有希望改进。Microsoft 邀请研究人员和开发人员探索 AI 辅助编程的下一个前沿。 尝试 debug-gym，构建更智能、更有用的 AI 工具，这些工具不仅能生成代码，还能改进代码。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Debug-gym]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[debug-gym]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.DebugGymTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try debug-gym]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[试用 debug-gym]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An animated sequence showing how a chain of amino acids folds into a protein’s three-dimensional structure using the natural sequences predicted by EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一个动画序列，显示氨基酸链如何使用 EvoDiff 预测的自然序列折叠成蛋白质的三维结构]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff is a general-purpose diffusion framework that combines evolutionary-scale data with the distinct conditioning capabilities of diffusion models for controllable protein generation in sequence space. EvoDiff generates high-fidelity, diverse, and structurally-plausible proteins that cover natural sequence and functional space.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff 是一个通用的扩散框架，它将进化规模的数据与扩散模型的独特调节能力相结合，以便在序列空间中实现可控的蛋白质生成。EvoDiff 将生成覆盖自然序列和功能空间的高保真、多样化且结构合理的蛋白质。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A general-purpose diffusion framework for controllable protein generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一种用于实现可控蛋白质生成的通用扩散框架。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 EvoDiff]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Critically, EvoDiff can generate proteins inaccessible to structure-based models, such as those with disordered regions, while maintaining the ability to design scaffolds for functional structural motifs, demonstrating the universality of our sequence-based formulation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[至关重要的是，EvoDiff 可以生成基于结构的模型所无法获得的蛋白质(例如那些具有无序区域的蛋白质)，同时保持为功能性结构基序设计支架的能力，证明了我们基于序列的公式的通用性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff will expand capabilities in protein engineering beyond the structure-function paradigm toward programmable, sequence-first design. The sequence and MSA models – EvoDiff-Seq and EvoDiff-MSA, respectively – were evaluated across a range of generation tasks to demonstrate their power for controllable protein design.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff 会将蛋白质工程的能力从结构-功能范式扩展到可编程的序列优先设计。序列模型和 MSA 模型(分别为 EvoDiff-Seq 和 EvoDiff-MSA)在一系列生成任务中进行了评估，证明了自身在可控蛋白质设计方面的强大能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTryItOutAzureAI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get EvoDiff model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 EvoDiff 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTryItOutGithub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open EvoDiff repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 EvoDiff 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* A gradient blue to green background features a white flowchart with rectangular boxes connected by arrows, ending in a hexagonal “STOP” sign and a check mark on the right side.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*装饰性* 蓝绿渐变背景上呈现一个白色流程图，其中矩形框通过箭头连接，末端是一个六边形的“停止”标志，右侧有一个勾号。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT 是一种教导 AI 智能体实现更高效探索的方法，使其能够智能地导航环境、收集有价值的信息、评估选项，并确定最佳决策和规划策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Teach AI agents to explore more effectively.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[教导 AI 智能体实现更高效探索。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT 是一种教导 AI 智能体实现更高效探索的方法，使其能够智能地导航环境、收集有价值的信息、评估选项，并确定最佳决策和规划策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover ExACT]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 ExACT]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Autonomous AI agents are transforming the way we approach multi-step decision-making processes, streamlining tasks like web browsing, video editing, and file management. By applying advanced machine learning, they automate workflows, optimize performance, and reduce the need for human input.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自主 AI 智能体正在改变我们处理多步骤决策流程的方式，简化 Web 浏览、视频编辑和文件管理等任务。通过应用先进的机器学习，它们能够自动化工作流、优化性能，并减少对人类输入的需求。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[However, these systems struggle in complex, dynamic environments. A key challenge lies in balancing exploitation, using known strategies for immediate gains, with exploration, which involves seeking new strategies that could yield long-term benefits. Additionally, they often have difficulty adapting to unpredictable changes in conditions and objectives, as well as generalizing knowledge across contexts, limiting their ability to transfer learned strategies between domains.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[然而，这些系统在复杂的动态环境中面临挑战。关键挑战在于如何平衡利用(使用已知策略获取即时收益)与探索(这涉及到寻求可能带来长期获益的新策略)。此外，它们通常难以适应条件和目标的不可预测变化，也难以在不同上下文中泛化知识，这限制了它们在不同领域间迁移所学策略的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In response, Microsoft researchers have developed ExACT, an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作为回应，Microsoft 研究人员开发了 ExACT，这是是一种教导 AI 智能体实现更高效探索的方法，使其能够智能地导航环境、收集有价值的信息、评估选项，并确定最佳决策和规划策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open ExACT repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 ExACT 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The MCP Server for Azure AI Foundry Labs is designed to supercharge team velocity in adopting and evaluating breakthrough AI research. By equipping GitHub Copilot with custom tools for intelligent model discovery, tailored implementation guidance, and rapid prototyping, this can achieve exponential productivity gains and reduce the “idea-to-prototype” cycle to under 10 minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用于 Azure AI Foundry 实验室的 MCP 服务器旨在加速团队在采用和评估突破性 AI 研究结果方面的速度。通过为 GitHub Copilot 提供用于支持智能模型发现、定制实施指导和快速原型设计的自定义工具，这可以实现指数级的生产力提升，并将“从创意到原型”的周期缩短到 10 分钟以内。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Integrate, Prototype, and Accelerate AI Model Experimentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[集成、原型设计和加速 AI 模型实验]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about the MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 MCP 服务器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Unified Model Discovery:</strong> Instantly list 45+ models (Microsoft Research, OpenAI, Meta, Mistral, and Azure Foundry Labs specialties) inside your coding environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong></strong>统一模型发现:在编码环境中即时列出超过 45 个模型(包括 Microsoft Research、OpenAI、Meta、Mistral 和 Azure Foundry Labs 的专用模型)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Implementation Guidance On Demand:</strong> GitHub Copilot receives detailed integration documentation and usage hints for each model—reducing hallucination and speeding up “from idea to working code."]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong></strong>按需实现指南: GitHub Copilot 会收到有关每个模型的详细集成文档和使用提示，以减少幻觉并加快“从构想到工作代码”的进程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Seamless Copilot Integration:</strong> GitHub Copilot is enhanced via MCP servers to understand model endpoints, available tools, and recommend best-fit models for your use case.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无缝 Copilot 集成:<strong></strong> GitHub Copilot 通过 MCP 服务器得到了增强，能够理解模型终结点、可用工具，并为你的用例推荐最合适的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Generative Coding Without the Chaos:</strong> “Prototyping without the rabbit holes.” The MCP Server constrains and guides the AI, avoiding runaway file generation, dead ends, or coding spirals typical of other agentic tools.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有任何漏洞的生成式编码:<strong></strong>“一切尽在掌握的原型设计。”MCP 服务器会约束并指导 AI，从而避免其他代理工具中常见的文件生成失控、死胡同或编码螺旋问题。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Prototyping at Lightning Speed:</strong> Build evaluators, dashboards, analyzers, and bespoke AI apps in minutes. Typical initial working apps are generated in <10 minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以闪电般的速度进行原型设计: <strong></strong>几分钟内即可构建评估器、仪表板、分析器和定制 AI 应用。典型的初始工作应用可在不到 10 分钟内生成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MCP 服务器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get started with the MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开始使用 MCP 服务器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Skala functional will enable more accurate, scalable predictions in computational chemistry. It starts with the largest high-accuracy dataset ever built for training deep-learning-based density functional theory (DFT) models. This dataset underpins Skala—coming soon to the Azure AI Foundry catalog—a new machine-learned exchange-correlation functional that reaches experimental accuracy for atomization energies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skala 泛函将在计算化学中实现更准确且可缩放的预测。它始于为训练基于深度学习的密度泛函理论(DFT)模型而构建的有史以来最大的高准确度数据集。此数据集支持 Skala (即将发布到 Azure AI Foundry 目录)，这是一种新的机器学习的交换-相关泛函，可在原子化能方面达到实验准确度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the largest high-accuracy dataset for training deep-learning-based models for density functional theory (DFT). It enables a leap forward in predictive chemistry and supports the development of Skala, a new DFT functional from Microsoft Research that has achieved a breakthrough in accuracy for this workhorse method that thousands of scientists use every year to simulate matter at the atomistic level.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这是用于训练基于深度学习的密度泛函理论(DFT)模型的最大高准确度数据集。它实现了预测化学的跨越式发展，并支持 Skala 的开发，Skala 是 Microsoft Research 推出的全新 DFT 泛函，对这种每年数千位科学家用于在院子层面模拟物质的常用方法在准确度方面实现了突破。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore MSR-ACC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 MSR-ACC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCShortenedTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MSR-ACC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MSR-ACC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This research marks a major advance in computational chemistry by dramatically improving the accuracy of <em>density functional theory</em> (DFT)—the most widely used method for simulating materials and molecules. The core breakthrough is a new deep-learning-based exchange-correlation (XC) functional, called Skala, which achieves <em>experimental-level accuracy</em> in predicting molecular properties like atomization energy—something previously thought out of reach for DFT. Skala will be available in the Azure AI Foundry catalog in the future. Researchers have released a large part of this dataset to the scientific community.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此项研究显著提升了密度泛函理论(DFT) - 模拟材料与分子的最常用方法 - 的准确度，标志着计算化学领域的重大进展。<em></em><em></em>其核心突破在于名为 Skala 的基于深度学习的交换-相关(XC)泛函，它在预测原子化能等分子属性方面达到了实验级精度，这在以往被认为是 DFT 难以企及的效果。Skala 将在将来的 Azure AI Foundry 目录中提供。研究人员已向科学界发布了此数据集的一大部分。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DFT is fast but limited by manual approximations of the XC functional. The research team addressed that limitation by generating the largest high-accuracy dataset of molecular energies to date, leveraging first principles methods and cloud-scale computation. They then trained Skala to learn directly from electron densities, bypassing hand-crafted feature engineering that has stalled progress for decades.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DFT 速度很快，但受限于对 XC 泛函的手动近似。该研究团队利用第一性原理方法和云规模计算，通过生成迄今为止最大的高准确度分子能量数据集，解决了这一局限。然后，他们训练 Skala 以直接从电子密度中学习，绕过了几十年来阻碍发展的手工打造的特征工程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This achievement removes a long-standing barrier in computational chemistry, enabling DFT to shift from interpreting experimental results to predicting them reliably. That unlocks enormous potential across domains—from drug design to battery development—where accurate, affordable simulations can replace costly lab work.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这一成果消除了计算化学中一个长期存在的障碍，支持 DFT 从解释实验结果转变为可靠地预测实验结果。该成果解锁了从药物设计到电池开发等各个领域的巨大潜力 - 在这些领域中，准确且经济的模拟可以取代成本高昂的实验室工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Research Accurate Chemistry Collection (MSR-ACC)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Research 精确化学数据集(MSR-ACC)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Diagram with five items (‘Orchestrator,’ ‘Coder,’ ‘FileSurfer,’ ‘WebSurfer,’ ‘ComputerTerminal’) connected to a single point.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[显示 5 个项目("Orchestrator"、"Coder"、"FileSurfer"、"WebSurfer"、"ComputerTerminal")连接到一个点的关系图。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One is a generalist multi-agent system created to address intricate web and file-based tasks. By utilizing an intelligent Orchestrator alongside specialized agents, it facilitates the automation of complex, multi-step activities across various environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 是一个通用型多智能体系统，旨在处理复杂的 Web 和基于文件的任务。通过将智能 Orchestrator 和专用智能体结合使用，它能够跨各种环境自动执行复杂的多步骤活动。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One is a multi-agent system designed to navigate complex tasks across diverse domains. Discover how intelligent agents could operate autonomously to enhance workflow efficiency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 是一个多智能体系统，旨在跨不同领域处理复杂任务。了解智能体如何实现自主运行来提高工作流效率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One introduces advancements in agentic AI through its modular architecture, which features a lead agent termed the Orchestrator. This component manages a network of specialized agents, enabling each to concentrate on specific tasks, such as web navigation, code execution, or local file management. This structure supports the efficient pursuit of complex objectives.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 通过其模块化体系结构推进智能体 AI 的发展，该体系结构具有一个名为 Orchestrator 的主导智能体。该组件管理专用智能体的网络，使每个智能体能够专注于特定任务，例如 Web 导航、代码执行或本地文件管理。这种结构支持高效达成复杂目标。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Central to Magentic-One’s operation are its dual planning mechanisms: the Task Ledger and the Progress Ledger. The Task Ledger empowers the Orchestrator to formulate strategic approaches, while the Progress Ledger provides real-time updates on task statuses. This interconnected system allows for ongoing evaluation and adjustment, optimizing overall efficiency. In situations where obstacles arise, the Orchestrator can adapt plans and reallocate tasks, ensuring effective workflow management under varying conditions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 运行的核心在于其双重规划机制: 任务账本和进度账本。任务账本使 Orchestrator 能够制定战略方法，而进度账本则提供任务状态的实时更新。这个互联系统可实现持续评估和调整，从而优化整体效率。在遇到障碍时，Orchestrator 可调整计划并重新分配任务，确保在不同条件下有效管理工作流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The image shows a bar chart comparing accuracy percentages across different systems on the medium subset of the GAIA benchmark.: Magentic-One (about 33%), Webby autonomous (about 38%), Webby + Simulated Human (about 58%), and Human (about 90%).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[该图显示了一个条形图，用于比较不同系统在 GAIA 基准的中等子集上的准确性百分比: Magentic-One (约 33%)、Webby 自主(约 38%)、Webby + 模拟人类(约 58%)和人类(约 90%)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlocking the full potential of AI requires the development of effective mechanisms for human-AI collaboration. By reducing cognitive load while ensuring users remain in control, AI can significantly enhance human capabilities and streamline complex workflows. Magentic-UI was designed with this goal in mind, serving as a research platform aimed at advancing research on human-in-the-loop experiences.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了释放 AI 的全部潜力，需要开发支持人类与 AI 协作的有效机制。通过在减少认知负担的同时确保用户仍然具有控制权，AI 可以显著增强人类的能力并简化复杂的工作流。Magentic-UI 是为实现这一目标而设计的，可作为一个旨在推进人机交互体验研究的研究平台。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI is an open-source experimental platform to accelerate progress in human-agent collaboration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 是一个开源的实验平台，旨在加速人机协作的进展。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about Magentic-UI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 Magentic-UI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI builds on  Magentic-One, a generalist multi-agent system that specializes in complex web and file-based tasks, and is powered by {AutoGenLink}, our leading agent framework.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 基于 Magentic-One 构建。后者是一种通用多代理系统，专门用于处理基于 Web 和文件的复杂任务，由我们领先的代理框架 {AutoGenLink} 提供支持。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key features of the Magentic-UI research include:<ol><li><strong>Co-planning.</strong> Magentic-UI allows users to directly modify its plan through a plan editor or by providing textual feedback before Magentic-UI executes any actions.</li><li><strong>Co-tasking.</strong> Users can pause the system and give feedback in natural language or demonstrate it by directly taking control of the browser.</li><li><strong>Action guards.</strong> Magentic-UI seeks user approval before executing potentially irreversible actions, and the user can specify how often Magentic-UI needs approvals. Furthermore, Magentic-UI is sandboxed for the safe operation of tools such as browsers and code executors.</li><li><strong>Task learning.</strong> Magentic-UI can learn and save plans from previous interactions to improve task completion for future tasks.</li></ol>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 研究的主要功能包括:<ol><li>共同制定规划。<strong></strong>Magentic-UI 允许用户直接通过计划编辑器或通过在 Magentic-UI 执行任何作之前提供文本反馈来修改其计划。</li><li>共同完成任务。<strong></strong>用户可以暂停系统并以自然语言提供反馈，或者通过直接控制浏览器来进行演示。</li><li>行动守卫。<strong></strong>Magentic-UI 会在执行可能不可逆的操作之前寻求用户批准，用户可以指定 Magentic-UI 需要审批的频率。此外，Magentic-UI 经过沙盒化，以确保浏览器和代码执行器等工具的安全运行。</li><li>任务学习。<strong></strong>Magentic-UI 可以从以前的交互中学习并保存计划，以改进未来任务的完成情况。</li></ol>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUITitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUITryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Magentic-UI repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 Magentic-UI 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A mechanical employee operating a large green machine in an industrial setting. He’s using a control panel with his right hand and holding a tablet in the other.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一名机械操作员在工业环境中操控一台大型绿色机器。他正用右手操作控制面板，左手拿着平板电脑。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Internal wiring and components of a device with green check marks and orange circles indicating areas of interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[设备的内部线路和组件，绿色勾号和橙色圆圈指示需要注意的区域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in digital and physical environments. Magma builds on the foundation models paradigm that pretraining on a larger amount of more diverse datasets allows these models to generalize better to new tasks and environments. Magma can perceive visual and textual inputs and generate actions, whether it’s clicking a button in a user interface or grabbing a tool in the real world. This new model represents a significant step towards AI agents that can serve as general-purpose assistants.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma 是一种多模态基础模型，旨在理解数字和物理环境并在这些环境中操作。Magma 基于基础模型范式构建，通过在更大规模、更多样化的数据集上进行预训练，使这些模型能够更好地泛化到新任务和新环境。Magma 能够感知视觉和文本输入并生成动作，无论是单击用户界面中的按钮还是在现实世界中抓取工具。这一新模型标志着向通用型 AI 智能体助手迈出的重要一步。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A multimodal AI foundation model designed to both understand and act in digital and physical environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一种多模态 AI 基础模型，旨在理解数字和物理环境并在这些环境中操作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in digital and physical environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma 是一种多模态基础模型，旨在理解数字和物理环境并在这些环境中操作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Magma]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Magma]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Imagine an AI assistant that can book a meeting online and also set up the room for it – navigating software menus as effortlessly as it moves physical objects. Such seamless integration of digital and physical tasks has long been a sci-fi vision. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[想象一个 AI 助手，它既能在线预订会议，还能为其设置房间 - 像移动物理对象一样轻松地导航软件菜单。这种数字任务与物理任务的无缝集成长期以来一直是科幻作品中的构想。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft researchers are bringing it closer to reality with Magma, a multimodal AI foundation model designed to both understand and act in digital and physical environments. Magma builds on the foundation models paradigm, that pretraining on a larger amount of more diverse datasets allows these models to generalize better to new tasks and environments. Magma can perceive visual and textual inputs and generate actions, whether it’s clicking a button in a user interface or grabbing a tool in the real world. This new model represents a significant step towards AI agents that can serve as general-purpose assistants. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 研究人员通过 Magma 让这一愿景更接近现实 - Magma 是一种多模态 AI 基础模型，旨在理解数字和物理环境并在这些环境中操作。Magma 基于基础模型范式构建，通过在更大规模、更多样化的数据集上进行预训练，使这些模型能够更好地泛化到新任务和新环境。Magma 能够感知视觉和文本输入并生成动作，无论是单击用户界面中的按钮还是在现实世界中抓取工具。这一新模型标志着向通用型 AI 智能体助手迈出的重要一步。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Vision-Language-Action (VLA) models are typically pretrained on large amounts of vision-language-action datasets to obtain the vision-language understanding ability (verbal intelligence) and the ability to perceive and interact with the visual spatial world to perform a wide range of tasks (spatial intelligence). However, due to the dramatic difference among various digital and physical environments, separate VLA models are trained and used for different environments. These models cannot easily generalize to new tasks and new environments that are unseen in training data. Moreover, most of these models do not leverage pretrained vision-language (VL) models or diverse vision-language datasets. As a result, their vision language understanding ability is often inferior to state-of-the-art VL models, which further limits model generalizability.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[视觉-语言-行动(VLA)模型通常使用大量视觉-语言-行动数据集进行预训练，以获得视觉-语言理解能力(语言智能)和感知并与视觉空间世界来执行各种任务的能力(空间智能)。然而，由于不同数字和物理环境之间的显著差异，针对不同环境训练和使用了独立的 VLA 模型。这些模型无法轻易泛化到训练数据中未见过的新任务和新环境。此外，这些模型大多数未使用预训练的视觉-语言(VL)模型或多样的视觉-语言数据集。因此，它们的视觉语言理解能力通常逊色于最先进的 VL 模型，这进一步限制了模型的泛化能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma, is a VLA foundation model that can adapt to downstream (unseen) agentic tasks in both the digital and physical environments. With Magma, researchers showed that it is beneficial to pretrain a single VLA model for AI agents across these environments while still achieving state-of-the-art results on UI navigation and robotic manipulation tasks, outperforming previous models that are tailored specifically to these tasks. On VL tasks, Magma also compares favorably to popular VL models that are trained on much larger datasets.  ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma 是一种 VLA 基础模型，能够适应数字环境和物理环境中的下游(未见过的)智能体任务。借助 Magma，研究人员证实，为 AI 智能体跨这些环境预训练单个 VLA 模型是有益的，同时在 UI 导航和机器人操作任务上仍能取得先进的结果，其性能优于先前专门为这些任务定制的模型。在 VL 任务中，Magma 的性能也优于使用更大数据集训练的热门 VL 模型。  ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Magma model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 Magma 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Various molecular structures and crystal lattices displayed in a grid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[网格中显示的各种分子结构和晶体晶格。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim is a deep learning model for accurate and efficient materials simulation and property prediction over a broad range of elements, temperatures and pressures to enable in silico materials design.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 是一种深度学习模型，用于在广泛的元素、温度和压力条件下进行准确高效的材料模拟和性能预测，从而支持计算机模拟材料设计。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An AI-driven innovation transforming how we create and understand new materials, starting with accurate and efficient simulations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一项 AI 驱动的创新，从准确高效模拟开始，正在改变我们创建和理解新材料的方式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore MatterSim]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 MatterSim]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim employs deep learning to understand atomic interactions from the very fundamental principles of quantum mechanics, across a comprehensive spectrum of elements and conditions—from 0 to 5,000 Kelvin (K), and from standard atmospheric pressure to 10,000,000 atmospheres.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 利用深度学习来理解量子力学基本原理中的原子相互作用，覆盖全元素谱系和广泛条件范围 - 温度从 0 到 5,000 开尔文(K)，大气压力从标准大气压到 10,000,000 倍大气压。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim efficiently handles simulations for a variety of materials, including metals, oxides, sulfides, halides, and their various states such as crystals, amorphous solids, and liquids. Additionally, it offers customization options for intricate prediction tasks by incorporating user-provided data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 可高效处理各种材料的模拟，包括金属、氧化物、硫化物、卤化物及其晶体、非晶态固体、液态等不同物相。此外，它还通过整合用户提供的数据，为复杂预测任务提供自定义选项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get MatterSim model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 MatterSim 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research in collaboration with game studio Ninja Theory, Muse is a World and Human Action Model (WHAM) - a generative AI model of a video game that can generate game visuals, controller actions, or both. Trained exclusively on the game Bleeding Edge, researchers and game creatives can explore how these model capabilities will have potential to accelerate their creativity in the future.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Muse 由 Microsoft Research 与游戏工作室 Ninja Theory 联合开发，是一款世界与人类行为模型(WHAM) - 一种可生成游戏视觉对象和/或控制器操作的生成式 AI 模型。专门在《嗜血边缘》游戏上进行训练，研究人员和游戏创意者可探索这些模型能力在未来如何加速他们的创造力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseDownloadDemo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download demonstrator app]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载演示应用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A generative AI model that can generate visuals and controller actions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一种生成式 AI 模型，能够生成视觉效果和控制器操作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empowering game creatives with generative AI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过生成式 AI 为游戏创意赋能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Muse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Muse]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse was trained on more than 1 billion images and controller actions, from the game Bleeding Edge, corresponding to over 7 years of continuous human gameplay.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Muse 基于《嗜血边缘》游戏的 10 亿多张图像和控制器操作进行训练，数据量相当于人类玩家 7 年以上的持续游戏记录。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The WHAM demonstrator app provides a visual interface for interacting with a deployment of the Muse model instance on Azure AI Foundry. Creators can load a screenshot from Bleeding Edge as an initial prompt, then use the model to generate multiple potential continuations of gameplay from this starting point. They can then explore the generated sequences and tweak them, such as changing the controller inputs or pasting game elements into the scene and predicting what will happen as a result. These features demonstrate how Muse’s capabilities could someday enable AI-supported iteration and brainstorming as part of the creative process.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[WHAM 演示应用提供一个可视界面，用于与 Azure AI Foundry 上的 Muse 模型实例部署进行交互。创建者可加载《嗜血边缘》屏幕截图作为初始提示，然后使用模型从此起始点生成多个可能的游戏延续方案。然后，他们可探索生成的序列并进行调整，例如更改控制器输入或将游戏元素粘贴到场景中，并预测由此将发生的情况。这些功能表明，Muse 的能力未来如何有望将 AI 支持的迭代优化与头脑风暴融入创作流程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Muse]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Muse model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 Muse 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Focus on the grid of 9 AI generated gaming video frames by the Muse model set in the same scene. On the left, showing 3 ground truth (original) scenes that are compared to the AI generated frames.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*装饰性* 聚焦九宫格: Muse 模型在同场景中设置的 AI 生成游戏视频帧。左侧显示 3 个真实场景(原始场景)，与 AI 生成的帧进行比较。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by the Muse model set in the same scene. The frames highlight differences when the game player takes a left, center, or right path.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*装饰性* Muse 模型在同场景中设置的 AI 生成游戏视频帧九宫格。这些帧突出显示了游戏玩家选择左侧、中间或右侧路径时产生的差异。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by the Muse model crafted in the creator tool. This game creator surface showcases and Xbox controller that can be used to impact the AI generate frames.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*装饰性* 由 Muse 模型通过创建者工具精心制作的 AI 生成游戏视频帧的九宫格图形。该游戏创建者界面展示了一个可用于影响 AI 生成帧的 Xbox 控制器。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Navigation.NextArticleText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下一步]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Navigation.PreviousArticleText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Previous]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上一步]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhancing the ability of coding models to handle diverse editing requirements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增强编码模型处理多样化编辑需求的能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhancing the ability of coding models to handle diverse editing requirements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增强编码模型处理多样化编辑需求的能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover NextCoder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Software engineering activities frequently involve edits to existing code. However, contemporary code language models lack the ability to handle diverse types of code-edit requirements. In this work, Microsoft researchers attempt to overcome this shortcoming through a novel synthetic data generation pipeline and a robust model adaptation algorithm. Starting with seed code examples and diverse editing criteria, their pipeline generates high-quality samples comprising original and modified code, along with natural language instructions in different styles and verbosity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[软件工程活动通常涉及对现有代码的编辑。但是，现代代码语言模型缺乏处理多样化代码编辑需求的能力。在这项工作中，Microsoft 研究人员尝试通过新型合成数据生成管道和强大的模型适应算法来克服这一短板。其管道从种子代码示例和多样化编辑标准入手，生成包含原始代码与修改后代码的高质量样本，以及不同风格和详细程度的自然语言指令。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Today’s code LMs come bundled with strong abilities, such as code generation and instruction following, which should not be lost due to fine-tuning. To ensure this, researchers proposed a novel adaptation algorithm, SeleKT, that (a) leverages a dense gradient-based step to identify the weights that are most important for code editing, and (b) does a sparse projection onto the base model to avoid overfitting.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如今的代码 LM 具备强大的功能(如代码生成和指令跟随)，这些功能不应因微调而丧失。为此，研究人员提出新型适应算法 SeleKT，该算法(a)可利用基于密集梯度的步骤来识别对代码编辑最重要的权重; 并(b)对基础模型进行稀疏投影以避免过度拟合。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Using this approach, researchers obtained a new series of models called NextCoder (adapted from QwenCoder-2.5) that achieves strong results on five code-editing benchmarks, outperforming comparable size models and even several larger ones. In their research paper, they demonstrate the generality of their approach on two model families (DeepSeekCoder and QwenCoder), compare against other fine-tuning approaches, and demonstrate robustness by showing retention of code generation and general problem-solving abilities post adaptation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过此方法，研究人员获得了一系列名为 NextCoder 的新模型(改编自 QwenCoder-2.5)，这些模型在五个代码编辑基准测试中取得了优异的成绩，表现优于同等规模模型，甚至一些更大的模型。这些研究人员在其研究论文中展示了其方法在两个模型系列(DeepSeekCoder 和 QwenCoder)上的通用性，将这些方法与其他微调方法对比，并通过展示适应后保留代码生成和解决一般问题的能力来展示其可靠性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are now available for experimental purposes on Azure AI Foundry.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这些模型现在可用于Azure AI Foundry 上的实验性用途。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NextCoder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try NextCoder in Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Foundry 中试用 NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Parsed Teams screenshot image by OmniParser.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 OmniParser 解析的 Teams 屏幕截图图像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is an advanced vision-based screen parsing module that converts user interface (UI) screenshots into structured elements, allowing agents to execute actions across various applications using visual data . By harnessing large vision-language model capabilities, OmniParser improves both efficiency and accuracy in UI interactions. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 是一种先进的基于视觉的屏幕解析模块，它将用户界面(UI)屏幕截图转换为结构化元素，使智能体能够使用视觉数据在各种应用中执行操作。通过利用大型视觉语言模型的能力，OmniParser 提高了 UI 交互的效率和准确性。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Turn any LLM into a computer use agent ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将任何 LLM 转化为计算机操作智能体]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is a pioneering screen parsing module that transforms user interfaces into actionable elements through visual input. Discover how this innovative approach can enhance automated UI interactions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 是一种开创性的屏幕解析模块，通过视觉输入将用户界面转换为可操作元素。了解此创新方法如何增强自动化 UI 交互。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore OmniParser V2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 OmniParser V2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recent developments in large vision-language models (VLMs), such as GPT-4V and GPT-4o, showcase their potential in creating agent systems that integrate smoothly within user interfaces. However, the practical application of these multimodal models, especially as general agents across different operating systems, faces challenges. A significant barrier to progress has been the absence of reliable screen parsing techniques that can effectively identify interactable icons and link intended actions to specific screen regions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大型视觉语言模型(VLM)的最新发展，例如 GPT-4V 和 GPT-4o，展示了其在创建可在用户界面中无缝集成的智能体系统方面的潜力。然而，这些多模态模型的实际应用(尤其是作为不同操作系统中的通用智能体)面临着挑战。进展的一个重大障碍是缺乏可靠的屏幕解析技术，这些技术能够有效识别可交互图标，并将预期操作链接到特定屏幕区域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser addresses this limitation through its compact and powerful architecture. It transforms UI screenshots into structured output elements, enabling the design of agents that can perform precise actions across various applications. When combined with models like GPT-4V, OmniParser markedly improves the agent's capability to engage accurately with user interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 通过其紧凑而强大的体系结构突破了这一限制。它将 UI 屏幕截图转化为结构化输出元素，支持设计能够在各种应用中执行精准操作的智能体。当与 GPT-4V 等模型结合使用时，OmniParser 显著提高了智能体与用户界面准确互动的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser V2 takes this capability to the next level. Compared to its predecessor, It achieves higher accuracy in detecting smaller interactable elements and faster inference, making it a useful tool for GUI automation. In particular, OmniParser V2 is trained with larger size of interactive element detection data and icon functional caption data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser V2 将这一能力提升到新的水平。与前代版本相比，它在检测更小可交互元素时准确性更高，并且具备更快推理速度，使其成为 GUI 自动化的有用工具。具体而言，OmniParser V2 使用更大规模的交互元素检测数据和图标功能描述数据进行训练。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The creation of OmniParser involved the development of specialized datasets, including an interactable icon detection dataset that identifies actionable regions within popular web pages, and an icon description dataset that correlates UI elements with their functions. These resources are crucial for training the detection and captioning models utilized by OmniParser. The detection model, specifically fine-tuned on the interactable icon dataset, reliably locates actionable screen regions, while the captioning model provides contextually relevant descriptions for the detected elements.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 的创建涉及开发专用数据集，包括可交互图标检测数据集和图标描述数据集，前者识别常用网页中的可操作区域，后者将 UI 元素与其功能相关联。这些资源对于训练 OmniParser 使用的检测和字幕生成模型至关重要。检测模型特别针对可交互图标数据集进行微调，能够可靠地定位可操作的屏幕区域，而字幕生成模型则为检测到的元素提供上下文相关的描述。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is designed to be modular and adaptable, enhancing interactions across both PC and mobile platforms.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 采用模块化设计且具备可适配性，可增强电脑和移动平台之间的交互。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser V2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser V2]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open OmniParser repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 OmniParser 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A colorful geological map with various regions marked in different colors and a detailed legend in Chinese on the right side.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一幅彩色地质图，上面用不同的颜色标记了各个区域，右侧有详细的中文图例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE enhances multimodal large language models (MLLMs) with geologic expertise, enabling accurate interpretation of complex, high-resolution maps. By integrating structured extraction, domain knowledge, and reasoning, it supports critical tasks in disaster risk, resource discovery, and infrastructure planning—turning general AI into a specialized tool for geoscience.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 利用地质专业知识增强了多模式大语言模型(MLLM)，可准确解读复杂的高分辨率地图。通过整合结构化提取、领域知识以及推理能力，它可支持处理灾害风险、资源勘探及基础设施规划等关键任务，从而将生成式 AI 转变为面向地球科学领域的专业化工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE paves the way for advanced AI applications in geology, powering more efficient and accurate disaster detection, resource exploration, and civil engineering. Learn how PEACE transforms general-purpose multimodal LLMs into powerful domain-specific agents.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 为高级 AI 功能在地质学方面的应用开辟了道路，为实现更高效、精准的灾害监测、资源勘探和土木工程提供了支持。了解 PEACE 如何将通用多模式 LLM 转换为功能强大的特定领域的代理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore PEACE]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 PEACE]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE (emPowering gEologic mAp holistiC undErstanding) enhances multimodal large language models (MLLMs) for expert-level geologic map understanding. Geologic maps, which provide critical insights into the structure and composition of Earth’s subsurface and surface, are vital tools in disaster detection, resource exploration, and civil engineering. But their complexity—featuring high-resolution visuals, symbolic representations, and domain-specific knowledge—poses significant challenges for current AI models. General-purpose MLLMs often fall short when interpreting such data due to the intricacies of cartographic generalization and geoscientific reasoning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE (emPowering gEologic mAp holistiC undErstanding)增强了多模式大语言模型(MLLM)，以实现专家级的地质图理解能力。地质地图可提供有关地球地下和地表结构及组成的关键见解，是灾害检测、资源勘探以及土木工程领域中的重要工具。但其复杂性(包括高分辨率视觉对象、符号化表示形式以及特定领域的知识)给当前的 AI 模型带来了重大挑战。由于制图综合以及地球科学推理方面的复杂性，通用多模式大语言模型(MLLM)在解释此类数据时通常表现欠佳。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To bridge this gap, Microsoft researchers and collaborators introduced GeoMap-Bench, the first benchmark specifically designed to evaluate MLLMs across five capabilities essential to geologic map interpretation: extracting, referring, grounding, reasoning, and analyzing. They also developed GeoMap-Agent, an AI system tailored to these challenges.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了弥合这一差距，Microsoft 研究人员及其协作者引入了 GeoMap-Bench，这是首个旨在评估 MLLM 的基准检验，评估对象包括解释地质地图所必需的五项能力: 提取、引用、基础设置、推理和分析。他们还开发了针对这些挑战而定制的 AI 系统 - GeoMap-Agent。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GeoMap-Agent is composed of three key modules:]A;<ul><li><strong>Hierarchical Information Extraction (HIE)</strong> for parsing structured content from complex maps,</li><li><strong>Domain Knowledge Injection (DKI)</strong> for embedding geological expertise, and</li><li><strong>Prompt-enhanced Question Answering (PEQA)</strong> for improved interpretive and reasoning capabilities.</li></ul>Together, these modules enable GeoMap-Agent to outperform existing models with superior accuracy and depth in geologic tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GeoMap-Agent 由三个关键模块组成:]A;<ul><li><strong>分层信息提取(HIE)</strong>，用于分析复杂地图中的结构化内容、</li><li><strong>领域知识注入(DKI)</strong>，用于嵌入地质学专业知识，以及</li><li><strong>提示增强问答(PEQA)</strong>，用于改进解释和推理能力。</li></ul>借助这些协同工作的模块，GeoMap-Agent 在地质任务中能够以更高的准确性和深度超越现有模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rather than modifying MLLMs themselves, PEACE builds intelligent, domain-specific layers on top of them, turning general models into specialized agents capable of handling real-world geoscientific problems. This advancement marks a critical step toward applying AI in Earth science, empowering faster, more accurate geological assessments for both researchers and practitioners.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 并非对多模式大语言模型(MLLM)本身进行修改，而是基于这些模型构建特定于领域的智能层，从而将通用模型转变为能够处理现实地球科学问题的专业助理。这一进展标志了 AI 在地球科学领域应用的关键一步，可助力研究人员和从业者实现更快速、更准确的地质评估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open PEACE repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 PEACE 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 已发布用于研究目的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4BodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The text 'Phi-4' in glowing white letters on a purple and blue gradient background]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[紫色和蓝色渐变背景上以发光白色字母显示的文本 “Phi-4”]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4BodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Table showing Phi-4 benchmarks against comparable models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[表格显示了针对可比较模型的 Phi-4 基准检验]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore the capabilities of Phi-4, the latest model in Microsoft's Phi family of advanced AI technologies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Microsoft 先进 AI 技术 Phi 系列的最新模型 Phi-4 的功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4HomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore the capabilities of Phi-4, the latest model in Microsoft's Phi family of advanced AI technologies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Microsoft 先进 AI 技术 Phi 系列的最新模型 Phi-4 的功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4HomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Microsoft Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解 Microsoft Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal and Phi-4-mini, the newest models in Microsoft’s Phi family of small language models (SLMs) are now available. These models are designed to empower developers with advanced AI capabilities. Phi-4-multimodal, with its ability to process speech, vision, and text simultaneously, opens new possibilities for creating innovative and context-aware applications. Phi-4-mini, on the other hand, excels in text-based tasks, providing high accuracy and scalability in a compact form.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作为 Microsoft 的 Phi 系列小型语言模型(SLM)中的最新模型，Phi-4-multimodal 和 Phi-4-mini 现已发布。这些模型旨在为开发人员提供先进的 AI 功能。借助可同时处理语音、视觉和文本的功能，Phi-4-multimodal 为创建创新性和上下文感知应用程序提供了新的可能性。另一方面，Phi-4-mini 在基于文本的任务中表现优异，以紧凑的形式提供了高准确性和可伸缩性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal marks a new milestone in Microsoft’s AI development as our first multimodal language model. At the core of innovation lies continuous improvement, and that starts with listening to our customers. In direct response to customer feedback, we’ve developed Phi-4-multimodal, a 5.6B parameter model, that seamlessly integrates speech, vision, and text processing into a single, unified architecture. By leveraging advanced cross-modal learning techniques, this model enables more natural and context-aware interactions, allowing devices to understand and reason across multiple input modalities simultaneously. Whether interpreting spoken language, analyzing images, or processing textual information, it delivers highly efficient, low-latency inference—all while optimizing for on-device execution and reduced computational overhead. Natively built for multimodal experiences Phi-4-multimodal is a single model with mixture-of-LoRAs that includes speech, vision, and language, all processed simultaneously within the same representation space. The result is a single, unified model capable of handling text, audio, and visual inputs seamlessly—no need for complex pipelines or separate models for different modalities. The Phi-4-multimodal is built on a new architecture that enhances efficiency and scalability. It incorporates a larger vocabulary for improved processing, supports multilingual capabilities, and integrates language reasoning with multimodal inputs. All of this is achieved within a powerful, compact, highly efficient model that’s perfectly suited for deployment on devices and edge computing platforms. This breakthrough model represents a major leap forward in AI technology, offering unprecedented performance in a small package. Whether you’re looking for advanced AI capabilities on mobile devices or edge systems, Phi-4-multimodal provides a high-capability option that’s both efficient and versatile. With its impressive range of capabilities and flexibility, Phi-4-multimodal opens exciting new possibilities for app developers, businesses, and industries looking to harness the power of AI in innovative ways. The future of multimodal AI is here, and it’s ready to transform your applications. Phi-4-multimodal is capable of processing both visual and audio together. The following table shows the model quality when the input query for vision content is synthetic speech on chart/table understanding and document reasoning tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作为我们的首个多模式语言模型，Phi-4-multimodal 是 Microsoft AI 开发工作中的新的里程碑。创新的核心在于持续改进，这始于倾听客户的声音。为了直接响应客户反馈，我们开发了 Phi-4-multimodal，这是一个 56 亿参数模型，它将语音、视觉和文本处理无缝集成到一个统一的体系结构中。通过利用先进的跨模式学习技术，此模型可实现更自然和上下文感知的交互，从而支持设备同时理解和解释多个输入模式。无论是解释口语、分析图像还是处理文本信息，它都可以提供高效、低延迟的推理，同时针对设备端运行进行了优化，并降低了计算开销。Phi-4-multimodal 为通过多模式体验原生构建，它是一款集成混合式 LoRA 的单一模型，包括语音、视觉和语言，所有这些都在同一表示空间内同时处理。其成果是一个单一且统一的模型，能够无缝处理文本、音频和视觉输入，且无需针对不同模式使用复杂的管道或单独的模型。Phi-4-multimodal 基于全新的体系结构，能够提高效率和可伸缩性。它纳入了更大量的词汇来改进处理，支持多语言功能，并将语言推理与多模式输入相结合。所有这些都是在功能强大、紧凑、高效的模型中实现的，非常适合在设备和边缘计算平台上进行部署。这款突破性的模型代表了 AI 技术的重大飞跃，通过小型程序包提供了前所未有的性能。无论你是在移动设备还是边缘系统上寻求先进 的 AI 功能，Phi-4-multimodal 均可提供兼具效率和通用性的高性能选择。凭借令人印象深刻的广泛能力和灵活性，Phi-4-multimodal 为希望以创新方式利用 AI 功能的应用开发者、企业和行业带来了新的令人兴奋的可能性。多模式 AI 的未来已经到来，它已准备好革新你的应用程序。Phi-4-multimodal 能够同时处理视觉和音频。下表显示了在图表/表格理解和文档推理任务中，当视觉内容的输入查询为图合成语音时的模型质量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal is capable of processing both visual and audio together. The following table shows the model quality when the input query for vision content is synthetic speech on chart/table understanding and document reasoning tasks. Compared to other existing state-of-the-art omni models that can enable audio and visual signals as input, Phi-4-multimodal achieves much stronger performance on multiple benchmarks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-multimodal 能够同时处理视觉和音频。下表显示了在图表/表格理解和文档推理任务中，当视觉内容的输入查询为图合成语音时的模型质量。与其他现有能够将音频和视觉信号作为输入的最先进全能模型相比，Phi-4-multimodal 在多个基准检验中均实现了更加出色的性能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-mini is a 3.8B parameter model and a dense, decoder-only transformer featuring grouped-query attention, 200,000 vocabulary, and shared input-output embeddings, designed for speed and efficiency. Despite its compact size, it continues outperforming larger models in text-based tasks, including reasoning, math, coding, instruction-following, and function-calling. Supporting sequences up to 128,000 tokens, it delivers high accuracy and scalability, making it a powerful solution for advanced AI applications. Function calling, instruction following, long context, and reasoning are powerful capabilities that enable small language models like Phi-4-mini to access external knowledge and functionality despite their limited capacity. Through a standardized protocol, function calling allows the model to seamlessly integrate with structured programming interfaces. When a user makes a request, Phi-4-Mini can reason through the query, identify and call relevant functions with appropriate parameters, receive the function outputs, and incorporate those results into its responses. This creates an extensible agentic-based system where the model’s capabilities can be enhanced by connecting it to external tools, application program interfaces (APIs), and data sources through well-defined function interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-mini 是一款 38 亿参数密集型纯解码器 Transformer 模型，具备分组查询注意力、200,000 词汇与共享输入输出嵌入，专为实现速度与效率而设计。尽管体积小巧，但它在基于文本的任务方面持续领先于更大的模型，其中包括推理、数学、编码、指令跟随和函数调用。它支持最多 128,000 个令牌的序列，可提供高准确性和可伸缩性，从而成为面向先进 AI 应用程序的强大解决方案。尽管容量有限，但强大的函数调用、指令跟随、长上下文和推理功能可支持 Phi-4-mini 等小型语言模型访问外部知识和功能。通过标准化协议，函数调用允许模型与结构化编程接口无缝集成。当用户发出请求时，Phi-4-Mini 可以通过查询进行推理，使用适当的参数标识和调用相关函数，接收函数输出，并将这些结果纳入到其响应中。这将创建一个基于代理的可扩展系统，通过定义完善的函数接口将其连接到外部工具、应用程序接口(API)和数据源，可以增强模型的功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are designed to handle complex tasks efficiently, making them ideal for edge case scenarios and compute-constrained environments. Given the new capabilities Phi-4-multimodal and Phi-4-mini bring, the uses of Phi are only expanding. Phi models are being embedded into AI ecosystems and used to explore various use cases across industries.]A;]A;<strong>Embedded directly to your smart device:</strong> Integrating Phi-4-multimodal directly into a smartphone could enable smartphones to process and understand voice commands, recognize images, and interpret text seamlessly. Users could benefit from advanced features like real-time language translation, enhanced photo and video analysis, and intelligent personal assistants that understand and respond to complex queries. This would elevate the user experience by providing powerful AI capabilities directly on the device, ensuring low latency and high efficiency.]A;]A;<strong>On the road:</strong> Imagine an automotive company integrating Phi-4-multimodal into their in-car assistant systems. The model could enable vehicles to understand and respond to voice commands, recognize driver gestures, and analyze visual inputs from cameras. For instance, it could enhance driver safety by detecting drowsiness through facial recognition and providing real-time alerts. Additionally, it could offer seamless navigation assistance, interpret road signs, and provide contextual information, creating a more intuitive and safer driving experience while connected to the cloud and offline when connectivity isn't available.]A;]A;<strong>Multilingual financial services:</strong> Imagine a financial services company integrating Phi-4-mini to automate complex financial calculations, generate detailed reports, and translate financial documents into multiple languages. For instance, the model can assist analysts by performing intricate mathematical computations required for risk assessments, portfolio management, and financial forecasting. Additionally, it can translate financial statements, regulatory documents, and client communications into various languages and could improve client relations globally.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这些模型旨在高效处理复杂任务，使其非常适合边缘事例方案和受到计算约束的环境。鉴于 Phi-4-multimodal 和 Phi-4-mini 带来的新功能，Phi 正在不断拓展应用领域。Phi 模型正在嵌入到 AI 生态系统中，并被用于跨行业探索各种用例。]A;]A;直接嵌入到智能设备: 通过将 Phi-4-multimodal 直接集成到智能手机，可支持智能手机无缝处理和理解语音命令、识别图像和解读文本<strong></strong>。用户可以受益于实时语言翻译、增强的照片和视频分析，以及理解和响应复杂查询的智能个人助理等高级功能。通过直接在设备上提供强大的 AI 功能，这会提升用户体验，确保低延迟和高效率。]A;]A;道路交通: 假设一家汽车公司将 Phi-4-multimodal 集成到其车内助手系统中<strong></strong>。借助该模型，车辆能够理解和响应语音命令、识别驾驶员手势，以及分析摄像头中的视觉输入。例如，通过面部识别检测驾驶员困倦状态并提供实时警报，它可以提高驾驶员的行车安全。此外，无论是连接到云还是无法连接的脱机情况下，它都能够提供无缝的导航协助、解释路标和提供上下文信息，从而提供更直观和更安全的驾驶体验。]A;]A;多语言金融服务: 假设一家金融服务公司集成 Phi-4-mini 来自动执行复杂的财务计算、生成详细报表，并将财务文档翻译为多种语言<strong></strong>。例如，模型可以通过执行风险评估、项目组合管理和财务预测所需的复杂数学计算来帮助分析师开展工作。此外，它可以将财务报表、法规文档和客户通信翻译为各种语言，并可在全球范围内改善客户关系。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4TryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bar chart comparing agent performance by ML task complexity between AIDE and Project Amelie (powered by R&D-Agent). The chart shows performance metrics (%) across different complexity levels (Low/Lite, Medium, High) with Project Amelie achieving 22.4% overall performance compared to AIDE's 16.9%. Project Amelie shows improved performance across all complexity categories, with the most significant improvement in the Low/Lite category represented by a light blue section at the bottom of each bar.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按 ML 任务复杂性比较 AIDE 和 Project Amelie (由 R&D-Agent 提供支持)的代理性能的条形图。图表显示了不同复杂性级别(低/精简、中、高)的性能指标(%)，其中 Project Amelie 的总体性能为 22.4%，而 AIDE 为 16.9%。Project Amelie 在所有复杂性类别中均表现出性能提升，尤其是在低/精简类别中(由每个竖条底部的浅蓝色部分表示)提升显著。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A demo of using the Project Amelie agent to predict accommodation rental prices in Seattle. The Agent builds a regression model, answers questions about it, and provides code to run the model. The user then opens the code in VS Code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Project Amelie 代理预测西雅图的租赁价格的演示。该代理会生成一个回归模型，回答有关这方面的问题，并提供代码来运行该模型。然后，用户在 VS Code 中打开代码。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[With Project Amelie, we are unveiling our first Foundry autonomous agent that can perform machine learning engineering tasks. ML teams can use the agent to initiate complex machine learning tasks using prompts —such as, “Help me create a model to predict customer churn"—and receive fully validated ML pipelines, detailed evaluation metrics, trained model, and ready-to-use, reproducible Python code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过 Project Amelie，我们将推出第一个能够执行机器学习工程任务的 Foundry 自治代理。机器学习团队可以使用该代理通过提示(例如“帮助我创建一个预测客户流失情况的模型”)启动复杂的机器学习任务，并接收经过完全验证的 ML 管道、详细的评估指标、训练好的模型以及可重复使用的现成 Python 代码。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie is our first Foundry autonomous agent built in collaboration with Microsoft Research that can perform machine learning engineering tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 是我们与 Microsoft Research 合作构建的第一个 Foundry 自主代理，可以执行机器学习工程任务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Project Amelie]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Project Amelie]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieSignUpLink1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign up for Private Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注册个人预览版]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieSignUpLink2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[sign up here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此处注册]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{SignUpLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{SignUpLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<em>Private Preview Coming Soon! Sign up to get early access and opportunity to share feedback. Approved users can explore and experiment with Project Amelie.</em>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<em>即将推出个人预览版!注册即可抢先体验并有机会提供反馈。获得批准的用户可以探索和试用 Project Amelie。</em>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[At its core, Project Amelie is powered by innovation from Microsoft Research designed specifically to automate and optimize research and development processes in machine learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从其核心来看，Project Amelie 依托于 Microsoft Research 专门为自动执行和优化机器学习中的研发过程而进行的创新。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie outperforms the current state of the art benchmarks on MLE-Bench by OpenAI, which measures MLE agent’s effectiveness on real world ML engineering tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 超越了 OpenAI 的 MLE-Bench 上当前最先进的基准，后者用于衡量 MLE 代理在现实世界的 ML 工程任务中的有效性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are currently in early beta and plan to launch Private Preview soon! If you are interested in getting early access to Project Amelie and sharing your insights to help shape the product, please {SignUpLink}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们目前处于早期测试阶段，并计划很快推出个人预览版!如果你有兴趣抢先体验 Project Amelie 并分享你的见解以帮助塑造产品，请{SignUpLink}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A healthcare professional in scrubs sits with an older adult in a care facility, holding a tablet displaying a meal. In the foreground, a smartphone screen shows the ReMe app interface with options for game training, user feedback, and starting a conversation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在一家护理机构里，一位穿着医护服的医疗专业人员坐在一位老人身边，手中拿着一台显示餐食的平板电脑。在前景中，智能手机屏幕显示了 ReMe 应用的界面，界面中提供游戏训练、用户反馈和开始对话的选项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe is a web-based framework that helps researchers create AI chatbots for personalized training and interventions aimed at strengthening memory and cognitive functions. Early evaluations show its potential to contribute to digital health innovation and advance non-pharmacological approaches to cognitive health.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 是一个基于 Web 的框架，可帮助研究人员创建 AI 聊天机器人，以开展个性化训练和实施干预，旨在增强记忆力和认知功能。早期评估显示了它在为数字健康创新做出贡献，以及推进针对认知健康的非药物方法发展方面的潜力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe enables scalable, personalized approaches to cognitive health—a growing need that affects millions. Explore this web-based framework, which puts powerful AI-enabled research tools in the hands of scientists and clinicians.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 实现了一种可缩放的个性化方法，可满足不断增长的对认知健康的需求，惠及数以百万计的人群。探索此基于 Web 的框架，了解该框架为科学家和临床医生提供的 AI 支持的强大研究工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore ReMe]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 ReMe]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe is a web-based framework designed to accelerate research into personalized cognitive training using AI chatbots. As cognitive decline becomes a growing public health concern, ReMe supports researchers, clinicians, and caregivers in developing interactive training tasks focused on episodic memory and open-ended cognitive challenges.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 是一个基于 Web 的框架，旨在使用 AI 聊天机器人加速对个性化认知训练的研究。随着认知衰退成为日益受到关注的公共健康问题，ReMe 将支持研究人员、临床医生以及护理人员开发专注于情景记忆和开放式认知挑战的交互式训练任务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The framework integrates a puzzle engine, a life-logging module for personal memory recall, and a multimodal training interface featuring text, image, and voice capabilities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[该框架集成了解谜引擎、用于个人记忆唤起的生活日志记录模块，以及具备文本、图像和语音功能的多模式训练界面。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cognitive training is one of the few non-pharmacological methods shown to help delay decline, but existing programs are often generic and not very engaging. ReMe aims to make cognitive training more personalized and adaptable while reaching more people at lower cost.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[认知训练是少数已被证明有助于延缓衰老的非药物方法之一，但现有程序通常过于通用化，且缺乏吸引力。ReMe 旨在提高认知训练的个性化程度和适应性，同时以更低成本惠及更多人。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instead of building every new cognitive training chatbot from scratch, researchers can use ReMe to prototype, test, and improve interventions more quickly, speeding up discovery of what works.  ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[研究人员可以使用 ReMe 更快地创建原型、进行测试和改进干预，从而加快发现有效的工作方法，而不是从头开始构建每个新的认知训练聊天机器人。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In initial evaluations, ReMe was well received by users citing strong conversational fluency and moderate difficulty. While not intended for clinical treatment, ReMe provides a valuable tool for exploring AI’s role in supporting cognitive health, paving the way for future innovations in personalized digital therapies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在初步评估中，ReMe 受到用户的好评，认为其具有很高的对话流畅性，并且难度适中。虽然 ReMe 并非用于临床治疗，但它为探索 AI 在支持认知健康方面的作用提供了有价值的工具，为将来在个性化数字疗法方面的创新铺平了道路。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open ReMe repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 ReMe 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 已发布用于研究目的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chemical structures and colored markers on a scatter plot.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[散点图上的化学结构和彩色标记。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen is a transformer-based chemical language model for developing target-specific drug compounds. Research shows that TamGen can also optimize existing molecules by designing target-aware molecule fragments, potentially enabling the discovery of novel compounds that build on a known molecular core structure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen 是一种基于转换器的化学语言模型，用于开发靶点特异性药物化合物。研究表明，TamGen 还可通过设计靶点感知型分子片段来优化现有分子，从而可能能够基于已知分子核心结构发现新型化合物。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover how AI could accelerate the process of pharmaceutical discovery, leading to faster medical breakthroughs and improved treatment options.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 AI 如何加速药物发现过程，推动医学突破提速并拓展创新治疗方案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover TamGen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 TamGen]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generative AI is opening new avenues for scientific exploration by allowing computers to autonomously learn and produce original content. TamGen offers a new approach to drug discovery by applying the principles of generative AI to molecular design. Unlike traditional methods, which depend on systematically screening known compounds—a process that is long, complex, and costly due to its reliance on empirical knowledge and the time-consuming task of exploring a vast chemical library—generative AI provides opportunities for designing entirely new chemical structures.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成式 AI 通过支持计算机自主学习和生成原创内容，为科学探索开辟全新路径。TamGen 通过将生成式 AI 原则应用于分子设计，提供了一种新的药物发现方法。传统方法依赖于系统筛选已知化合物，这一过程因依赖经验知识且需耗时探索庞大化合物库而漫长、复杂且昂贵，而生成式 AI 提供了设计全新化学结构的机会。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen showcases the transformative potential of generative AI in drug design, combining advanced molecular modeling with researcher-AI collaboration. Tasks that once took years could now be accomplished in a fraction of the time. This research underscores AI’s expanding role in drug discovery and its promise for developing effective treatments against persistent infectious diseases like tuberculosis.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen 展示了生成式 AI 在药物设计中的变革性潜力，将先进的分子建模与研究人员-AI 协作相结合。曾经需要数年才能完成的任务，现在只需极短时间即可达成。这项研究凸显了 AI 在药物发现领域日益扩大的作用，及其针对结核病等顽固性传染病开发有效疗法的巨大潜力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get TamGen model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 TamGen 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis creates  high-quality 3D assets from simple text or image inputs. Using a unified latent space (SLAT), it delivers detailed, textured 3D models in formats like meshes,radiance fields, and 3D Gaussians. Its flexibility, editing capabilities, and superior quality enable faster, more adaptable workflows in gaming, virtual worlds, industrial design, and beyond.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 可以根据简单的文本或图像输入创建高质量的 3D 资产。它使用统一的潜在空间(SLAT)，以网格、辐射场和 3D 高斯等格式提供带纹理的详细 3D 模型。其灵活性、编辑功能和卓越质量可在游戏、虚拟世界、工业设计等领域实现更快、适用性更高的工作流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis is an AI model that generates high-quality 3D assets from text or image prompts in formats like meshes, radiance fields, and 3D Gaussians. Discover how it uses Structured LATents (SLAT) to fuse sparse 3D grids with dense visual features—powering easy and creative 3D content creation in gaming, AR/VR, design, and simulation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 是一种 AI 模型，可以根据文本或图像提示生成高质量的 3D 资产，格式包括网格、辐射场和 3D 高斯。了解它如何使用结构化 LATent (SLAT) 将稀疏 3D 网格与密集的视觉功能融合在一起 - 支持在游戏、AR/VR、设计和模拟领域创建简单而有创意的 3D 内容。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Trellis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Trellis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis provides a powerful foundation for scalable, AI-driven content creation. Built to meet rapidly growing industrial demand, Trellis creates high-quality, editable 3D assets from simple text or image prompts in lieu of manual modeling. This saves time, lowers barriers, and unlocks new possibilities for developers, designers, and digital content creators.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 为创建可缩放的 AI 驱动内容创建提供了强大的基础。Trellis 旨在满足快速增长的行业需求，它替代手动建模，根据简单的文本或图像提示创建高质量的可编辑 3D 资产。这将节省时间，降低门槛，并为开发人员、设计师和数字内容创作者开启新的可能性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis is built on a novel Structured LATent (SLat) representation that captures both geometric structure and visual detail in a compact, editable form. Trained on 500,000 diverse 3D objects using rectified flow transformers with up to 2 billion parameters, Trellis significantly improves both quality and flexibility compared to existing models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 基于新式的结构化 LATent (SLat) 表示形式，能够以紧凑、可编辑的形式捕获几何结构和视觉细节。通过使用具有多达 20 亿个参数的修正流转换器基于 500,000 个多样化的 3D 对象进行了训练，Trellis 相较于现有模型，显著提高了质量和灵活性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike traditional methods that target a single output type or require labor-intensive setup, Trellis can generate a 3D asset in multiple formats, including meshes, 3D gaussians, and radiance fields. This makes it compatible with different rendering pipelines and applications. The generated models feature detailed structure and rich texture, enabling their direct use in games, AR/VR experiences, digital twins, simulation environments, and product visualization.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与针对单个输出类型或需要人工密集型设置的传统方法不同，Trellis 可以生成多种格式的 3D 资产，包括网格、三维高斯和辐射场。这使得它与不同的渲染管道和应用程序兼容。生成的模型具有详细的结构和丰富的纹理，因此能够直接在游戏、AR/VR 体验、数字孪生、模拟环境和产品可视化中使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis also allows for prompt-guided local edits—such as removing, replacing, or adding parts of a 3D model—without retraining or manual sculpting, which dramatically accelerates iteration and customization. Its design eliminates the need for costly 3D fitting and leverages pretrained vision models for high-fidelity results, even when working with sparse 3D data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 还允许进行提示引导的本地编辑(例如移除、替换或添加 3D 模型的部件)，而无需重新训练或手动雕刻，这大大加快了迭代和定制的速度。其设计消除了对成本高昂的 3D 拟合的需求，并利用预先训练的视觉模型来实现高保真效果，即使在处理稀疏 3D 数据时也是如此。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Trellis repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 Trellis 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TryOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{modelName} has been released for research purposes. Users can learn, explore and experiment with {modelName}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{modelName} 已发布用于研究目的。用户可学习、探索和试用 {modelName}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A demonstration of using the TypeAgent shell navigating and interacting with a visually rich Paleobiology Database (paleodb) website, highlighting its ability to process and act on complex web interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[演示了如何使用 TypeAgent shell 导航视觉元素丰富的古生物学数据库(paleodb)网站并与之交互，强调了其处理复杂 Web 接口的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An demonstration of the TypeAgent Shell where a user converses with the agent about events and entities extracted months earlier. The gif shows entities being retrieved from long-term memory into the conversation, enabling the user to take actions on them.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent Shell 的演示，展示的是用户与代理讨论几个月前提取的事件和实体。该 GIF 显示了从长期记忆检索到对话中的实体，这样用户便能够对它们采取行动。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent is sample code that explores an architecture for building a single personal agent with natural language interfaces leveraging current advances in LLM technology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 是一段示例代码，它探索了一种利用 LLM 技术的最新进展构建具有多种自然语言界面的个人代理的体系结构。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent is sample code that explores an architecture for building a single personal agent with natural language interfaces leveraging current advances in LLM technology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 是一段示例代码，它探索了一种利用 LLM 技术的最新进展构建具有多种自然语言界面的个人代理的体系结构。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover TypeAgent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 TypeAgent]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The goal of the TypeAgent team is to explore how to get work done by safely and efficiently combining stochastic systems like language models with traditional software components. Three principles have emerged during this investigation. They are listed below along with examples of how the principles apply to actions, memory and plans.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 团队的目标是探索如何通过安全高效地将语言模型等随机系统与传统软件组件相结合来完成工作。在此调查过程中，出现了三个原则。以下列出了这些原则及其在操作、记忆和计划中的体现的示例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<ul><li><strong>Principle:</strong> distilling models into logical structures</li><ul><li>Actions: find translation patterns and replace some model calls by applying patterns</li><li>Memory: build ontologies from text</li><li>Plans: people, programs and models collaborate using “tree of thought”</li></ul><br><li><strong>Principle:</strong> control information density</li><ul><li>Actions: applications define discrete categories with dense descriptions of action sets</li><li>Memory: tight semantic structures fit into attention budget</li><li>Plans: each search tree node defines a focused sub-problem</li></ul><br><li><strong>Principle:</strong> use logical structures to enable collaboration</li><ul><li>Actions: humans decide how to disambiguate action requests</li><li>Memory: simple models extract logical structure from text</li><li>Plans: quality models, advantage models, language models, humans and programs collaborate to expand each best-first-search node</li></ul>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<ul><li>原则:<strong></strong> 将模型提炼为逻辑结构</li><ul><li>操作: 查找翻译模式，并通过应用模式替换某些模型调用</li><li>记忆: 基于文本构建本体</li><li>计划: 用户、程序和模型通过“思维树”进行协作</li></ul><br><li>原则:<strong></strong> 控制信息密度</li><ul><li>操作: 应用程序定义具有密集操作集描述的离散类别</li><li>记忆: 紧凑的语义结构符合注意力预算</li><li>计划: 每个搜索树节点定义一个聚焦的子问题</li></ul><br><li>原则:<strong></strong> 使用逻辑结构来支持协作</li><ul><li>操作: 人类决定如何消除操作请求的歧义</li><li>记忆: 简单模型从文本中提取逻辑结构</li><li>计划: 质量模型、优势模型、语言模型、人类和程序协作，以扩展每个最佳优先搜索节点</li></ul>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are trying to build a single personal agent that can apply to any application.  To apply agent interfaces to all applications, we need to map user requests to actions at much lower cost and latency than current systems. To make this possible, we have created a system that can distill language models into logical systems that can handle most user requests.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们正在尝试构建一个可供任何应用程序使用的个人代理。 为了将代理接口应用于所有应用程序，我们需要以比远低于当前系统的成本和延迟将用户请求映射到操作。为此，我们创建了一个系统，用于将语言模型提炼为能够处理大多数用户请求的逻辑系统。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Figure 1: The TypeAgent shell example navigating a visually rich {paleodbLink} website.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图 1: TypeAgent shell 示例，用于导航视觉元素丰富的 {paleodbLink} 网站。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We use LLMS with structured prompts to extract a logical representation of actions on a page (e.g. buy product). This logical schema is the same across multiple sites, even if the sites have different HTML and JS implementations. We demonstrate the power of this approach by building automation to interact with multiple crossword sites and multiple e-commerce sites using consistent logical schema.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们使用带有结构化提示的 LLMS 提取页面上操作(例如购买产品)的逻辑表示。此逻辑架构在多个站点中是相同的，即使这些站点具有不同的 HTML 和 JS 实现也是如此。我们通过实现自动化来使用一致的逻辑架构与多个填字游戏网站和多个电子商务网站进行交互，从而展示了这种方法的强大之处。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are trying to create human-like memory with super-human precision and recall for agent conversations. We are using a new indexing and query processing approach called <strong>Structured RAG</strong> as the basis for agent memory. Structured RAG does substantially better than Classic RAG at answering questions about past conversations such as "what were the books we talked about?" and "what step were we on in building the photo montage?"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们正在尝试打造类似人类所拥有的记忆力，但这种记忆力的准确度高于人类，且可用于回忆代理对话。我们将使用一种名为“结构化 RAG”的新索引和查询处理方法作为代理记忆力的基础<strong></strong>。结构化 RAG 在回答有关过去对话的问题时明显优于经典 RAG，例如“我们讨论的书籍是什么?”和“我们进行到了制作照片蒙太奇的哪个步骤?”]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Figure 2: Here using the experimental TypeAgent Shell a user can have conversations with the agent about events and entities that were extracted months ago. Entities are pulled from long-term memory into the conversation memory and user can then take actions on the entities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图 2: 在这里，使用实验性的 TypeAgent Shell，用户可以与代理就几个月前提取的事件和实体进行对话。将实体从长期记忆提取到对话记忆中后，用户可以对实体采取行动。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText8" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Actions and memories flow together. Actions like "add to my calendar pickleball game 2-3pm on Friday" yield memories that can become parameters of future actions like "put in an hour of recovery time after my pickleball game." We are working on an architecture, <strong>AMP</strong>, that enables this natural information flow by integrating actions, memories, and plans. We are applying AMP to the web by creating a browser that enables web sites to register actions through a JavaScript interface.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[操作与记忆相互交融。像“将周五下午 2-3 点的匹克球比赛添加到我的日历”这样的操作会产生记忆，这些记忆可以成为未来操作的参数，比如“在匹克球比赛后用一个小时的时间进行恢复”。我们正在努力构建一种名为 "AMP" 的体系结构，<strong></strong>通过整合操作、记忆和计划来实现这种自然的信息流。我们正在通过创建一个允许网站通过 JavaScript 接口注册操作的浏览器，将 AMP 应用到 Web。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open TypeAgent repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 TypeAgent 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A diagram showing the process of generating a 3D talking head animation from a single image using VASA-3D. The steps include creating VASA-1 videos from the image, building a VASA-3D model, and combining it with an audio clip and optional control signals to produce various animated outputs of the subject.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图中展示了使用 VASA-3D 根据一张图像生成会说话的 3D 头部动画的过程。步骤包括根据图像制作 VASA-1 视频、构建 VASA-3D 模型，并将其与音频剪辑片段和可选控制信号结合，从而生成相应主题的各种动画输出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D exemplifies how generative AI can enhance human-computer interaction by making expressive, customizable 3D avatars accessible from minimal input. Extending VASA-1's motion latent into 3D and optimizing with synthetic multiview data, it opens new frontiers in communication, immersion, and assistive technology—setting a new standard for realism and responsiveness in avatar generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 证实了生成式 AI 如何通过创建仅需极少输入即可访问的富有表现力且可自定义的 3D 虚拟形象来增强人机交互体验。通过将 VASA-1 的动作潜变量扩展到 3D，并利用合成的多视角数据进行优化，VASA-3D 开辟了通信、沉浸式体验以及辅助技术领域的新前沿，为实现虚拟形象生成方面的真实感和响应能力设定了新的标准。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D turns a single portrait and speech audio into a lifelike 3D talking head using a novel motion-latent-driven model. Discover how it enables real-time, expressive, and multiview-consistent avatars for education, collaboration, and immersive experiences.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 利用新颖的动作潜变量驱动模型，可将单个肖像和语音音频转化为逼真的会说话的 3D 头部模型。探索它如何为教育、协作和沉浸式体验实现实时、富有表现力且多视角一致的虚拟形象。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore VASA-3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 VASA-3D]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D is a major step forward in 3D avatar generation, producing realistic, expressive, and multiview-consistent 3D talking heads from just a single image and speech audio. The system builds on VASA-1, which introduced high-fidelity 2D talking head synthesis through a richly expressive motion latent. VASA-3D extends this capability into 3D by conditioning a neural 3D head model on the motion latent, capturing nuanced facial expressions and natural head motion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 是 3D 虚拟形象生成领域的重大进步，仅通过单个图像和语音音频即可生成逼真、富有表现力且多视角一致的会说话的 3D 头部。该系统基于 VASA-1 构建，后者通过极富表现力的运动潜变量实现了高保真 2D 会说话头像合成。通过根据运动潜变量调节神经网络 3D 头像模型，VASA-3D 将此功能扩展到 3D，可捕获细微的面部表情和自然的头部运用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To personalize the 3D head from a single portrait, VASA-3D creates additional views of the face from different angles and uses them to fine-tune the 3D model, even if some of those views have visual flaws or limited variety. The result is a model capable of real-time generation at 75 frames per second with just 65 milliseconds latency, supporting free-view rendering, emotional control, and high-quality animation, even from stylized or artistic portraits.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了根据单人肖像对 3D 头部模型进行个性化处理，VASA-3D 可从不同角度生成额外的人脸视图，并利用这些视图对 3D 模型进行微调，即使其中一些视图存在视觉缺陷或多样性有限的问题。其结果是模型能够以每秒 75 的速度实时生成，且延迟仅为 65 毫秒，并支持自由视图渲染、情感控制和高质量动画，甚至支持根据风格化或艺术化肖像进行处理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D significantly outperforms prior methods on realism and expressiveness in both audio- and video-driven talking head tasks. Its broad potential spans virtual collaboration (AI coworkers), education (AI tutors), entertainment, and neuroscience research. Early applications include VR-based social interaction, memory activation studies, and adaptive learning tools. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在同时由音频和视频驱动的会说话的头部模型任务中，VASA-3D 在真实感和表现力方面显著优于先前的方法。VASA-3D 具有广泛的潜在应用领域，涵盖了虚拟协作(如 AI 同事)、教育(如 AI 导师)、娱乐以及神经科学研究等方面。早期应用包括基于 VR 的社交互动、记忆激活研究以及自适应学习工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Responsible AI is integral to VASA-3D’s development. To prevent misuse, the model and APIs are not publicly released. Face forgery detection systems trained on VASA-3D outputs can reliably distinguish synthetic content, enhancing safety and model robustness.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[负责任 AI 是 VASA-3D 开发工作中不可或缺的一部分。为了防止滥用，模型和 API 不会公开发布。基于 VASA-3D 输出训练的人脸伪造检测系统可以可靠地识别合成内容，从而增强安全性和模型可靠性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D exemplifies how advanced generative AI can enhance human-computer interaction. By making expressive, customizable 3D avatars accessible from minimal input, it opens new frontiers in communication, immersion, and assistive technology—setting a new standard for realism and responsiveness in avatar generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 展示了高级生成式 AI 如何增强人机交互。通过生成仅需极少输入即可访问的富有表现力且可自定义的 3D 虚拟形象，VASA-3D 开辟了通信、沉浸式体验以及辅助技术领域的新前沿，为虚拟形象生成方面的真实感和响应能力设定了新的标准。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Vasa-3D repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 Vasa-3D 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA3D has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA3D 已发布用于研究目的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a framework for generating realistic, long-form, multi-speaker dialogue from transcripts, making it ideal for podcasts, voiceovers, and narrative audio. Unlike typical TTS tools, VibePod handles up to four speakers across 30-minute sessions with strong speaker consistency, natural pacing, and turn-taking.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一个用于根据脚本生成逼真、长篇的多说话人对话的框架，非常适合用于播客、画外音和叙述性音频。与典型的 TTS 工具不同，VibePod 可在 30 分钟的会话中支持最多四个说话人，在说话人一致性、自然节奏和轮流发言方面表现出色。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodHomeBlurb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a multi-speaker audio generation framework for creating long-form, realistic dialogue from transcripts. It’s ideal for podcasts and voiceovers, with strong performance in pacing, coherence, and speaker dynamics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一种多说话人音频生成框架，用于根据脚本创建长篇、逼真的对话。它非常适合用于播客和画外音，在节奏、连贯性和说话人动态方面表现出色。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore VibePod]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 VibePod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a new framework designed to generate realistic, multi-speaker audio content from transcripts. Ideal for producing podcasts, voiceovers, and other narrative formats, it outperforms conventional text-to-speech (TTS) tools, which are unable to produce long-form, coherent, and interactive multi-speaker dialogue. VibePod supports up to four distinct voices in 30-minute segments. With its improved pacing, turn-taking, and speaker consistency, VibePod rated highly in user testing for spontaneity and realism.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一个新框架，旨在根据脚本生成逼真的多说话人音频内容。它非常适合制作播客、画外音和其他叙述性格式，性能优于传统的文本转语音(TTS)工具，这些工具无法生成长篇、连贯且互动的多说话人对话。VibePod 在 30 分钟的片段中支持最多四种不同的嗓音。凭借其改进的节奏、轮流发言和说话人一致性，VibePod 在用户测试中因其自发性和真实感而获得高评价。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike systems focused on voice cloning, VibePod emphasizes dialogue quality over personalization. It was trained on publicly available and synthetically generated datasets, with safeguards built in to prevent misuse. It does not allow custom voice uploads, reflecting {ResponsibleAIPrinciples}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与专注于语音克隆的系统不同，VibePod 强调对话质量而不是个性化。它基于公开可用和合成生成的数据集进行了训练，内置了防止滥用的安全措施。它不允许定制声音，这反映了 {ResponsibleAIPrinciples}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText2Link" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft’s Responsible AI principles]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的负责任 AI 原则]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To promote research, transparency, and responsible use, VibePod is being released on Hugging Face under the MIT License. Open-sourcing the technology invites collaboration from the broader speech synthesis community. Future enhancements will include multilingual support and controls for emotional tone, expanding its creative potential.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了促进研究、透明度和负责任的使用，VibePod 将在 MIT 许可证下发布于 Hugging Face。将技术开源需要邀请更广泛的语音合成社区进行协作。未来的增强功能将包括多语言支持和情感语气控制，从而扩展其创意潜力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.[object Object]" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops foundational image generation and editing models which are both fast and highly performant. These include FLUX.1 Kontext [pro]5D; and FLUX1.1 [pro]5D;.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开发既快速又高性能的基础图像生成和编辑模型。其中包括 FLUX.1 Kontext [pro]5D; 和 FLUX1.1 [pro]5D;。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.ai21" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops advanced language models like Jurassic-2 for complex text generation and understanding tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开发先进的语言模型(如 Jurassic-2)，用于复杂的文本生成和理解任务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.aoai" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft-hosted OpenAI models, including GPT-4 and Codex, offering enterprise-grade security and compliance.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 托管的 OpenAI 模型，包括 GPT-4 和 Codex，可提供企业级安全性和合规性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.bayer" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Leverages AI for advancements in healthcare and agriculture, focusing on research and development.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[利用 AI 在医疗保健和农业领域取得进展，专注于研究和开发。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.blackforestlabs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Amazing AI models from the Black Forest]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[来自黑森林的令人惊叹的 AI 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.bria" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops generative AI models for visual content creation, including images and videos.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开发用于视觉内容创建(包括图像和视频)的生成式 AI 模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.cerence" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides AI-powered voice and speech recognition solutions for automotive and mobility applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为汽车和移动性应用提供 AI 支持的语音和语音识别解决方案。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.cohere" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Offers language models optimized for retrieval-augmented generation and enterprise applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供已针对检索增强生成和企业应用程序优化的语言模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.core42" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides multilingual language models tailored for Arabic and English, facilitating diverse linguistic applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供专为阿拉伯语和英语定制的多语言语言模型，可促进各种语言应用程序的开发。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.databricks" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides a unified data analytics platform integrating AI model development and deployment capabilities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供一个统一的数据分析平台，集成了 AI 模型开发和部署功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.deci" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specializes in optimizing deep learning models for faster inference and deployment efficiency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[专注于优化深度学习模型，以加快推理速度并提高部署效率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.deepseek" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops cost-effective large language models like R1, optimized for performance and efficiency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开发针对性能和效率进行了优化的经济高效的大型语言模型，如 R1。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.gretel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides synthetic data generation tools to enhance privacy and augment datasets for machine learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供合成数据生成工具，以增强隐私保护并扩充机器学习数据集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.histai" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[HistAI provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[HistAI 提供商]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.huggingface" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hosts a vast repository of open-source models and tools for natural language processing tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[托管大量用于自然语言处理任务的开源模型和工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.meta" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open-source models like Llama 2, built for versatile language tasks and research applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Llama 2 等开源模型，为各种语言任务和研究应用而构建。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.mimsHarvard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.mistral" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[French AI startup offering efficient and cost-effective language models, including Mistral 7B and Mixtral.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[法国 AI 初创公司，提供经济高效的语言模型，包括 Mistral 7B 和 Mixtral。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.nixtla" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Focuses on time-series forecasting models, offering tools like TimeGEN-1 for predictive analytics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[专注于时间序列预测模型，提供 TimeGEN-1 等用于预测分析的工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.nttdata" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provides AI solutions across various industries, focusing on digital transformation and innovation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跨行业提供 AI 解决方案，专注于数字化转型和创新。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.nvidia" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Offers GPU-optimized models and tools for high-performance AI applications across various domains.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供针对各个领域的高性能 AI 应用进行 GPU 优化的模型和工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.paige" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specializes in AI-powered pathology solutions to enhance cancer diagnosis and treatment planning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[专门从事 AI 支持的病理学解决方案，以增强癌症诊断和治疗规划。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.phi" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Proprietary AI models developed by Microsoft, tailored for various enterprise applications and integrated within Azure services.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 开发的专有 AI 模型，为各种企业应用量身定制，并集成在 Azure 服务中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.rockwell" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develops AI-driven industrial automation solutions to optimize manufacturing processes and operations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开发 AI 驱动的工业自动化解决方案，以优化制造流程和运营。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.saifr" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Offers AI tools for content compliance and risk management in regulated industries.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为受监管行业提供用于内容合规性和风险管理的 AI 工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.sdaia" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Saudi Data and AI Authority developing AI solutions to drive national digital transformation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[沙特数据和人工智能管理局开发 AI 解决方案以推动国家数字化转型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.sightmachine" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Offers AI-driven manufacturing analytics to improve operational efficiency and product quality.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供 AI 驱动的制造分析，以提高运营效率和产品质量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.snowflake" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cloud-based data platform that integrates with AI models for enhanced data processing and analysis.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于云的数据平台，与 AI 模型集成以增强数据处理和分析能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.stabilityai" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specializes in open-source generative AI models for image and text generation, including Stable Diffusion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[专注于图像和文本生成的开源生成式 AI 模型，包括 Stable Diffusion。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.wanglab" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";PublisherDescriptions.xai" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[]]></Val>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ChatCompletions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat completions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.Finance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Finance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[财务]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.agents" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Agent supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支持的智能体]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.agriculture" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Agriculture]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[农业]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.assistants" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assistant supported]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[支持的助手]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.audio-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Audio classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[音频分类]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Audio Classification]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.audio-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Audio generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[音频生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.audio-text-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Audio text To text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[音频文本转文本]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Audio Text To Text]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.automatic-speech-recognition" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic speech recognition]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动语音识别]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.batch-paygo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批处理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.bitext_mining" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bitext mining]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[双语文本挖掘]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.chat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.chat-completion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat completion]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.clustering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clustering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聚类分析]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.completions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Completions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.consumer-goods" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Consumer goods]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[消费品]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Consumer Goods]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.conversational" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Conversational]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.data-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.drug-discovery" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drug discovery]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[药物发现]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.embeddings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Embeddings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[嵌入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.environmental-forecasting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environmental forecasting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[环境预测]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Environmental Forecasting]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.feature-extraction" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Feature extraction]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[特征提取]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Feature Extraction]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.fill-mask" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fill mask]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[填充掩码]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.financial-services" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Financial services]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[金融服务]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Financial Services]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.forecasting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Forecasting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[预测]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.health-and-life-sciences" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Health and life sciences]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[医疗健康与生命科学]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Health and Life Sciences]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-feature-extraction" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image feature extraction]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像特征提取]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Image Feature Extraction]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-instance-segmentation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image instance segmentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像实例分段]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-object-detection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image object detection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像对象检测]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-segmentation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image segmentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像分段]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-text-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image text To text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像文本转文本]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Image Text To Text]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-to-image" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image to image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像到图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.image-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image to text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像到文本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.maap-inference" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[托管计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.manufacturing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Manufacturing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[制造]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.materials-design" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Materials design]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[材料设计]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.mobility" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mobility]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[出行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.multi-object-tracking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multi-Object tracking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多对象跟踪]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.multimodal-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multimodal classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多模式分类]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Multimodal Classification]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.object-detection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Object detection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对象检测]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.pair_classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pair classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配对分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.protein-binder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Protein binder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[蛋白质结合剂]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Protein Binder]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.protein-design" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Protein design]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[蛋白质设计]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Protein Design]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.protein-structure-prediction" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Protein structure prediction]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[蛋白质结构预测]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Protein Structure Prediction]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.ptu" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provisioned throughput (PTU)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[预配吞吐量(PTU)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Provisioned Throughput (PTU)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.question-answering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[问题解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.reasoning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.reranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新排序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.responses" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Responses]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[响应]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.restrictedaccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restricted access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[受限访问]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.retrieval" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Retrieval]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[检索]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.sentence-similarity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sentence similarity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[句子相似度]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Sentence Similarity]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.serverless-inference" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Serverless API]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无服务器 API]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.speech-recognition" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Speech recognition]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[语音识别]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.speech-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Speech to text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[语音转文本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.standard-paygo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pay per use]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即付即用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.streaming" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Streaming]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流式处理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.sts" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[STS]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[STS]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.table-question-answering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Table question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[表问题解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.table-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Table to text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[表格转文本]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Table To Text]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-ranking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text ranking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本排序]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Text Ranking]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-summarization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text summarization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-to-3d" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to 3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本转 3D]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Text To 3D]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-to-image" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本到图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-to-speech" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to speech]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本转语音]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text-translation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text translation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本翻译]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.text2text-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to text generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本到文本生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.time-series-forecasting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Time series forecasting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[时序预测]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Time Series Forecasting]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.token-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[令牌分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.tool-calling" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tool calling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工具调用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.translation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Translation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[翻译]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.video-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[视频分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.video-generation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video generation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[视频生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.video-multi-object-tracking" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video multi-Object tracking]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[视频多对象跟踪]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.video-text-to-text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video text to text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[视频文本转文本]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Video Text To Text]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.visual-question-answering" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visual question answering]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可视化问题解答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.vm" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Virtual machine]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[虚拟机]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Virtual Machine]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.vm-withsurcharge" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Optimized virtual machine]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已优化的虚拟机]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Optimized Virtual Machine]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.zero-shot-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zero-shot classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零快照分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tasks.zero-shot-image-classification" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zero-shot image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零快照图像分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.chatHistory_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select the number of past messages to include in each new API request. This helps give the model context for new user queries. Setting this number to 10 will include 5 user queries and 5 system responses.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择要包含在每个新 API 请求中的过去消息数。这有助于为新用户查询提供模型上下文。将此数字设置为 10 将包括 5 个用户查询和 5 个系统响应。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.do_sample_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Whether or not to use sampling. If False, uses greedy decoding.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否使用采样。如果为 False，则使用贪婪解码。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.file_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload the audio file to be transcribed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上传待转录的音频文件。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.formatting_reenabled_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enables response in Markdown format.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启用 Markdown 格式的响应。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.frequency_penalty_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discourages the model from generating the same words or phrases too frequently by applying a penalty (between -2.0 and 2.0) based on their existing frequency in the text.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根据字词或短语在文本中出现的频率，应用惩罚 (介于 -2.0 和 2.0 之间)，从而防止模型过于频繁地生成相同的字词或短语。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.frequency_penalty_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reduce the chance of repeating a token proportionally based on how often it has appeared in the text so far. This decreases the likelihood of repeating the exact same text in a response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根据令牌到目前为止在文本中出现的频率，按比例减少重复令牌的几率。这降低了在响应中重复完全相同的文本的可能性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.generate_summary_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A summary of the reasoning performed by the model. This can be useful for debugging and understanding the model's reasoning process.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型执行的推理摘要。这对于调试和理解模型推理过程非常有用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.guidance_scale_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls how closely the image follows the text prompt. Higher values produce images that more closely follow the prompt.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制图像与文本提示的匹配程度。较高的值会生成更契合提示的图像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.image_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The image to edit. Must be a valid PNG file, less than 4MB, and square. If mask is not provided, image must have transparency, which will be used as the mask.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要编辑的图像。必须是有效的 PNG 文件，小于 4MB，且为正方形。如果未提供掩码，则图像必须具有透明度，这将用作遮罩。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.image_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The base image to modify when using image-to-image (i.e. text + image = image) generation mode.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在使用图像到图像(如文本 + 图像 = 图像)生成模式时要修改的基础图像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.image_format_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The file format for the generated output image (PNG or JPG).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成的输出图像的文件格式(PNG 或 JPG)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.instructions_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instructions for the model to follow when generating audio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成音频时要遵循的模型指令。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.mask_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An additional image whose fully transparent areas (e.g. where alpha is zero) indicate where image should be edited. Must be a valid PNG file, less than 4MB, and have the same dimensions as image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[另一个图像，其完全透明的区域(例如 alpha 为零)指示了应编辑图像的位置。必须是有效的 PNG 文件，小于 4MB，并且维度与图像相同]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.max_tokens_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Limit the maximum output tokens for the model response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[限制模型响应的最大输出令牌数。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.n_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of images to generate. Currently limited to 1 per request.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要生成的图像数量。当前每个请求限制为 1 个。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.n_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of images to generate. Currently limited to 10 per request.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要生成的图像数量。当前限制为每个请求 10 个。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.negative_prompt_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text describing what you want to avoid in the generated image. Use this to exclude unwanted elements, styles, or themes from your results.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述你希望在生成图像中避免出现的内容的文本。使用此选项可从结果中排除不需要的元素、样式或主题。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.num_inference_steps_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of denoising steps (higher = more detail but slower)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[去噪步骤数(越高，表示细节越丰富，但速度越慢)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.output_compression_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Control the compression level for the generated image. Higher values (100) mean less compression, while lower values reduce file size with some quality loss.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制所生成图像的压缩级别。值越高(100)，则表示压缩程度越低，而较低的值则会减少文件大小，但会出现质量损失。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.output_format_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose the format for the generated image. PNG supports transparency and lossless quality, while JPEG offers smaller file sizes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择所生成图像的格式。PNG 支持透明度和无损质量，而 JPEG 则提供较小的文件大小。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.presence_penalty_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discourages the model from repeating the same words or phrases too frequently by applying a penalty (between -2.0 and 2.0) based on their presence in the text.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[根据字词或短语在文本中的出现情况，应用惩罚 (介于 -2.0 和 2.0 之间)，从而防止模型过于频繁地重复相同的字词或短语。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.presence_penalty_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reduce the chance of repeating any token that has appeared in the text at all so far. This increases the likelihood of introducing new topics in a response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[减少到目前为止文本中出现的任何标记重复的可能性。这增加了在响应中引入新主题的可能性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.prompt_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Improve transcription accuracy by providing a list of known phrases, such as names of people or specific locations. Use commas or semicolons to separate each value in the phrase list.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过提供已知短语的列表(例如人员姓名或特定位置)，提高听录准确性。使用逗号或分号分隔短语列表中的每个值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.quality_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the quality and level of detail in the generated image. 'high' produces more detailed images but takes longer to generate.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制所生成图像的质量和细节层次。“高”会生成更详细的图像，但生成所需的时间更长。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.quality_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the quality and level of detail in the generated image. 'HD' produces more detailed images but takes longer to generate.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制所生成图像的质量和细节层次。“HD”生成更详细的图像，但所需的时间更长。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.quality_description_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the quality and level of detail in the generated image. 'High' produces more detailed images but takes longer to generate.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制所生成图像的质量和细节层次。“高”会生成更详细的图像，但生成所需的时间更长。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.reasoning_effort_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Adjust the model's cognitive load with options for low, medium, and high reasoning levels.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用低、中和高推理级别的选项调整模型的认知负载。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.repetition_penalty_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The weight of penalty for repeated phrases. Higher values will suppress repeating similar phrases.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重复短语的惩罚权重。较高的值将禁止重复类似的短语。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.response_format_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The output format of the generated audio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成音频的输出格式。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.return_full_text_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Whether or not to return the full text including the original query or just the completion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是返回包括原始查询的全文，还是仅返回完成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.rows_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of rows to return]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要返回的行数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.seed_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the randomness of generation. Using the same seed value will produce similar results for identical prompts. Set to 0 for random results each time.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制生成的随机性。对于相同的提示，使用相同的种子值将生成相似的结果。设置为 0，每次可获得随机结果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.size_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The dimensions of the generated image in pixels (width × height).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所生成图像的尺寸，以像素为单位(宽 × 高)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.size_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The dimensions of the generated image in pixels (width × height). Default is 1024 x 1024.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所生成图像的尺寸，以像素为单位(宽 × 高)。默认为 1024 x 1024。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.speed_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The speed of the generated audio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成音频的速度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.stop_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Force cutting the output when this string occurs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此字符串出现时强制切断输出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.strength_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Often referred to as denoising, Strength controls how much the input image has on the generated output image. Values closer to 0 generate an output image closer to the input image. Values closer to 1 generate an output image more influenced by the model and text prompt applied to the input image.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通常称为去噪，强度控制输入图像对生成的输出图像的影响程度。值接近 0 时，生成的输出图像更接近输入图像。值接近 1 时，生成的输出图像则更受应用于输入图像的模型和文本提示的影响。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.style_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls the visual style of the image. 'Vivid' creates hyper-real and dramatic images, while 'Natural' creates more realistic images with less exaggeration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制图像的视觉样式。“生动”创建超真实且引人注目的图像，而“自然”则创建更逼真但吸引力更小的图像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.temperature_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls randomness in the response, use lower to be more deterministic.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制响应中的随机性，使用较低的值来提高确定性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.text_guidance_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls how closely the image follows the text prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[控制图像与文本提示的匹配程度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.top_k_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of highest probability vocabulary tokens to keep for top-k-filtering, defaults to null.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要保留用于 top-k 筛选的最高概率词汇令牌数，默认为 null。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.top_k_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of highest probability vocabulary tokens to keep for top-k-filtering.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要保留用于 top-k 筛选的最高概率词汇令牌数。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.top_p_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls text diversity by selecting the most probable words until a set probability is reached.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过选择最可能的单词来控制文本多样性，直到达到设定的概率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.top_p_description_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Controls data diversity by selecting the most probable words until a set probability is reached.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过选择最可能的字词来控制数据多样性，直到达到设定的概率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.video_duration_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The duration of the generated video.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所生成视频的持续时间。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.video_height_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The height of the generated video.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所生成视频的高度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.video_variations_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The number of variations to generate.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要生成的变体数。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.video_width_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The width of the generated video.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所生成视频的宽度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.descriptions.voice_description_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The voice to use for the generated audio.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要用于生成的音频的语音。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.answer_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Answer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[回答]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.audio_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Speech to Text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[语音转文本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.chatHistory_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat History]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天历史记录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.chatHistory_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Past messages included]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已包含过去的消息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.context_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Context]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上下文]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.do_sample_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do Sample]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[执行采样]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.document_url_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Document]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文档]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.file_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.formatting_reenabled_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Formatting re-enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已重新启用格式设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.frequency_penalty_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Frequency Penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[频率惩罚]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.frequency_penalty_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Frequency penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[频率惩罚]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.generate_summary_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate Summary]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.generatedImage_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成的映像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.generated_image_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Result]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[结果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.generated_video_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated video result]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成的视频结果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.guidance_scale_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Guidance Scale]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指导系数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_features_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image Features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像特征]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_format_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zero shot image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[零快照图像分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Original image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[原始图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.image_friendlyName_6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Frontal Image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正面图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.include_image_base64_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Include Image Base64]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包括图像 Base64]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.instructions_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instructions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指令]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.jsonInput_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.labels_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Labels]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标签]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.language_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Language]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[语言]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.mask_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mask]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[过滤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.mask_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Mask image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[过滤图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.max_completion_tokens_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max Completion Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大完成令牌数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.max_new_tokens_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max New Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大新令牌数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.max_tokens_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大令牌数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.model_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.n_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of Images]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.n_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of variations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[变体数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.negative_prompt_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Negative Prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[负面提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.negative_prompt_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Negative prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[负面提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.num_inference_steps_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Diffusion Steps]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[扩散步骤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.output_compression_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compression Level]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[压缩级别]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.output_format_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image Format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.output_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Findings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.presence_penalty_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Presence Penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[状态惩罚]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.presence_penalty_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Presence penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[状态惩罚]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.prompt_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phrase list]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[短语列表]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.prompt_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.quality_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[质量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.question_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text to image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本到图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.question_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Question]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[问题]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.question_friendlyName_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Query]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查询]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.reasoning_effort_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reasoning Effort]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推理工作量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.repetition_penalty_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Repetition Penalty]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重复惩罚]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.response_format_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Response format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[响应格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.return_full_text_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Return Full Text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[返回全文]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.revised_prompt_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Revised Prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[修订后的提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.rows_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.seed_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Seed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[种子]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.size_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.size_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image Size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图像大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.speed_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Speed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[速度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.stop_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.strength_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Strength]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[强度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.style_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Style]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[样式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.temperature_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Temperature]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[温度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.text_features_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Text Features]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本特征]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.text_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Transcription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[听录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.text_friendlyName_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generated text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成的文本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.text_friendlyName_3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Indication]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.top_k_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Top K]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Top K]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.top_p_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Top P]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Top P]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_duration_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Duration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[持续时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_format_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_height_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Height]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_variations_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Video to text classification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[视频转文本分类]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.video_width_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Width]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[宽度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.friendlyNames.voice_friendlyName_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Voice]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[语音]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.options.quality_high_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[High]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.options.quality_low_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Low]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[低]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Widget.options.quality_medium_1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Medium]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[中等]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
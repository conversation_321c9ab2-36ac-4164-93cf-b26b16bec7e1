﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\3\s\extensions\labs\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-CN" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";Banner.AboutText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[About]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[关于]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.BrandName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Foundry Labs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Foundry实验室]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.FollowLinkedin" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Follow us on LinkedIn]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 LinkedIn 上关注我们]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.FollowX" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Follow us on X]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 X 上关注我们]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.FollowYoutube" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Follow us on YouTube]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 YouTube 上关注我们]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.MoreOptions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More options]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更多选项]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Banner.SwitchToDarkTheme" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dark theme]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[深色主题]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.ContactLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Contact]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[联系人]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.CopyrightText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[© Microsoft 2025]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[© Microsoft 2025]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.PrivacyLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Privacy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隐私]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.TermsLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Terms]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[条款]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Footer.TrademarksLinkText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trademarks]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[商标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.AuroraDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora is a foundation model, built on vast amounts of atmospheric data, that can significantly improve our ability to predict extreme weather events. Explore how this innovative model can enhance weather forecasting.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 是一种基础模型，基于大量空气数据构建，可显著提高我们预测极端天气事件的能力。了解此创新模型如何增强天气预报。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Introducing Aurora, a foundation model, built on vast amounts of atmospheric data, that can significantly improve our ability to predict extreme weather events. Explore how this innovative model can enhance weather forecasting.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.BeFirst" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Be the first to know about the latest AI innovations and accelerate your journey.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[第一个了解最新 AI 创新并加速你之旅的人。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.BioEmuDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A deep learning model that can generate thousands of protein structures per hour on a single GPU.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[每小时可在单个 GPU 上生成数千个蛋白质结构的深度学习模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.BitnetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research, BitNet b1.58 2B4T is the first open-source, native 1-bit large language model (LLM) at a 2-billion parameter scale.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet b1.58 2B4T 由 Microsoft Research 开发，是第一个 20 亿参数规模的开放源代码本机 1 位大语言模型(LLM)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Moving from curiosity to clarity, from imagination to reality.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从好奇到清晰，从构想变为现实。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.EvoDiffDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A general-purpose diffusion framework for controllable protein generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一种用于实现可控蛋白质生成的通用扩散框架。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.ExACTDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT 是一种让 AI 代理更有效地探索的方法，使它们能够智能地导航环境、收集有价值的信息、评估选项，并确定最佳决策和规划策略。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Developed by Microsoft Research, ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.InnovationSubtitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get a glimpse of potential future directions for AI, with these experimental technologies from Microsoft Research.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过来自Microsoft研究的这些实验性技术，了解 AI 的潜在未来方向。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Glimpse of potential future directions for AI, with these experimental technologies from Microsoft Research.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Innovations" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Innovations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.JoinCommunity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Join the community and stay connected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[加入社区并保持联系]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Labs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Labs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实验室]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MCPServerDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Integrate, Prototype, and Accelerate AI Model Experimentation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[集成、原型设计和加速 AI 模型实验]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MSRACCDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is the largest high-accuracy dataset for training deep-learning-based models for density functional theory (DFT). It enables a leap forward in predictive chemistry and supports the development of Skala, a new DFT functional from Microsoft Research that has achieved a breakthrough in accuracy for this workhorse method that thousands of scientists use every year to simulate matter at the atomistic level.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这是用于训练基于深度学习的密度泛函理论(DFT)模型的最大高准确度数据集。它实现了预测化学的跨越式发展，并支持 Skala 的开发，Skala 是 Microsoft Research 推出的全新 DFT 泛函，对这种每年数千位科学家用于在院子层面模拟物质的常用方法在准确度方面实现了突破。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MagenticOneDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One is a multi-agent system designed to navigate complex tasks across diverse domains. Discover how intelligent agents could operate autonomously to enhance workflow efficiency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 是一个多代理系统，旨在跨不同域导航复杂任务。了解智能代理如何可以自主操作以提高工作流效率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MagenticUIDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI is an open-source experimental platform to accelerate progress in human-agent collaboration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 是一个开源的实验平台，旨在加速人机协作的进展。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MagmaDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in digital and physical environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma 是一种多模式基础模型，旨在了解数字和物理环境中的行为。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in dexpigital and physical environments.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MatterSimDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An AI-driven innovation transforming how we create and understand new materials, starting with accurate and efficient simulations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一项 AI 驱动的创新，它转换了我们创建和理解新材料的方法，从准确高效的模拟开始。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.MuseDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empowering game creatives with generative AI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过生成的 AI 为游戏创意助力。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Empowering game creatives with Generative AI.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.NextCoderDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhancing the ability of coding models to handle diverse editing requirements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增强编码模型处理多样化编辑需求的能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.OmniParserDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is a pioneering screen parsing module that transforms user interfaces into actionable elements through visual input. Discover how this innovative approach can enhance automated UI interactions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 是一个通过可视化输入将用户界面转换为可操作元素的节点屏幕分析模块。了解此创新方法如何增强自动化 UI 交互。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.PEACEDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE paves the way for advanced AI applications in geology, powering more efficient and accurate disaster detection, resource exploration, and civil engineering. Learn how PEACE transforms general-purpose multimodal LLMs into powerful domain-specific agents.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 为高级 AI 功能在地质学方面的应用开辟了道路，为实现更高效、精准的灾害监测、资源勘探和土木工程提供了支持。了解 PEACE 如何将通用多模式 LLM 转换为功能强大的特定领域的代理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Pause" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pause]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[暂停]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Phi4Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore the capabilities of Phi-4, the latest model in Microsoft's Phi family of advanced AI technologies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Microsoft 先进 AI 技术 Phi 系列的最新模型 Phi-4 的功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Play" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Play]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[播放]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.ProjectAmelieDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie is our first Foundry autonomous agent built in collaboration with Microsoft Research that can perform machine learning engineering tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 是我们与 Microsoft Research 合作构建的第一个 Foundry 自主代理，可以执行机器学习工程任务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.ReMeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe enables scalable, personalized approaches to cognitive health—a growing need that affects millions. Explore this web-based framework, which puts powerful AI-enabled research tools in the hands of scientists and clinicians.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 实现了一种可缩放的个性化方法，可满足不断增长的对认知健康的需求，惠及数以百万计的人群。探索此基于 Web 的框架，了解该框架为科学家和临床医生提供的 AI 支持的强大研究工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.TamGenDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover how AI could accelerate the process of pharmaceutical discovery, leading to faster medical breakthroughs and improved treatment options.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 AI 如何加速药物发现过程，从而更快地发现医疗障碍和改进的治疗选项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure AI Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure AI Foundry]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.TrellisDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis is an AI model that generates high-quality 3D assets from text or image prompts in formats like meshes, radiance fields, and 3D Gaussians. Discover how it uses Structured LATents (SLAT) to fuse sparse 3D grids with dense visual features—powering easy and creative 3D content creation in gaming, AR/VR, design, and simulation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 是一种 AI 模型，可以根据文本或图像提示生成高质量的 3D 资产，格式包括网格、辐射场和 3D 高斯。了解它如何使用结构化 LATent (SLAT) 将稀疏 3D 网格与密集的视觉功能融合在一起 - 支持在游戏、AR/VR、设计和模拟领域创建简单而有创意的 3D 内容。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.TypeAgentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent is sample code that explores an architecture for building a single personal agent with natural language interfaces leveraging current advances in LLM technology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 是一段示例代码，它探索了一种利用 LLM 技术的最新进展构建具有多种自然语言界面的个人代理的体系结构。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.VASA3DDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D turns a single portrait and speech audio into a lifelike 3D talking head using a novel motion-latent-driven model. Discover how it enables real-time, expressive, and multiview-consistent avatars for education, collaboration, and immersive experiences.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 利用新颖的动作潜变量驱动模型，可将单个肖像和语音音频转化为逼真的会说话的 3D 头部模型。探索它如何为教育、协作和沉浸式体验实现实时、富有表现力且多视角一致的虚拟形象。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HomePage.VibePodDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a multi-speaker audio generation framework for creating long-form, realistic dialogue from transcripts. It’s ideal for podcasts and voiceovers, with strong performance in pacing, coherence, and speaker dynamics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一种多说话人音频生成框架，用于根据脚本创建长篇、逼真的对话。它非常适合用于播客和画外音，在节奏、连贯性和说话人动态方面表现出色。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.DownloadDemo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Link to demo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指向演示的链接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.OpenInGithub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open in Github]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 GitHub 中打开]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.ReadAnnouncements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read announcements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[读取公告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.ReadPaper" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Read paper]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[阅读报纸]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Actions.WatchVideo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Watch video]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[观看视频]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraBodyVideoAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Megan Stanley, Senior Researcher at Microsoft Research AI for Science, discusses Aurora, a groundbreaking model for weather forecasting that could revolutionize predictions and mitigation of extreme events, air pollution, and climate change.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MeganStan， senior research at Microsoft Research AI for Science， discusses Aurora， a groundbreaking model for weather forecasting that could revolutionize predictions and mitigation of extreme events， air mitigation， and weather change.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora is a large-scale foundation model developed for atmospheric forecasting. By leveraging extensive atmospheric data, this model enhances our capacity to predict and mitigate the impacts of extreme weather events.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 是一种为气压预测而开发的大型基础模型。通过利用广泛的空气数据，此模型增强了我们预测和缓解极端天气事件影响的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Aurora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 Aurora]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraHomeVideoDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Built on vast amounts of atmospheric data, this foundation model can significantly improve our ability to predict extreme weather events.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此基础模型基于大量的空气数据构建，可以显著提高我们预测极端天气事件的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora emerged from the recognition that traditional weather prediction models often fall short in capturing the rapid intensification and peak wind speeds that characterize extreme storms. Aurora’s innovative architecture has been trained on over a million hours of diverse weather and climate simulations, enabling it to excel in a broad spectrum of predictive tasks while achieving an impressive spatial resolution of 0.1° - approximately 11 km at the equator. This level of granularity enhances the accuracy of operational forecasts and confers an estimated computational speed advantage of around 5,000 times over conventional numerical weather-prediction systems.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 利用了对传统天气预测模型通常不起作用来捕获极速特征的快速加速和峰值风速。Aurora 的创新体系结构经过了一百多万小时的不同天气和天气模拟训练，使它能够在各种预测任务中脱颖而出，同时在赤道上达到显著的空间分辨率 0.1° - 约 11 公里。此级别的粒度可提高操作预测的准确性，并将计算速度优势估计为大约 5,000 倍于常规数字天气预测系统。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora’s capabilities extend beyond accuracy and efficiency; it showcases versatility in forecasting a variety of atmospheric variables, including temperature, wind speed, and air pollution levels. Built using a flexible 3D Swin Transformer architecture and incorporating Perceiver-based encoders and decoders, Aurora effectively processes heterogeneous input data and generates predictions across multiple resolutions. Utilizing extensive pretraining on diverse datasets and fine-tuning for specific tasks, Aurora discerns complex patterns in atmospheric data, often yielding noteworthy results even with limited training data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 的功能超出准确性和效率;它展示预测各种气压变量的多功能性，包括温度、风速和空气严重程度。Aurora 使用灵活的 3D Swin Transformer 体系结构生成，并合并基于 Perceiver 的编码器和解码器，可有效地处理异类输入数据，并跨多个分辨率生成预测。利用对各种数据集进行广泛的预训练，并针对特定任务进行微调，Aurora discerns 在空气数据中的复杂模式，即使训练数据有限，也通常会产生值得注意的结果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The significance of Aurora transcends performance metrics; its robust architecture and diverse pretraining illustrate how scale and data variety enhance atmospheric forecasting. By incorporating data from climate simulations, reanalysis products, and operational forecasts, Aurora builds a nuanced and generalizable understanding of atmospheric dynamics. Compared to leading specialized deep learning models, Aurora demonstrates the ability to surpass existing benchmarks, establishing it as a crucial tool for future environmental predictions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Aurora 的重要性超过了性能指标;其强大的体系结构和多样的预训练说明比例和数据种类如何增强气流预测。通过整合来自天气模拟、重新分析产品和操作预测的数据，Aurora 生成了对气动动态的有差异和可通用理解。与领先的专业深度学习模型相比，Aurora 演示了能够检验现有基准的能力，并将其作为未来环境预测的关键工具建立。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Aurora]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[炫目极光]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.AuroraTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Aurora model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 Aurora 模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Try Aurora]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu samples functionally distinct protein conformations. a) Large-scale domain motions such as opening/closing, rotation, and repacking. b) Local unfolding or unbinding of parts of the protein. c) Formation of cryptic binding pockets that are not present in the apo ground state.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu 可对功能各异的蛋白质构象进行采样。a) 大规模的结构域运动，例如开/合、旋转和重新排列。b) 局部解折叠或解离蛋白质部分区域。c) 形成在脱辅基基态中不存在的隐性结合口袋。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu multi-conformation benchmark of local unfolding. For each case, the PDB structure used as folded state reference is shown in red, with the part can unfold highlighted. Energy landscapes show the empirical free energy sampled by the pre-trained (black) and fine-tuned (blue) model.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu 局部解折叠多构象基准检验。对于每种情况，用作折叠态参考的 PD 结构以红色显示，其中能够发生解折叠的部分则突出显示。能量景观展示了由预训练模型(黑色)和微调模型(蓝色)采样的经验自由能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu-1 is a deep learning model that can generate thousands of protein structures per hour on a single graphics processing unit. It provides orders of magnitude greater computational efficiency compared to classical MD simulations, thereby opening the door to insights that have, until now, been out of reach.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu-1 是一种深度学习模型，每小时可在单个图形处理单元上生成数千个蛋白质结构。与古典 MD 模拟相比，它的计算效率要高出几个数量级，从而为获取到目前为止一直无法获取的见解开辟了道路。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover BioEmu]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 BioEmu]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[From forming muscle fibers to protecting us from disease, proteins play an essential role in almost all biological processes in humans and other life forms alike. There has been extraordinary progress in recent years toward better understanding protein structures using deep learning, enabling the accurate prediction of protein structures from their amino acid sequences. However, predicting a single protein structure from its amino acid sequence is like looking at a single frame of a movie—it offers only a snapshot of a highly flexible molecule. Biomolecular Emulator-1 (BioEmu-1) is a deep-learning model that provides scientists with a glimpse into the rich world of different structures each protein can adopt, or structural ensembles, bringing us a step closer to understanding how proteins work. A deeper understanding of proteins enables us to design more effective drugs, as many medications work by influencing protein structures to boost their function or prevent them from causing harm.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从形成肌肉纤维到保护我们免受疾病侵害，蛋白质在人类以及其他各种生命形式的几乎所有生物过程中都发挥着至关重要的作用。近年来，通过使用深度学习，对于蛋白质结构方面的更深入理解已取得重大进展，从而能够根据蛋白质的氨基酸结构准确预测其结构。但根据氨基酸序列预测单个蛋白质结构就像观看电影中的一帧画面一样，因为它只提供了一个高度灵活多变的分子的快照。生物分子模拟器 1 (BioEmu-1)是一种深度学习模型，它让科学家得以初步了解每种蛋白质可能呈现的多种结构的丰富世界或结构集合，从而推动我们更深入理解蛋白质的作用机制。通过对蛋白质的更深入理解，我们能够设计更有效的药物，因为许多药物都是通过影响蛋白质结构来增强其药效或防止其产生危害。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BioEmu-1]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BioEmu-1]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get BioEmu model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 BioEmu 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BioEmuTryItOutGitHub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open BioEmu repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 BioEmu 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A scatter plot graph showing the average score of 11 benchmarks on the y-axis and memory footprint in gigabytes (GB) on the x-axis for various open-weight large language models (LLMs). The Pareto Frontier of Open-weight LLMs is indicated by a blue dashed line. Data points include Qwen2-5.3B, BitNet b1.58 2B (marked with a red star as an outlier with low memory footprint), Qwen2-5.1-5B, SmolLM2-1.7B, MiniCPM-2B, LLaMa-2-13B, Gemma-3-13B, and Qwen2-0.5-8B. The image shows a comparison of different large language models based on their performance and memory usage, highlighting which models are more efficient or powerful relative to their memory footprint. This is relevant for understanding trade-offs in model design and deployment efficiency in machine learning applications.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[关于各种开源权重大语言模型(LLM)的散点图，其中 y 轴为各种模型在 11 项基准检验中的平均分数，x 轴为其内存占用情况(GB)。蓝色虚线指示了开源权重 LLM 的 Pareto 边界。数据点包括 Qven2-5.3B、BitNet b1.58 2B (使用红色星号标记为内存占用较低的离群值)、Qven2-5.1-5B、SmolLM2-1.7B、MiniCPM-2B、LLaMa-2-13B、Gemma-3-13B 和 Qven2-0.5-8B。此图显示了不同大语言模型在性能和内存使用方面的比较情况，其中突出显示了相对于其内存占用更高效或更强大的模型。这与了解机器学习应用程序中模型设计和部署效率的权衡相关。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research, BitNet b1.58 2B4T is the first open-source, native 1-bit large language model (LLM) in which every parameter is ternary (i.e., -1, 0, 1), at a 2-billion parameter scale. Trained on a corpus of 4 trillion tokens, this model demonstrates that native 1-bit LLMs can achieve performance comparable to leading open-weight, full-precision models of similar size, while offering substantial advantages in computational efficiency, including substantially reduced memory footprint, energy consumption, and decoding latency.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet b1.58 2B4T 由 Microsoft Research 开发，它是第一款开放源代码本机 1 位大语言模型(LLM)，其参数规模为 20 亿，且每个参数均可为三个值(即 -1、0、1)。此模型在 4 万亿个令牌的语料库上进行训练，表明原生 1 位大型语言模型能够实现与同等大小的领先开放权重全精度模型相当的性能，同时在计算效率方面具有显著优势，包括大幅减少内存占用、能耗和解码延迟。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about BitNet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解有关 BitNet 的详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft researchers have demonstrated that BitNet b1.58 2B4T achieves performance on par with leading open-weight, full-precision LLMs of similar size, while offering significant advantages in computational efficiency, including substantially reduced memory footprint, energy consumption, and decoding latency. To facilitate further research and adoption, the model weights have been released along with open-source inference implementations for both GPU and CPU architectures.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 研究人员已证明，BitNet b1.58 2B4T 在性能上可与同等大小的领先开放权重全精度大型语言模型(LLM)持平，同时在计算效率方面提供了显著的优势，包括大幅减少内存占用、能耗解码延迟。为了促进进一步研究和采用，模型权重已经与 GPU 和 CPU 体系结构的开放源代码推理实现一起发布。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BitNet]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open BitNet repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开源 BitNet 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.BitnetTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[BitNet has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[BitNet 已发布用于研究目的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An animated sequence showing how a chain of amino acids folds into a protein’s three-dimensional structure using the natural sequences predicted by EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一个动画序列，显示氨基酸链如何使用 EvoDiff 预测的自然序列折叠成蛋白质的三维结构]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff is a general-purpose diffusion framework that combines evolutionary-scale data with the distinct conditioning capabilities of diffusion models for controllable protein generation in sequence space. EvoDiff generates high-fidelity, diverse, and structurally-plausible proteins that cover natural sequence and functional space.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff 是一个通用的扩散框架，它将进化规模的数据与扩散模型的独特调节能力相结合，以便在序列空间中实现可控的蛋白质生成。EvoDiff 将生成覆盖自然序列和功能空间的高保真、多样化且结构合理的蛋白质。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 EvoDiff]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Critically, EvoDiff can generate proteins inaccessible to structure-based models, such as those with disordered regions, while maintaining the ability to design scaffolds for functional structural motifs, demonstrating the universality of our sequence-based formulation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[至关重要的是，EvoDiff 可以生成基于结构的模型所无法获得的蛋白质(例如那些具有无序区域的蛋白质)，同时保持为功能性结构基序设计支架的能力，证明了我们基于序列的公式的通用性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff will expand capabilities in protein engineering beyond the structure-function paradigm toward programmable, sequence-first design. The sequence and MSA models – EvoDiff-Seq and EvoDiff-MSA, respectively – were evaluated across a range of generation tasks to demonstrate their power for controllable protein design.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff 会将蛋白质工程的能力从结构-功能范式扩展到可编程的序列优先设计。序列模型和 MSA 模型(分别为 EvoDiff-Seq 和 EvoDiff-MSA)在一系列生成任务中进行了评估，证明了自身在可控蛋白质设计方面的强大能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[EvoDiff]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[EvoDiff]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTryItOutAzureAI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get EvoDiff model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 EvoDiff 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.EvoDiffTryItOutGithub" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open EvoDiff repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 EvoDiff 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* A gradient blue to green background features a white flowchart with rectangular boxes connected by arrows, ending in a hexagonal “STOP” sign and a check mark on the right side.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*DECORATIVE* 从渐变的蓝色到绿色背景的特征是白色流程图，其中矩形框由箭头连接，以六边形“STOP”标志和右侧检查标记结尾。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT is an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT 是一种让 AI 代理更有效地探索的方法，使它们能够智能地导航环境、收集有价值的信息、评估选项，并确定最佳决策和规划策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Teach AI agents to explore more effectively.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[教导 AI 代理更有效地探索。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover ExACT]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 ExACT]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Autonomous AI agents are transforming the way we approach multi-step decision-making processes, streamlining tasks like web browsing, video editing, and file management. By applying advanced machine learning, they automate workflows, optimize performance, and reduce the need for human input.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自治 AI 代理正在改变我们处理多步骤决策流程、简化 Web 浏览、视频编辑和文件管理等任务的方式。通过应用高级机器学习，它们可自动化工作流、优化性能并减少对人类输入的需要。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[However, these systems struggle in complex, dynamic environments. A key challenge lies in balancing exploitation, using known strategies for immediate gains, with exploration, which involves seeking new strategies that could yield long-term benefits. Additionally, they often have difficulty adapting to unpredictable changes in conditions and objectives, as well as generalizing knowledge across contexts, limiting their ability to transfer learned strategies between domains.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[然而，这些系统在复杂的动态环境中处于抗争状态。关键挑战在于平衡利用，使用已知的策略来立即获得收益，以及探索，这涉及到寻找可以带来长期权益的新策略。此外，它们通常难以适应条件和目标的不可预测更改，以及跨上下文通用化知识，从而限制它们在域之间传输已学习的策略的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In response, Microsoft researchers have developed ExACT, an approach for teaching AI agents to explore more effectively, enabling them to intelligently navigate their environments, gather valuable information, evaluate options, and identify optimal decision-making and planning strategies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作为回应，Microsoft研究人员开发了 ExACT，这是一种让 AI 代理更有效地探索的方法，便于他们智能地导航环境、收集有价值的信息、评估选项，并确定最佳决策制定和规划策略。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ExACT]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ExACT]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ExACTTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open ExACT repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 ExACT 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The MCP Server for Azure AI Foundry Labs is designed to supercharge team velocity in adopting and evaluating breakthrough AI research. By equipping GitHub Copilot with custom tools for intelligent model discovery, tailored implementation guidance, and rapid prototyping, this can achieve exponential productivity gains and reduce the “idea-to-prototype” cycle to under 10 minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用于 Azure AI Foundry 实验室的 MCP 服务器旨在加速团队在采用和评估突破性 AI 研究结果方面的速度。通过为 GitHub Copilot 提供用于支持智能模型发现、定制实施指导和快速原型设计的自定义工具，这可以实现指数级的生产力提升，并将“从创意到原型”的周期缩短到 10 分钟以内。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about the MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 MCP 服务器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Unified Model Discovery:</strong> Instantly list 45+ models (Microsoft Research, OpenAI, Meta, Mistral, and Azure Foundry Labs specialties) inside your coding environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong></strong>统一模型发现:在编码环境中即时列出超过 45 个模型(包括 Microsoft Research、OpenAI、Meta、Mistral 和 Azure Foundry Labs 的专用模型)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Implementation Guidance On Demand:</strong> GitHub Copilot receives detailed integration documentation and usage hints for each model—reducing hallucination and speeding up “from idea to working code."]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<strong></strong>按需实现指南: GitHub Copilot 会收到有关每个模型的详细集成文档和使用提示，以减少幻觉并加快“从构想到工作代码”的进程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Seamless Copilot Integration:</strong> GitHub Copilot is enhanced via MCP servers to understand model endpoints, available tools, and recommend best-fit models for your use case.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无缝 Copilot 集成:<strong></strong> GitHub Copilot 通过 MCP 服务器得到了增强，能够理解模型终结点、可用工具，并为你的用例推荐最合适的模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Generative Coding Without the Chaos:</strong> “Prototyping without the rabbit holes.” The MCP Server constrains and guides the AI, avoiding runaway file generation, dead ends, or coding spirals typical of other agentic tools.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有任何漏洞的生成式编码:<strong></strong>“一切尽在掌握的原型设计。”MCP 服务器会约束并指导 AI，从而避免其他代理工具中常见的文件生成失控、死胡同或编码螺旋问题。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<strong>Prototyping at Lightning Speed:</strong> Build evaluators, dashboards, analyzers, and bespoke AI apps in minutes. Typical initial working apps are generated in <10 minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以闪电般的速度进行原型设计: <strong></strong>几分钟内即可构建评估器、仪表板、分析器和定制 AI 应用。典型的初始工作应用可在不到 10 分钟内生成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MCP 服务器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MCPServerTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get started with the MCP Server]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开始使用 MCP 服务器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The Skala functional will enable more accurate, scalable predictions in computational chemistry. It starts with the largest high-accuracy dataset ever built for training deep-learning-based density functional theory (DFT) models. This dataset underpins Skala—coming soon to the Azure AI Foundry catalog—a new machine-learned exchange-correlation functional that reaches experimental accuracy for atomization energies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Skala 泛函将在计算化学中实现更准确且可缩放的预测。它始于为训练基于深度学习的密度泛函理论(DFT)模型而构建的有史以来最大的高准确度数据集。此数据集支持 Skala (即将发布到 Azure AI Foundry 目录)，这是一种新的机器学习的交换-相关泛函，可在原子化能方面达到实验准确度。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore MSR-ACC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 MSR-ACC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCShortenedTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MSR-ACC]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MSR-ACC]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This research marks a major advance in computational chemistry by dramatically improving the accuracy of <em>density functional theory</em> (DFT)—the most widely used method for simulating materials and molecules. The core breakthrough is a new deep-learning-based exchange-correlation (XC) functional, called Skala, which achieves <em>experimental-level accuracy</em> in predicting molecular properties like atomization energy—something previously thought out of reach for DFT. Skala will be available in the Azure AI Foundry catalog in the future. Researchers have released a large part of this dataset to the scientific community.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此项研究显著提升了密度泛函理论(DFT) - 模拟材料与分子的最常用方法 - 的准确度，标志着计算化学领域的重大进展。<em></em><em></em>其核心突破在于名为 Skala 的基于深度学习的交换-相关(XC)泛函，它在预测原子化能等分子属性方面达到了实验级精度，这在以往被认为是 DFT 难以企及的效果。Skala 将在将来的 Azure AI Foundry 目录中提供。研究人员已向科学界发布了此数据集的一大部分。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DFT is fast but limited by manual approximations of the XC functional. The research team addressed that limitation by generating the largest high-accuracy dataset of molecular energies to date, leveraging first principles methods and cloud-scale computation. They then trained Skala to learn directly from electron densities, bypassing hand-crafted feature engineering that has stalled progress for decades.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DFT 速度很快，但受限于对 XC 泛函的手动近似。该研究团队利用第一性原理方法和云规模计算，通过生成迄今为止最大的高准确度分子能量数据集，解决了这一局限。然后，他们训练 Skala 以直接从电子密度中学习，绕过了几十年来阻碍发展的手工打造的特征工程。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This achievement removes a long-standing barrier in computational chemistry, enabling DFT to shift from interpreting experimental results to predicting them reliably. That unlocks enormous potential across domains—from drug design to battery development—where accurate, affordable simulations can replace costly lab work.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这一成果消除了计算化学中一个长期存在的障碍，支持 DFT 从解释实验结果转变为可靠地预测实验结果。该成果解锁了从药物设计到电池开发等各个领域的巨大潜力 - 在这些领域中，准确且经济的模拟可以取代成本高昂的实验室工作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MSRACCTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Research Accurate Chemistry Collection (MSR-ACC)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Research 精确化学数据集(MSR-ACC)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Diagram with five items (‘Orchestrator,’ ‘Coder,’ ‘FileSurfer,’ ‘WebSurfer,’ ‘ComputerTerminal’) connected to a single point.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包含五个项目的图表 (“Orchestrator”、“Coder”、“FileSurfer”、“WebSurfer”、“ComputerTerminal”) 连接到一个点。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One is a generalist multi-agent system created to address intricate web and file-based tasks. By utilizing an intelligent Orchestrator alongside specialized agents, it facilitates the automation of complex, multi-step activities across various environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 是一个通用多代理系统，创建用于处理复杂的 Web 和基于文件的任务。通过将智能 Orchestrator 与专用代理一起使用，可以促进跨各种环境的复杂多步骤活动的自动化。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One introduces advancements in agentic AI through its modular architecture, which features a lead agent termed the Orchestrator. This component manages a network of specialized agents, enabling each to concentrate on specific tasks, such as web navigation, code execution, or local file management. This structure supports the efficient pursuit of complex objectives.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 通过其模块化体系结构引入了代理 AI 中的发展，该体系结构具有将潜在顾客代理称为 Orchestrator 的功能。此组件管理专用代理的网络，使每个代理能够集中处理特定任务，如 Web 导航、代码执行或本地文件管理。此结构支持有效实现复杂目标。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Central to Magentic-One’s operation are its dual planning mechanisms: the Task Ledger and the Progress Ledger. The Task Ledger empowers the Orchestrator to formulate strategic approaches, while the Progress Ledger provides real-time updates on task statuses. This interconnected system allows for ongoing evaluation and adjustment, optimizing overall efficiency. In situations where obstacles arise, the Orchestrator can adapt plans and reallocate tasks, ensuring effective workflow management under varying conditions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One 的操作的中心是其双重计划机制： 任务账本和进度账本。任务分类帐使 Orchestrator 能够加强战略方法，而进度账本则提供任务状态的实时更新。此互连系统允许进行评估和调整，优化整体效率。在出现障碍的情况下，Orchestrator 可以调整计划并重新分配任务，以确保在不同的条件下有效管理工作流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticOneTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-One]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Magentic-One]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 Magentic-One]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Try Magentic-One with AutoGen]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The image shows a bar chart comparing accuracy percentages across different systems on the medium subset of the GAIA benchmark.: Magentic-One (about 33%), Webby autonomous (about 38%), Webby + Simulated Human (about 58%), and Human (about 90%).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[该图显示了一个条形图，用于比较不同系统在 GAIA 基准的中等子集上的准确性百分比: Magentic-One (约 33%)、Webby 自主(约 38%)、Webby + 模拟人类(约 58%)和人类(约 90%)。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlocking the full potential of AI requires the development of effective mechanisms for human-AI collaboration. By reducing cognitive load while ensuring users remain in control, AI can significantly enhance human capabilities and streamline complex workflows. Magentic-UI was designed with this goal in mind, serving as a research platform aimed at advancing research on human-in-the-loop experiences.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了释放 AI 的全部潜力，需要开发支持人类与 AI 协作的有效机制。通过在减少认知负担的同时确保用户仍然具有控制权，AI 可以显著增强人类的能力并简化复杂的工作流。Magentic-UI 是为实现这一目标而设计的，可作为一个旨在推进人机交互体验研究的研究平台。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn about Magentic-UI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解 Magentic-UI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI builds on  Magentic-One, a generalist multi-agent system that specializes in complex web and file-based tasks, and is powered by {AutoGenLink}, our leading agent framework.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 基于 Magentic-One 构建。后者是一种通用多代理系统，专门用于处理基于 Web 和文件的复杂任务，由我们领先的代理框架 {AutoGenLink} 提供支持。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUIText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key features of the Magentic-UI research include:<ol><li><strong>Co-planning.</strong> Magentic-UI allows users to directly modify its plan through a plan editor or by providing textual feedback before Magentic-UI executes any actions.</li><li><strong>Co-tasking.</strong> Users can pause the system and give feedback in natural language or demonstrate it by directly taking control of the browser.</li><li><strong>Action guards.</strong> Magentic-UI seeks user approval before executing potentially irreversible actions, and the user can specify how often Magentic-UI needs approvals. Furthermore, Magentic-UI is sandboxed for the safe operation of tools such as browsers and code executors.</li><li><strong>Task learning.</strong> Magentic-UI can learn and save plans from previous interactions to improve task completion for future tasks.</li></ol>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI 研究的主要功能包括:<ol><li>共同制定规划。<strong></strong>Magentic-UI 允许用户直接通过计划编辑器或通过在 Magentic-UI 执行任何作之前提供文本反馈来修改其计划。</li><li>共同完成任务。<strong></strong>用户可以暂停系统并以自然语言提供反馈，或者通过直接控制浏览器来进行演示。</li><li>行动守卫。<strong></strong>Magentic-UI 会在执行可能不可逆的操作之前寻求用户批准，用户可以指定 Magentic-UI 需要审批的频率。此外，Magentic-UI 经过沙盒化，以确保浏览器和代码执行器等工具的安全运行。</li><li>任务学习。<strong></strong>Magentic-UI 可以从以前的交互中学习并保存计划，以改进未来任务的完成情况。</li></ol>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUITitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magentic-UI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magentic-UI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagenticUITryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Magentic-UI repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 Magentic-UI 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A mechanical employee operating a large green machine in an industrial setting. He’s using a control panel with his right hand and holding a tablet in the other.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一名机械员工在工业环境中作大型绿色机器。正在使用控制面板，右手拿着另一个平板电脑。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Internal wiring and components of a device with green check marks and orange circles indicating areas of interest.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[设备的内部接线和组件，具有绿色检查标记和橙色圆圈，表示感兴趣的区域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma is a multimodal foundation model designed to both understand and act in digital and physical environments. Magma builds on the foundation models paradigm that pretraining on a larger amount of more diverse datasets allows these models to generalize better to new tasks and environments. Magma can perceive visual and textual inputs and generate actions, whether it’s clicking a button in a user interface or grabbing a tool in the real world. This new model represents a significant step towards AI agents that can serve as general-purpose assistants.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma 是一种多模式基础模型，旨在了解数字和物理环境中的行为。基于基础模型参数的 Magma 生成，通过预训练大量更不同的数据集，这些模型可以更好地向新任务和环境进行通用化。无论是单击用户界面中的按钮还是在现实世界中抓取工具，Magma 都可以感知视觉和文本输入并生成作。此新模型表示面向可用作常规用途助手的 AI 代理的重要步骤。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A multimodal AI foundation model designed to both understand and act in digital and physical environments.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一种多模式 AI 基础模型，旨在了解数字和物理环境中的行为。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Magma]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览 Magma]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Imagine an AI assistant that can book a meeting online and also set up the room for it – navigating software menus as effortlessly as it moves physical objects. Such seamless integration of digital and physical tasks has long been a sci-fi vision. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Imagine an AI 助手 that can book a meeting online and also set the room for it – navigating software menus as effortly as it moves physical objects.数字任务和物理任务的这种无缝集成长期是一个科幻的视觉。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft researchers are bringing it closer to reality with Magma, a multimodal AI foundation model designed to both understand and act in digital and physical environments. Magma builds on the foundation models paradigm, that pretraining on a larger amount of more diverse datasets allows these models to generalize better to new tasks and environments. Magma can perceive visual and textual inputs and generate actions, whether it’s clicking a button in a user interface or grabbing a tool in the real world. This new model represents a significant step towards AI agents that can serve as general-purpose assistants. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft研究人员使用 Magma 拉近现实，Magma 是一种多模式 AI 基础模型，旨在了解数字和物理环境中的行为。Magma 基于基础模型参数生成，通过对更多不同数据集进行预训练，这些模型可以更好地向新任务和环境进行通用化。无论是单击用户界面中的按钮还是在现实世界中抓取工具，Magma 都可以感知视觉和文本输入并生成作。此新模型表示面向可用作常规用途助手的 AI 代理的重要步骤。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Vision-Language-Action (VLA) models are typically pretrained on large amounts of vision-language-action datasets to obtain the vision-language understanding ability (verbal intelligence) and the ability to perceive and interact with the visual spatial world to perform a wide range of tasks (spatial intelligence). However, due to the dramatic difference among various digital and physical environments, separate VLA models are trained and used for different environments. These models cannot easily generalize to new tasks and new environments that are unseen in training data. Moreover, most of these models do not leverage pretrained vision-language (VL) models or diverse vision-language datasets. As a result, their vision language understanding ability is often inferior to state-of-the-art VL models, which further limits model generalizability.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[视觉-语言-作 (VLA) 模型通常对大量视觉语言作数据集进行预训练，以获得视觉语言理解能力 (语言智能) 以及感知视觉空间世界并与之交互的能力，以执行各种任务 (空间智能)。但是，由于各种数字和物理环境之间的显著差异，单独的 VLA 模型经过训练并用于不同的环境。这些模型无法轻松地泛化为训练数据中未查看的新任务和新环境。此外，这些模型中的大多数不利用预先训练的视觉语言 (VL) 模型或不同的视觉语言数据集。因此，他们的视觉语言理解能力通常会被最先进的 VL 模型所影响，这进一步限制了模型的通用性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma, is a VLA foundation model that can adapt to downstream (unseen) agentic tasks in both the digital and physical environments. With Magma, researchers showed that it is beneficial to pretrain a single VLA model for AI agents across these environments while still achieving state-of-the-art results on UI navigation and robotic manipulation tasks, outperforming previous models that are tailored specifically to these tasks. On VL tasks, Magma also compares favorably to popular VL models that are trained on much larger datasets.  ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Magma 是一种 VLA 基础模型，可以适应下游 (在数字和物理环境中看不到的) 代理任务。借助 Magma，研究人员表明，对于跨这些环境中的 AI 代理预先训练单个 VLA 模型十分有用，同时在 UI 导航和机器人作任务上仍达到最先进的结果，同时会验证专门为这些任务量身定制的先前模型。在 VL 任务上，Magma 还可与在较大数据集上训练的热门 VL 模型进行比较。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Magma]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[岩浆]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MagmaTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Magma model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 Magma 模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Various molecular structures and crystal lattices displayed in a grid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[网格中显示的各种分子结构和晶体񝬡板。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim is a deep learning model for accurate and efficient materials simulation and property prediction over a broad range of elements, temperatures and pressures to enable in silico materials design.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 是一种深度学习模型，用于对各种元素进行准确高效的材料模拟和属性预测，以及精准的元素，以在弹性材料设计中启用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore MatterSim]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览 MatterSim]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim employs deep learning to understand atomic interactions from the very fundamental principles of quantum mechanics, across a comprehensive spectrum of elements and conditions—from 0 to 5,000 Kelvin (K), and from standard atmospheric pressure to 10,000,000 atmospheres.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 利用深度学习来了解量子机制的非常基本原则中的原子交互，包括各种元素和条件，范围从 0 到 5,000 Kelvin (K)，以及从标准的气压到 10,000,000 个空气。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[MatterSim is a deep-learning model for accurate and efficient materials simulation and property prediction over a broad range of elements, temperatures, and pressures to enable the in silico materials design. MatterSim employs deep learning to understand atomic interactions from the very fundamental principles of quantum mechanics, across a comprehensive spectrum of elements and conditions—from 0 to 5,000 Kelvin (K), and from standard atmospheric pressure to 10,000,000 atmospheres.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim efficiently handles simulations for a variety of materials, including metals, oxides, sulfides, halides, and their various states such as crystals, amorphous solids, and liquids. Additionally, it offers customization options for intricate prediction tasks by incorporating user-provided data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim 高效地处理各种材料的模拟，包括金属、金属、镍、镍镑及其各种状态，如晶体、无态实体和液体。此外，它还通过合并用户提供的数据为复杂预测任务提供自定义选项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[MatterSim]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[MatterSim]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MatterSimTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get MatterSim model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 MatterSim 模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Try MatterSim]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Developed by Microsoft Research in collaboration with game studio Ninja Theory, Muse is a World and Human Action Model (WHAM) - a generative AI model of a video game that can generate game visuals, controller actions, or both. Trained exclusively on the game Bleeding Edge, researchers and game creatives can explore how these model capabilities will have potential to accelerate their creativity in the future.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Muse 由 Microsoft Research 与 game studio Ninja Research 协作开发，是一个世界和人类作模型，(WHAM) - 视频游戏的生成 AI 模型，可生成游戏视觉对象和控制器作，或同时生成两者。专门在游戏《天分边缘》上训练，研究人员和游戏创意者可以探索这些模型功能在将来如何提高其创造力。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Developed by Microsoft Research in collaboration with game studio Ninja Theory, Muse is a World and Human Action Model (WHAM) - a Generative AI of a video game that can generate game visuals, controller actions, or both. Trained exclusively on the game Bleeding Edge, researchers and game creatives can explore how these model capabilities will have potential to accelerate their creativity in the future.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseDownloadDemo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download demonstrator app]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载下载应用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A generative AI model that can generate visuals and controller actions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可生成视觉对象和控制器作的生成 AI 模型。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[A Generative AI model that can generate visuals and controller actions.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Muse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索墨斯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse was trained on more than 1 billion images and controller actions, from the game Bleeding Edge, corresponding to over 7 years of continuous human gameplay.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从游戏《Edge》中训练了超过 10 亿张图像和控制器作，对应于 7 年持续的人类游戏。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The WHAM demonstrator app provides a visual interface for interacting with a deployment of the Muse model instance on Azure AI Foundry. Creators can load a screenshot from Bleeding Edge as an initial prompt, then use the model to generate multiple potential continuations of gameplay from this starting point. They can then explore the generated sequences and tweak them, such as changing the controller inputs or pasting game elements into the scene and predicting what will happen as a result. These features demonstrate how Muse’s capabilities could someday enable AI-supported iteration and brainstorming as part of the creative process.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[WHAM 实用应用提供了一个可视界面，用于与Azure AI Foundry上部署的Muse 模型实例进行交互。创建者可以从《流星边缘》加载屏幕截图作为初始提示，然后使用模型从此起点生成多个潜在的游戏延续。然后，它们可以浏览生成的序列并对其进行调整，例如更改控制器输入或将游戏元素粘贴到场景中，并预测结果将发生的情况。这些功能演示了第一天，用户的功能如何在创意过程中启用 AI 支持的迭代和灵感触发。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The WHAM demonstrator app provides a visual interface for interacting with a deployment of the Muse model instance on Azure Foundry. Creators can load a screenshot from Bleeding Edge as an initial prompt, then use the model to generate multiple potential continuations of gameplay from this starting point. They can then explore the generated sequences and tweak them, such as changing the controller inputs or pasting game elements into the scene and predicting what will happen as a result. These features demonstrate how Muse’s capabilities could someday enable AI-supported iteration and brainstorming as part of the creative process.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Muse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[缪斯]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Muse model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取墨斯模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Focus on the grid of 9 AI generated gaming video frames by the Muse model set in the same scene. On the left, showing 3 ground truth (original) scenes that are compared to the AI generated frames.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*DECORATIVE* 聚焦于 9 个 AI 生成的游戏视频帧的网格，这些视频帧由在相同场景中设置的用户模型设定。在左侧，显示 3 个地面真实 (与 AI 生成的帧比较的原始) 场景。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by the Muse model set in the same scene. The frames highlight differences when the game player takes a left, center, or right path.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by the Muse model set in the same scene.当游戏玩家采用左侧、居中或右侧路径时，框架突出显示差异。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.MuseVideoAltText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by the Muse model crafted in the creator tool. This game creator surface showcases and Xbox controller that can be used to impact the AI generate frames.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[*DECORATIVE* Grid of 9 AI generated gaming video frames by theMuse model crafted in the creator tool.此游戏创建者展示和 Xbox 控制器，可用于影响 AI 生成帧。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Navigation.NextArticleText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下一步]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Next article]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Navigation.PreviousArticleText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Previous]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上一步]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Previous article]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enhancing the ability of coding models to handle diverse editing requirements]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[增强编码模型处理多样化编辑需求的能力]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover NextCoder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Software engineering activities frequently involve edits to existing code. However, contemporary code language models lack the ability to handle diverse types of code-edit requirements. In this work, Microsoft researchers attempt to overcome this shortcoming through a novel synthetic data generation pipeline and a robust model adaptation algorithm. Starting with seed code examples and diverse editing criteria, their pipeline generates high-quality samples comprising original and modified code, along with natural language instructions in different styles and verbosity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[软件工程活动通常涉及对现有代码的编辑。但是，现代代码语言模型缺乏处理多样化代码编辑需求的能力。在这项工作中，Microsoft 研究人员尝试通过新型合成数据生成管道和强大的模型适应算法来克服这一短板。其管道从种子代码示例和多样化编辑标准入手，生成包含原始代码与修改后代码的高质量样本，以及不同风格和详细程度的自然语言指令。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Today’s code LMs come bundled with strong abilities, such as code generation and instruction following, which should not be lost due to fine-tuning. To ensure this, researchers proposed a novel adaptation algorithm, SeleKT, that (a) leverages a dense gradient-based step to identify the weights that are most important for code editing, and (b) does a sparse projection onto the base model to avoid overfitting.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如今的代码 LM 具备强大的功能(如代码生成和指令跟随)，这些功能不应因微调而丧失。为此，研究人员提出新型适应算法 SeleKT，该算法(a)可利用基于密集梯度的步骤来识别对代码编辑最重要的权重; 并(b)对基础模型进行稀疏投影以避免过度拟合。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Using this approach, researchers obtained a new series of models called NextCoder (adapted from QwenCoder-2.5) that achieves strong results on five code-editing benchmarks, outperforming comparable size models and even several larger ones. In their research paper, they demonstrate the generality of their approach on two model families (DeepSeekCoder and QwenCoder), compare against other fine-tuning approaches, and demonstrate robustness by showing retention of code generation and general problem-solving abilities post adaptation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过此方法，研究人员获得了一系列名为 NextCoder 的新模型(改编自 QwenCoder-2.5)，这些模型在五个代码编辑基准测试中取得了优异的成绩，表现优于同等规模模型，甚至一些更大的模型。这些研究人员在其研究论文中展示了其方法在两个模型系列(DeepSeekCoder 和 QwenCoder)上的通用性，将这些方法与其他微调方法对比，并通过展示适应后保留代码生成和解决一般问题的能力来展示其可靠性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are now available for experimental purposes on Azure AI Foundry.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这些模型现在可用于Azure AI Foundry 上的实验性用途。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[NextCoder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.NextCoderTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Try NextCoder in Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Foundry 中试用 NextCoder]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Parsed Teams screenshot image by OmniParser.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由 OmniParser 分析的 Teams 屏幕截图图像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is an advanced vision-based screen parsing module that converts user interface (UI) screenshots into structured elements, allowing agents to execute actions across various applications using visual data . By harnessing large vision-language model capabilities, OmniParser improves both efficiency and accuracy in UI interactions. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 是一个基于视觉的高级屏幕分析模块，可将用户界面 (UI) 屏幕截图转换为结构化元素，使代理能够使用可视数据跨各种应用程序执行作。通过利用大型视觉语言模型功能，OmniParser 可提高 UI 交互的效率和准确性。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[OmniParser is an advanced vision-based screen parsing module that converts user interface (UI) screenshots into structured elements, allowing agents to execute actions across various applications using visual data . By harnessing large vision-language model capabilities, OmniParser improves both efficiency and accuracy in UI interactions.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHighlight1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Turn any LLM into a computer use agent ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将任何 LLM 转换为计算机使用代理 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore OmniParser V2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览 OmniParser V2]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Click to learn more!]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Recent developments in large vision-language models (VLMs), such as GPT-4V and GPT-4o, showcase their potential in creating agent systems that integrate smoothly within user interfaces. However, the practical application of these multimodal models, especially as general agents across different operating systems, faces challenges. A significant barrier to progress has been the absence of reliable screen parsing techniques that can effectively identify interactable icons and link intended actions to specific screen regions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大型视觉语言模型 (VLM)（如 GPT-4V 和 GPT-4o） 的最新发展展示其在创建在用户界面中顺利集成的代理系统方面的潜在客户。但是，这些多模式模型的实际应用，特别是作为不同操作系统中的常规代理，面临挑战。进度的一个严重障碍是缺少可靠的屏幕分析技术，这些技术可以有效地识别可交互的图标并将预期操作链接到特定屏幕区域。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser addresses this limitation through its compact and powerful architecture. It transforms UI screenshots into structured output elements, enabling the design of agents that can perform precise actions across various applications. When combined with models like GPT-4V, OmniParser markedly improves the agent's capability to engage accurately with user interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 通过其紧凑强大的体系结构解决此限制。它会将 UI 屏幕截图转换为结构化输出元素，从而启用可跨各种应用程序执行精确操作的代理的设计。与 GPT-4V 等模型结合使用时，OmniParser 会显著提高代理与用户界面准确互动的功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser V2 takes this capability to the next level. Compared to its predecessor, It achieves higher accuracy in detecting smaller interactable elements and faster inference, making it a useful tool for GUI automation. In particular, OmniParser V2 is trained with larger size of interactive element detection data and icon functional caption data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser V2 将此功能带入下一级别。与前置任务相比，它在检测较小的可交互元素和更快的推理时实现更高的准确性，使其成为用于 GUI 自动化的有用工具。特别是，使用较大大小的交互式元素检测数据和图标功能描述文字数据训练了 OmniParser V2。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[The creation of OmniParser involved the development of specialized datasets, including an interactable icon detection dataset that identifies actionable regions within popular web pages, and an icon description dataset that correlates UI elements with their functions. These resources are crucial for training the detection and captioning models utilized by OmniParser. The detection model, specifically fine-tuned on the interactable icon dataset, reliably locates actionable screen regions, while the captioning model provides contextually relevant descriptions for the detected elements.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The creation of OmniParser involved the development of specialized datasets, including an interactable icon detection dataset that identifies actionable regions within popular web pages, and an icon description dataset that correlates UI elements with their functions. These resources are crucial for training the detection and captioning models utilized by OmniParser. The detection model, specifically fine-tuned on the interactable icon dataset, reliably locates actionable screen regions, while the captioning model provides contextually relevant descriptions for the detected elements.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建 OmniParser 涉及开发专用数据集，包括可交互的图标检测数据集，该数据集标识常用网页中的可作区域，以及一个将 UI 元素与其函数相关联的图标说明数据集。这些资源对于训练由 OmniParser 使用的检测和字幕模型至关重要。检测模型，特别是对可交互图标数据集进行微调，可可靠地定位可作的屏幕区域，而字幕模型为检测到的元素提供上下文相关说明。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[OmniParser is designed to be modular and adaptable, enhancing interactions across both PC and mobile platforms.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser is designed to be modular and adaptable, enhancing interactions across both PC and mobile platforms.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser 设计为模块化且可适应，可增强电脑和移动平台之间的交互。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[OmniParser V2]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[OmniParser V2]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[OmniParser]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.OmniParserTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open OmniParser repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 OmniParser 存储库]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Get OmniParser V2 model]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A colorful geological map with various regions marked in different colors and a detailed legend in Chinese on the right side.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一幅彩色地质图，上面用不同的颜色标记了各个区域，右侧有详细的中文图例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE enhances multimodal large language models (MLLMs) with geologic expertise, enabling accurate interpretation of complex, high-resolution maps. By integrating structured extraction, domain knowledge, and reasoning, it supports critical tasks in disaster risk, resource discovery, and infrastructure planning—turning general AI into a specialized tool for geoscience.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 利用地质专业知识增强了多模式大语言模型(MLLM)，可准确解读复杂的高分辨率地图。通过整合结构化提取、领域知识以及推理能力，它可支持处理灾害风险、资源勘探及基础设施规划等关键任务，从而将生成式 AI 转变为面向地球科学领域的专业化工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore PEACE]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 PEACE]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE (emPowering gEologic mAp holistiC undErstanding) enhances multimodal large language models (MLLMs) for expert-level geologic map understanding. Geologic maps, which provide critical insights into the structure and composition of Earth’s subsurface and surface, are vital tools in disaster detection, resource exploration, and civil engineering. But their complexity—featuring high-resolution visuals, symbolic representations, and domain-specific knowledge—poses significant challenges for current AI models. General-purpose MLLMs often fall short when interpreting such data due to the intricacies of cartographic generalization and geoscientific reasoning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE (emPowering gEologic mAp holistiC undErstanding)增强了多模式大语言模型(MLLM)，以实现专家级的地质图理解能力。地质地图可提供有关地球地下和地表结构及组成的关键见解，是灾害检测、资源勘探以及土木工程领域中的重要工具。但其复杂性(包括高分辨率视觉对象、符号化表示形式以及特定领域的知识)给当前的 AI 模型带来了重大挑战。由于制图综合以及地球科学推理方面的复杂性，通用多模式大语言模型(MLLM)在解释此类数据时通常表现欠佳。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To bridge this gap, Microsoft researchers and collaborators introduced GeoMap-Bench, the first benchmark specifically designed to evaluate MLLMs across five capabilities essential to geologic map interpretation: extracting, referring, grounding, reasoning, and analyzing. They also developed GeoMap-Agent, an AI system tailored to these challenges.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了弥合这一差距，Microsoft 研究人员及其协作者引入了 GeoMap-Bench，这是首个旨在评估 MLLM 的基准检验，评估对象包括解释地质地图所必需的五项能力: 提取、引用、基础设置、推理和分析。他们还开发了针对这些挑战而定制的 AI 系统 - GeoMap-Agent。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[GeoMap-Agent is composed of three key modules:]A;<ul><li><strong>Hierarchical Information Extraction (HIE)</strong> for parsing structured content from complex maps,</li><li><strong>Domain Knowledge Injection (DKI)</strong> for embedding geological expertise, and</li><li><strong>Prompt-enhanced Question Answering (PEQA)</strong> for improved interpretive and reasoning capabilities.</li></ul>Together, these modules enable GeoMap-Agent to outperform existing models with superior accuracy and depth in geologic tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[GeoMap-Agent 由三个关键模块组成:]A;<ul><li><strong>分层信息提取(HIE)</strong>，用于分析复杂地图中的结构化内容、</li><li><strong>领域知识注入(DKI)</strong>，用于嵌入地质学专业知识，以及</li><li><strong>提示增强问答(PEQA)</strong>，用于改进解释和推理能力。</li></ul>借助这些协同工作的模块，GeoMap-Agent 在地质任务中能够以更高的准确性和深度超越现有模型。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACEText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rather than modifying MLLMs themselves, PEACE builds intelligent, domain-specific layers on top of them, turning general models into specialized agents capable of handling real-world geoscientific problems. This advancement marks a critical step toward applying AI in Earth science, empowering faster, more accurate geological assessments for both researchers and practitioners.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 并非对多模式大语言模型(MLLM)本身进行修改，而是基于这些模型构建特定于领域的智能层，从而将通用模型转变为能够处理现实地球科学问题的专业助理。这一进展标志了 AI 在地球科学领域应用的关键一步，可助力研究人员和从业者实现更快速、更准确的地质评估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open PEACE repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 PEACE 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.PEACETryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[PEACE has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[PEACE 已发布用于研究目的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4BodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The text 'Phi-4' in glowing white letters on a purple and blue gradient background]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[紫色和蓝色渐变背景上以发光白色字母显示的文本 “Phi-4”]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4BodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Table showing Phi-4 benchmarks against comparable models]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[表格显示了针对可比较模型的 Phi-4 基准检验]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore the capabilities of Phi-4, the latest model in Microsoft's Phi family of advanced AI technologies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Microsoft 先进 AI 技术 Phi 系列的最新模型 Phi-4 的功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4HomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about Microsoft Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解 Microsoft Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal and Phi-4-mini, the newest models in Microsoft’s Phi family of small language models (SLMs) are now available. These models are designed to empower developers with advanced AI capabilities. Phi-4-multimodal, with its ability to process speech, vision, and text simultaneously, opens new possibilities for creating innovative and context-aware applications. Phi-4-mini, on the other hand, excels in text-based tasks, providing high accuracy and scalability in a compact form.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作为 Microsoft 的 Phi 系列小型语言模型(SLM)中的最新模型，Phi-4-multimodal 和 Phi-4-mini 现已发布。这些模型旨在为开发人员提供先进的 AI 功能。借助可同时处理语音、视觉和文本的功能，Phi-4-multimodal 为创建创新性和上下文感知应用程序提供了新的可能性。另一方面，Phi-4-mini 在基于文本的任务中表现优异，以紧凑的形式提供了高准确性和可伸缩性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal marks a new milestone in Microsoft’s AI development as our first multimodal language model. At the core of innovation lies continuous improvement, and that starts with listening to our customers. In direct response to customer feedback, we’ve developed Phi-4-multimodal, a 5.6B parameter model, that seamlessly integrates speech, vision, and text processing into a single, unified architecture. By leveraging advanced cross-modal learning techniques, this model enables more natural and context-aware interactions, allowing devices to understand and reason across multiple input modalities simultaneously. Whether interpreting spoken language, analyzing images, or processing textual information, it delivers highly efficient, low-latency inference—all while optimizing for on-device execution and reduced computational overhead. Natively built for multimodal experiences Phi-4-multimodal is a single model with mixture-of-LoRAs that includes speech, vision, and language, all processed simultaneously within the same representation space. The result is a single, unified model capable of handling text, audio, and visual inputs seamlessly—no need for complex pipelines or separate models for different modalities. The Phi-4-multimodal is built on a new architecture that enhances efficiency and scalability. It incorporates a larger vocabulary for improved processing, supports multilingual capabilities, and integrates language reasoning with multimodal inputs. All of this is achieved within a powerful, compact, highly efficient model that’s perfectly suited for deployment on devices and edge computing platforms. This breakthrough model represents a major leap forward in AI technology, offering unprecedented performance in a small package. Whether you’re looking for advanced AI capabilities on mobile devices or edge systems, Phi-4-multimodal provides a high-capability option that’s both efficient and versatile. With its impressive range of capabilities and flexibility, Phi-4-multimodal opens exciting new possibilities for app developers, businesses, and industries looking to harness the power of AI in innovative ways. The future of multimodal AI is here, and it’s ready to transform your applications. Phi-4-multimodal is capable of processing both visual and audio together. The following table shows the model quality when the input query for vision content is synthetic speech on chart/table understanding and document reasoning tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[作为我们的首个多模式语言模型，Phi-4-multimodal 是 Microsoft AI 开发工作中的新的里程碑。创新的核心在于持续改进，这始于倾听客户的声音。为了直接响应客户反馈，我们开发了 Phi-4-multimodal，这是一个 56 亿参数模型，它将语音、视觉和文本处理无缝集成到一个统一的体系结构中。通过利用先进的跨模式学习技术，此模型可实现更自然和上下文感知的交互，从而支持设备同时理解和解释多个输入模式。无论是解释口语、分析图像还是处理文本信息，它都可以提供高效、低延迟的推理，同时针对设备端运行进行了优化，并降低了计算开销。Phi-4-multimodal 为通过多模式体验原生构建，它是一款集成混合式 LoRA 的单一模型，包括语音、视觉和语言，所有这些都在同一表示空间内同时处理。其成果是一个单一且统一的模型，能够无缝处理文本、音频和视觉输入，且无需针对不同模式使用复杂的管道或单独的模型。Phi-4-multimodal 基于全新的体系结构，能够提高效率和可伸缩性。它纳入了更大量的词汇来改进处理，支持多语言功能，并将语言推理与多模式输入相结合。所有这些都是在功能强大、紧凑、高效的模型中实现的，非常适合在设备和边缘计算平台上进行部署。这款突破性的模型代表了 AI 技术的重大飞跃，通过小型程序包提供了前所未有的性能。无论你是在移动设备还是边缘系统上寻求先进 的 AI 功能，Phi-4-multimodal 均可提供兼具效率和通用性的高性能选择。凭借令人印象深刻的广泛能力和灵活性，Phi-4-multimodal 为希望以创新方式利用 AI 功能的应用开发者、企业和行业带来了新的令人兴奋的可能性。多模式 AI 的未来已经到来，它已准备好革新你的应用程序。Phi-4-multimodal 能够同时处理视觉和音频。下表显示了在图表/表格理解和文档推理任务中，当视觉内容的输入查询为图合成语音时的模型质量。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-multimodal is capable of processing both visual and audio together. The following table shows the model quality when the input query for vision content is synthetic speech on chart/table understanding and document reasoning tasks. Compared to other existing state-of-the-art omni models that can enable audio and visual signals as input, Phi-4-multimodal achieves much stronger performance on multiple benchmarks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-multimodal 能够同时处理视觉和音频。下表显示了在图表/表格理解和文档推理任务中，当视觉内容的输入查询为图合成语音时的模型质量。与其他现有能够将音频和视觉信号作为输入的最先进全能模型相比，Phi-4-multimodal 在多个基准检验中均实现了更加出色的性能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Phi-4-mini is a 3.8B parameter model and a dense, decoder-only transformer featuring grouped-query attention, 200,000 vocabulary, and shared input-output embeddings, designed for speed and efficiency. Despite its compact size, it continues outperforming larger models in text-based tasks, including reasoning, math, coding, instruction-following, and function-calling. Supporting sequences up to 128,000 tokens, it delivers high accuracy and scalability, making it a powerful solution for advanced AI applications. Function calling, instruction following, long context, and reasoning are powerful capabilities that enable small language models like Phi-4-mini to access external knowledge and functionality despite their limited capacity. Through a standardized protocol, function calling allows the model to seamlessly integrate with structured programming interfaces. When a user makes a request, Phi-4-Mini can reason through the query, identify and call relevant functions with appropriate parameters, receive the function outputs, and incorporate those results into its responses. This creates an extensible agentic-based system where the model’s capabilities can be enhanced by connecting it to external tools, application program interfaces (APIs), and data sources through well-defined function interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Phi-4-mini 是一款 38 亿参数密集型纯解码器 Transformer 模型，具备分组查询注意力、200,000 词汇与共享输入输出嵌入，专为实现速度与效率而设计。尽管体积小巧，但它在基于文本的任务方面持续领先于更大的模型，其中包括推理、数学、编码、指令跟随和函数调用。它支持最多 128,000 个令牌的序列，可提供高准确性和可伸缩性，从而成为面向先进 AI 应用程序的强大解决方案。尽管容量有限，但强大的函数调用、指令跟随、长上下文和推理功能可支持 Phi-4-mini 等小型语言模型访问外部知识和功能。通过标准化协议，函数调用允许模型与结构化编程接口无缝集成。当用户发出请求时，Phi-4-Mini 可以通过查询进行推理，使用适当的参数标识和调用相关函数，接收函数输出，并将这些结果纳入到其响应中。这将创建一个基于代理的可扩展系统，通过定义完善的函数接口将其连接到外部工具、应用程序接口(API)和数据源，可以增强模型的功能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Text5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[These models are designed to handle complex tasks efficiently, making them ideal for edge case scenarios and compute-constrained environments. Given the new capabilities Phi-4-multimodal and Phi-4-mini bring, the uses of Phi are only expanding. Phi models are being embedded into AI ecosystems and used to explore various use cases across industries.]A;]A;<strong>Embedded directly to your smart device:</strong> Integrating Phi-4-multimodal directly into a smartphone could enable smartphones to process and understand voice commands, recognize images, and interpret text seamlessly. Users could benefit from advanced features like real-time language translation, enhanced photo and video analysis, and intelligent personal assistants that understand and respond to complex queries. This would elevate the user experience by providing powerful AI capabilities directly on the device, ensuring low latency and high efficiency.]A;]A;<strong>On the road:</strong> Imagine an automotive company integrating Phi-4-multimodal into their in-car assistant systems. The model could enable vehicles to understand and respond to voice commands, recognize driver gestures, and analyze visual inputs from cameras. For instance, it could enhance driver safety by detecting drowsiness through facial recognition and providing real-time alerts. Additionally, it could offer seamless navigation assistance, interpret road signs, and provide contextual information, creating a more intuitive and safer driving experience while connected to the cloud and offline when connectivity isn't available.]A;]A;<strong>Multilingual financial services:</strong> Imagine a financial services company integrating Phi-4-mini to automate complex financial calculations, generate detailed reports, and translate financial documents into multiple languages. For instance, the model can assist analysts by performing intricate mathematical computations required for risk assessments, portfolio management, and financial forecasting. Additionally, it can translate financial statements, regulatory documents, and client communications into various languages and could improve client relations globally.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这些模型旨在高效处理复杂任务，使其非常适合边缘事例方案和受到计算约束的环境。鉴于 Phi-4-multimodal 和 Phi-4-mini 带来的新功能，Phi 正在不断拓展应用领域。Phi 模型正在嵌入到 AI 生态系统中，并被用于跨行业探索各种用例。]A;]A;直接嵌入到智能设备: 通过将 Phi-4-multimodal 直接集成到智能手机，可支持智能手机无缝处理和理解语音命令、识别图像和解读文本<strong></strong>。用户可以受益于实时语言翻译、增强的照片和视频分析，以及理解和响应复杂查询的智能个人助理等高级功能。通过直接在设备上提供强大的 AI 功能，这会提升用户体验，确保低延迟和高效率。]A;]A;道路交通: 假设一家汽车公司将 Phi-4-multimodal 集成到其车内助手系统中<strong></strong>。借助该模型，车辆能够理解和响应语音命令、识别驾驶员手势，以及分析摄像头中的视觉输入。例如，通过面部识别检测驾驶员困倦状态并提供实时警报，它可以提高驾驶员的行车安全。此外，无论是连接到云还是无法连接的脱机情况下，它都能够提供无缝的导航协助、解释路标和提供上下文信息，从而提供更直观和更安全的驾驶体验。]A;]A;多语言金融服务: 假设一家金融服务公司集成 Phi-4-mini 来自动执行复杂的财务计算、生成详细报表，并将财务文档翻译为多种语言<strong></strong>。例如，模型可以通过执行风险评估、项目组合管理和财务预测所需的复杂数学计算来帮助分析师开展工作。此外，它可以将财务报表、法规文档和客户通信翻译为各种语言，并可在全球范围内改善客户关系。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[These models are designed to handle complex tasks efficiently, making them ideal for edge case scenarios and compute-constrained environments. Given the new capabilities Phi-4-multimodal and Phi-4-mini bring, the uses of Phi are only expanding. Phi models are being embedded into AI ecosystems and used to explore various use cases across industries.]A;]A;Embedded directly to your smart device: Integrating Phi-4-multimodal directly into a smartphone could enable smartphones to process and understand voice commands, recognize images, and interpret text seamlessly. Users could benefit from advanced features like real-time language translation, enhanced photo and video analysis, and intelligent personal assistants that understand and respond to complex queries. This would elevate the user experience by providing powerful AI capabilities directly on the device, ensuring low latency and high efficiency.]A;]A;On the road: Imagine an automotive company integrating Phi-4-multimodal into their in-car assistant systems. The model could enable vehicles to understand and respond to voice commands, recognize driver gestures, and analyze visual inputs from cameras. For instance, it could enhance driver safety by detecting drowsiness through facial recognition and providing real-time alerts. Additionally, it could offer seamless navigation assistance, interpret road signs, and provide contextual information, creating a more intuitive and safer driving experience while connected to the cloud and offline when connectivity isn't available.]A;]A;Multilingual financial services: Imagine a financial services company integrating Phi-4-mini to automate complex financial calculations, generate detailed reports, and translate financial documents into multiple languages. For instance, the model can assist analysts by performing intricate mathematical computations required for risk assessments, portfolio management, and financial forecasting. Additionally, it can translate financial statements, regulatory documents, and client communications into various languages and could improve client relations globally.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.Phi4TryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover Phi-4]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 Phi-4]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieBodyImageAltText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bar chart comparing agent performance by ML task complexity between AIDE and Project Amelie (powered by R&D-Agent). The chart shows performance metrics (%) across different complexity levels (Low/Lite, Medium, High) with Project Amelie achieving 22.4% overall performance compared to AIDE's 16.9%. Project Amelie shows improved performance across all complexity categories, with the most significant improvement in the Low/Lite category represented by a light blue section at the bottom of each bar.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按 ML 任务复杂性比较 AIDE 和 Project Amelie (由 R&D-Agent 提供支持)的代理性能的条形图。图表显示了不同复杂性级别(低/精简、中、高)的性能指标(%)，其中 Project Amelie 的总体性能为 22.4%，而 AIDE 为 16.9%。Project Amelie 在所有复杂性类别中均表现出性能提升，尤其是在低/精简类别中(由每个竖条底部的浅蓝色部分表示)提升显著。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A demo of using the Project Amelie agent to predict accommodation rental prices in Seattle. The Agent builds a regression model, answers questions about it, and provides code to run the model. The user then opens the code in VS Code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Project Amelie 代理预测西雅图的租赁价格的演示。该代理会生成一个回归模型，回答有关这方面的问题，并提供代码来运行该模型。然后，用户在 VS Code 中打开代码。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[With Project Amelie, we are unveiling our first Foundry autonomous agent that can perform machine learning engineering tasks. ML teams can use the agent to initiate complex machine learning tasks using prompts —such as, “Help me create a model to predict customer churn"—and receive fully validated ML pipelines, detailed evaluation metrics, trained model, and ready-to-use, reproducible Python code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过 Project Amelie，我们将推出第一个能够执行机器学习工程任务的 Foundry 自治代理。机器学习团队可以使用该代理通过提示(例如“帮助我创建一个预测客户流失情况的模型”)启动复杂的机器学习任务，并接收经过完全验证的 ML 管道、详细的评估指标、训练好的模型以及可重复使用的现成 Python 代码。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Project Amelie]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Project Amelie]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieSignUpLink1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sign up for Private Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注册个人预览版]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieSignUpLink2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[sign up here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此处注册]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{SignUpLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{SignUpLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<em>Private Preview Coming Soon! Sign up to get early access and opportunity to share feedback. Approved users can explore and experiment with Project Amelie.</em>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<em>即将推出个人预览版!注册即可抢先体验并有机会提供反馈。获得批准的用户可以探索和试用 Project Amelie。</em>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[At its core, Project Amelie is powered by innovation from Microsoft Research designed specifically to automate and optimize research and development processes in machine learning.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从其核心来看，Project Amelie 依托于 Microsoft Research 专门为自动执行和优化机器学习中的研发过程而进行的创新。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie outperforms the current state of the art benchmarks on MLE-Bench by OpenAI, which measures MLE agent’s effectiveness on real world ML engineering tasks.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie 超越了 OpenAI 的 MLE-Bench 上当前最先进的基准，后者用于衡量 MLE 代理在现实世界的 ML 工程任务中的有效性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are currently in early beta and plan to launch Private Preview soon! If you are interested in getting early access to Project Amelie and sharing your insights to help shape the product, please {SignUpLink}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们目前处于早期测试阶段，并计划很快推出个人预览版!如果你有兴趣抢先体验 Project Amelie 并分享你的见解以帮助塑造产品，请{SignUpLink}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ProjectAmelieTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project Amelie]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Project Amelie]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A healthcare professional in scrubs sits with an older adult in a care facility, holding a tablet displaying a meal. In the foreground, a smartphone screen shows the ReMe app interface with options for game training, user feedback, and starting a conversation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在一家护理机构里，一位穿着医护服的医疗专业人员坐在一位老人身边，手中拿着一台显示餐食的平板电脑。在前景中，智能手机屏幕显示了 ReMe 应用的界面，界面中提供游戏训练、用户反馈和开始对话的选项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe is a web-based framework that helps researchers create AI chatbots for personalized training and interventions aimed at strengthening memory and cognitive functions. Early evaluations show its potential to contribute to digital health innovation and advance non-pharmacological approaches to cognitive health.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 是一个基于 Web 的框架，可帮助研究人员创建 AI 聊天机器人，以开展个性化训练和实施干预，旨在增强记忆力和认知功能。早期评估显示了它在为数字健康创新做出贡献，以及推进针对认知健康的非药物方法发展方面的潜力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore ReMe]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 ReMe]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe is a web-based framework designed to accelerate research into personalized cognitive training using AI chatbots. As cognitive decline becomes a growing public health concern, ReMe supports researchers, clinicians, and caregivers in developing interactive training tasks focused on episodic memory and open-ended cognitive challenges.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 是一个基于 Web 的框架，旨在使用 AI 聊天机器人加速对个性化认知训练的研究。随着认知衰退成为日益受到关注的公共健康问题，ReMe 将支持研究人员、临床医生以及护理人员开发专注于情景记忆和开放式认知挑战的交互式训练任务。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The framework integrates a puzzle engine, a life-logging module for personal memory recall, and a multimodal training interface featuring text, image, and voice capabilities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[该框架集成了解谜引擎、用于个人记忆唤起的生活日志记录模块，以及具备文本、图像和语音功能的多模式训练界面。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cognitive training is one of the few non-pharmacological methods shown to help delay decline, but existing programs are often generic and not very engaging. ReMe aims to make cognitive training more personalized and adaptable while reaching more people at lower cost.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[认知训练是少数已被证明有助于延缓衰老的非药物方法之一，但现有程序通常过于通用化，且缺乏吸引力。ReMe 旨在提高认知训练的个性化程度和适应性，同时以更低成本惠及更多人。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instead of building every new cognitive training chatbot from scratch, researchers can use ReMe to prototype, test, and improve interventions more quickly, speeding up discovery of what works.  ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[研究人员可以使用 ReMe 更快地创建原型、进行测试和改进干预，从而加快发现有效的工作方法，而不是从头开始构建每个新的认知训练聊天机器人。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[In initial evaluations, ReMe was well received by users citing strong conversational fluency and moderate difficulty. While not intended for clinical treatment, ReMe provides a valuable tool for exploring AI’s role in supporting cognitive health, paving the way for future innovations in personalized digital therapies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在初步评估中，ReMe 受到用户的好评，认为其具有很高的对话流畅性，并且难度适中。虽然 ReMe 并非用于临床治疗，但它为探索 AI 在支持认知健康方面的作用提供了有价值的工具，为将来在个性化数字疗法方面的创新铺平了道路。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open ReMe repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 ReMe 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.ReMeTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ReMe has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ReMe 已发布用于研究目的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chemical structures and colored markers on a scatter plot.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[散点图上的化学结构和彩色标记。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen is a transformer-based chemical language model for developing target-specific drug compounds. Research shows that TamGen can also optimize existing molecules by designing target-aware molecule fragments, potentially enabling the discovery of novel compounds that build on a known molecular core structure.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen 是一种基于变压器的化学语言模型，用于开发特定于目标的药品复合。Research shows that TamGen can also optimize existing dnas by designing target-aware fragments， potentially enabling the discovery of novel compounds that build on a known dna core structure.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover TamGen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 TamGen]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generative AI is opening new avenues for scientific exploration by allowing computers to autonomously learn and produce original content. TamGen offers a new approach to drug discovery by applying the principles of generative AI to molecular design. Unlike traditional methods, which depend on systematically screening known compounds—a process that is long, complex, and costly due to its reliance on empirical knowledge and the time-consuming task of exploring a vast chemical library—generative AI provides opportunities for designing entirely new chemical structures.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Generative AI 通过允许计算机自主学习和生成原始内容，为科学探索打开新的通道。TamGen 通过将生成 AI 原则应用于分子设计，提供了一种新的药物发现方法。与传统方法不同，传统方法依赖于大规模筛选已知的复合体-一个时间长、复杂且成本高昂的进程，因为它缺少经验知识，并且探索大量化学库的耗时任务—生成 AI 提供了设计全新化学结构的机会。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen showcases the transformative potential of generative AI in drug design, combining advanced molecular modeling with researcher-AI collaboration. Tasks that once took years could now be accomplished in a fraction of the time. This research underscores AI’s expanding role in drug discovery and its promise for developing effective treatments against persistent infectious diseases like tuberculosis.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen 展示了药物设计中生成 AI 的可变潜在，将高级分子建模与研究人员-AI 协作结合使用。一旦花费数年的任务现在可以在一定时间内完成。此研究强调 AI 在药物发现中的扩展角色，并承诺开发针对诸如结核等持久性疾病的有效攻击。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TamGen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TamGen]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TamGenTryOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get TamGen model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 TamGen 模型]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Try TamGen]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis creates  high-quality 3D assets from simple text or image inputs. Using a unified latent space (SLAT), it delivers detailed, textured 3D models in formats like meshes,radiance fields, and 3D Gaussians. Its flexibility, editing capabilities, and superior quality enable faster, more adaptable workflows in gaming, virtual worlds, industrial design, and beyond.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 可以根据简单的文本或图像输入创建高质量的 3D 资产。它使用统一的潜在空间(SLAT)，以网格、辐射场和 3D 高斯等格式提供带纹理的详细 3D 模型。其灵活性、编辑功能和卓越质量可在游戏、虚拟世界、工业设计等领域实现更快、适用性更高的工作流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore Trellis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 Trellis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis provides a powerful foundation for scalable, AI-driven content creation. Built to meet rapidly growing industrial demand, Trellis creates high-quality, editable 3D assets from simple text or image prompts in lieu of manual modeling. This saves time, lowers barriers, and unlocks new possibilities for developers, designers, and digital content creators.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 为创建可缩放的 AI 驱动内容创建提供了强大的基础。Trellis 旨在满足快速增长的行业需求，它替代手动建模，根据简单的文本或图像提示创建高质量的可编辑 3D 资产。这将节省时间，降低门槛，并为开发人员、设计师和数字内容创作者开启新的可能性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis is built on a novel Structured LATent (SLat) representation that captures both geometric structure and visual detail in a compact, editable form. Trained on 500,000 diverse 3D objects using rectified flow transformers with up to 2 billion parameters, Trellis significantly improves both quality and flexibility compared to existing models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 基于新式的结构化 LATent (SLat) 表示形式，能够以紧凑、可编辑的形式捕获几何结构和视觉细节。通过使用具有多达 20 亿个参数的修正流转换器基于 500,000 个多样化的 3D 对象进行了训练，Trellis 相较于现有模型，显著提高了质量和灵活性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike traditional methods that target a single output type or require labor-intensive setup, Trellis can generate a 3D asset in multiple formats, including meshes, 3D gaussians, and radiance fields. This makes it compatible with different rendering pipelines and applications. The generated models feature detailed structure and rich texture, enabling their direct use in games, AR/VR experiences, digital twins, simulation environments, and product visualization.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与针对单个输出类型或需要人工密集型设置的传统方法不同，Trellis 可以生成多种格式的 3D 资产，包括网格、三维高斯和辐射场。这使得它与不同的渲染管道和应用程序兼容。生成的模型具有详细的结构和丰富的纹理，因此能够直接在游戏、AR/VR 体验、数字孪生、模拟环境和产品可视化中使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis also allows for prompt-guided local edits—such as removing, replacing, or adding parts of a 3D model—without retraining or manual sculpting, which dramatically accelerates iteration and customization. Its design eliminates the need for costly 3D fitting and leverages pretrained vision models for high-fidelity results, even when working with sparse 3D data.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis 还允许进行提示引导的本地编辑(例如移除、替换或添加 3D 模型的部件)，而无需重新训练或手动雕刻，这大大加快了迭代和定制的速度。其设计消除了对成本高昂的 3D 拟合的需求，并利用预先训练的视觉模型来实现高保真效果，即使在处理稀疏 3D 数据时也是如此。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trellis]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Trellis]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TrellisTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Trellis repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 Trellis 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TryOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{modelName} has been released for research purposes. Users can learn, explore and experiment with {modelName}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[出于研究目的，已发布 {modelName}。用户可以学习、浏览和试验 {modelName}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A demonstration of using the TypeAgent shell navigating and interacting with a visually rich Paleobiology Database (paleodb) website, highlighting its ability to process and act on complex web interfaces.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[演示了如何使用 TypeAgent shell 导航视觉元素丰富的古生物学数据库(paleodb)网站并与之交互，强调了其处理复杂 Web 接口的能力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentBodyImageAltText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An demonstration of the TypeAgent Shell where a user converses with the agent about events and entities extracted months earlier. The gif shows entities being retrieved from long-term memory into the conversation, enabling the user to take actions on them.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent Shell 的演示，展示的是用户与代理讨论几个月前提取的事件和实体。该 GIF 显示了从长期记忆检索到对话中的实体，这样用户便能够对它们采取行动。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent is sample code that explores an architecture for building a single personal agent with natural language interfaces leveraging current advances in LLM technology.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 是一段示例代码，它探索了一种利用 LLM 技术的最新进展构建具有多种自然语言界面的个人代理的体系结构。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discover TypeAgent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现 TypeAgent]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The goal of the TypeAgent team is to explore how to get work done by safely and efficiently combining stochastic systems like language models with traditional software components. Three principles have emerged during this investigation. They are listed below along with examples of how the principles apply to actions, memory and plans.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent 团队的目标是探索如何通过安全高效地将语言模型等随机系统与传统软件组件相结合来完成工作。在此调查过程中，出现了三个原则。以下列出了这些原则及其在操作、记忆和计划中的体现的示例。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<ul><li><strong>Principle:</strong> distilling models into logical structures</li><ul><li>Actions: find translation patterns and replace some model calls by applying patterns</li><li>Memory: build ontologies from text</li><li>Plans: people, programs and models collaborate using “tree of thought”</li></ul><br><li><strong>Principle:</strong> control information density</li><ul><li>Actions: applications define discrete categories with dense descriptions of action sets</li><li>Memory: tight semantic structures fit into attention budget</li><li>Plans: each search tree node defines a focused sub-problem</li></ul><br><li><strong>Principle:</strong> use logical structures to enable collaboration</li><ul><li>Actions: humans decide how to disambiguate action requests</li><li>Memory: simple models extract logical structure from text</li><li>Plans: quality models, advantage models, language models, humans and programs collaborate to expand each best-first-search node</li></ul>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<ul><li>原则:<strong></strong> 将模型提炼为逻辑结构</li><ul><li>操作: 查找翻译模式，并通过应用模式替换某些模型调用</li><li>记忆: 基于文本构建本体</li><li>计划: 用户、程序和模型通过“思维树”进行协作</li></ul><br><li>原则:<strong></strong> 控制信息密度</li><ul><li>操作: 应用程序定义具有密集操作集描述的离散类别</li><li>记忆: 紧凑的语义结构符合注意力预算</li><li>计划: 每个搜索树节点定义一个聚焦的子问题</li></ul><br><li>原则:<strong></strong> 使用逻辑结构来支持协作</li><ul><li>操作: 人类决定如何消除操作请求的歧义</li><li>记忆: 简单模型从文本中提取逻辑结构</li><li>计划: 质量模型、优势模型、语言模型、人类和程序协作，以扩展每个最佳优先搜索节点</li></ul>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are trying to build a single personal agent that can apply to any application.  To apply agent interfaces to all applications, we need to map user requests to actions at much lower cost and latency than current systems. To make this possible, we have created a system that can distill language models into logical systems that can handle most user requests.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们正在尝试构建一个可供任何应用程序使用的个人代理。 为了将代理接口应用于所有应用程序，我们需要以比远低于当前系统的成本和延迟将用户请求映射到操作。为此，我们创建了一个系统，用于将语言模型提炼为能够处理大多数用户请求的逻辑系统。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Figure 1: The TypeAgent shell example navigating a visually rich {paleodbLink} website.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图 1: TypeAgent shell 示例，用于导航视觉元素丰富的 {paleodbLink} 网站。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We use LLMS with structured prompts to extract a logical representation of actions on a page (e.g. buy product). This logical schema is the same across multiple sites, even if the sites have different HTML and JS implementations. We demonstrate the power of this approach by building automation to interact with multiple crossword sites and multiple e-commerce sites using consistent logical schema.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们使用带有结构化提示的 LLMS 提取页面上操作(例如购买产品)的逻辑表示。此逻辑架构在多个站点中是相同的，即使这些站点具有不同的 HTML 和 JS 实现也是如此。我们通过实现自动化来使用一致的逻辑架构与多个填字游戏网站和多个电子商务网站进行交互，从而展示了这种方法的强大之处。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText6" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are trying to create human-like memory with super-human precision and recall for agent conversations. We are using a new indexing and query processing approach called <strong>Structured RAG</strong> as the basis for agent memory. Structured RAG does substantially better than Classic RAG at answering questions about past conversations such as "what were the books we talked about?" and "what step were we on in building the photo montage?"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们正在尝试打造类似人类所拥有的记忆力，但这种记忆力的准确度高于人类，且可用于回忆代理对话。我们将使用一种名为“结构化 RAG”的新索引和查询处理方法作为代理记忆力的基础<strong></strong>。结构化 RAG 在回答有关过去对话的问题时明显优于经典 RAG，例如“我们讨论的书籍是什么?”和“我们进行到了制作照片蒙太奇的哪个步骤?”]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText7" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Figure 2: Here using the experimental TypeAgent Shell a user can have conversations with the agent about events and entities that were extracted months ago. Entities are pulled from long-term memory into the conversation memory and user can then take actions on the entities.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图 2: 在这里，使用实验性的 TypeAgent Shell，用户可以与代理就几个月前提取的事件和实体进行对话。将实体从长期记忆提取到对话记忆中后，用户可以对实体采取行动。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentText8" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Actions and memories flow together. Actions like "add to my calendar pickleball game 2-3pm on Friday" yield memories that can become parameters of future actions like "put in an hour of recovery time after my pickleball game." We are working on an architecture, <strong>AMP</strong>, that enables this natural information flow by integrating actions, memories, and plans. We are applying AMP to the web by creating a browser that enables web sites to register actions through a JavaScript interface.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[操作与记忆相互交融。像“将周五下午 2-3 点的匹克球比赛添加到我的日历”这样的操作会产生记忆，这些记忆可以成为未来操作的参数，比如“在匹克球比赛后用一个小时的时间进行恢复”。我们正在努力构建一种名为 "AMP" 的体系结构，<strong></strong>通过整合操作、记忆和计划来实现这种自然的信息流。我们正在通过创建一个允许网站通过 JavaScript 接口注册操作的浏览器，将 AMP 应用到 Web。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeAgent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeAgent]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.TypeAgentTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open TypeAgent repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 TypeAgent 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DBodyImageAltText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A diagram showing the process of generating a 3D talking head animation from a single image using VASA-3D. The steps include creating VASA-1 videos from the image, building a VASA-3D model, and combining it with an audio clip and optional control signals to produce various animated outputs of the subject.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图中展示了使用 VASA-3D 根据一张图像生成会说话的 3D 头部动画的过程。步骤包括根据图像制作 VASA-1 视频、构建 VASA-3D 模型，并将其与音频剪辑片段和可选控制信号结合，从而生成相应主题的各种动画输出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D exemplifies how generative AI can enhance human-computer interaction by making expressive, customizable 3D avatars accessible from minimal input. Extending VASA-1's motion latent into 3D and optimizing with synthetic multiview data, it opens new frontiers in communication, immersion, and assistive technology—setting a new standard for realism and responsiveness in avatar generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 证实了生成式 AI 如何通过创建仅需极少输入即可访问的富有表现力且可自定义的 3D 虚拟形象来增强人机交互体验。通过将 VASA-1 的动作潜变量扩展到 3D，并利用合成的多视角数据进行优化，VASA-3D 开辟了通信、沉浸式体验以及辅助技术领域的新前沿，为实现虚拟形象生成方面的真实感和响应能力设定了新的标准。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore VASA-3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 VASA-3D]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D is a major step forward in 3D avatar generation, producing realistic, expressive, and multiview-consistent 3D talking heads from just a single image and speech audio. The system builds on VASA-1, which introduced high-fidelity 2D talking head synthesis through a richly expressive motion latent. VASA-3D extends this capability into 3D by conditioning a neural 3D head model on the motion latent, capturing nuanced facial expressions and natural head motion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 是 3D 虚拟形象生成领域的重大进步，仅通过单个图像和语音音频即可生成逼真、富有表现力且多视角一致的会说话的 3D 头部。该系统基于 VASA-1 构建，后者通过极富表现力的运动潜变量实现了高保真 2D 会说话头像合成。通过根据运动潜变量调节神经网络 3D 头像模型，VASA-3D 将此功能扩展到 3D，可捕获细微的面部表情和自然的头部运用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To personalize the 3D head from a single portrait, VASA-3D creates additional views of the face from different angles and uses them to fine-tune the 3D model, even if some of those views have visual flaws or limited variety. The result is a model capable of real-time generation at 75 frames per second with just 65 milliseconds latency, supporting free-view rendering, emotional control, and high-quality animation, even from stylized or artistic portraits.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了根据单人肖像对 3D 头部模型进行个性化处理，VASA-3D 可从不同角度生成额外的人脸视图，并利用这些视图对 3D 模型进行微调，即使其中一些视图存在视觉缺陷或多样性有限的问题。其结果是模型能够以每秒 75 的速度实时生成，且延迟仅为 65 毫秒，并支持自由视图渲染、情感控制和高质量动画，甚至支持根据风格化或艺术化肖像进行处理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D significantly outperforms prior methods on realism and expressiveness in both audio- and video-driven talking head tasks. Its broad potential spans virtual collaboration (AI coworkers), education (AI tutors), entertainment, and neuroscience research. Early applications include VR-based social interaction, memory activation studies, and adaptive learning tools. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在同时由音频和视频驱动的会说话的头部模型任务中，VASA-3D 在真实感和表现力方面显著优于先前的方法。VASA-3D 具有广泛的潜在应用领域，涵盖了虚拟协作(如 AI 同事)、教育(如 AI 导师)、娱乐以及神经科学研究等方面。早期应用包括基于 VR 的社交互动、记忆激活研究以及自适应学习工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Responsible AI is integral to VASA-3D’s development. To prevent misuse, the model and APIs are not publicly released. Face forgery detection systems trained on VASA-3D outputs can reliably distinguish synthetic content, enhancing safety and model robustness.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[负责任 AI 是 VASA-3D 开发工作中不可或缺的一部分。为了防止滥用，模型和 API 不会公开发布。基于 VASA-3D 输出训练的人脸伪造检测系统可以可靠地识别合成内容，从而增强安全性和模型可靠性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DText5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D exemplifies how advanced generative AI can enhance human-computer interaction. By making expressive, customizable 3D avatars accessible from minimal input, it opens new frontiers in communication, immersion, and assistive technology—setting a new standard for realism and responsiveness in avatar generation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D 展示了高级生成式 AI 如何增强人机交互。通过生成仅需极少输入即可访问的富有表现力且可自定义的 3D 虚拟形象，VASA-3D 开辟了通信、沉浸式体验以及辅助技术领域的新前沿，为虚拟形象生成方面的真实感和响应能力设定了新的标准。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA-3D]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA-3D]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTryItOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open Vasa-3D repo]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开 Vasa-3D 存储库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VASA3DTryItOutSubtext" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VASA3D has been released for research purposes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VASA3D 已发布用于研究目的。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a framework for generating realistic, long-form, multi-speaker dialogue from transcripts, making it ideal for podcasts, voiceovers, and narrative audio. Unlike typical TTS tools, VibePod handles up to four speakers across 30-minute sessions with strong speaker consistency, natural pacing, and turn-taking.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一个用于根据脚本生成逼真、长篇的多说话人对话的框架，非常适合用于播客、画外音和叙述性音频。与典型的 TTS 工具不同，VibePod 可在 30 分钟的会话中支持最多四个说话人，在说话人一致性、自然节奏和轮流发言方面表现出色。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodHomeLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore VibePod]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[探索 VibePod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod is a new framework designed to generate realistic, multi-speaker audio content from transcripts. Ideal for producing podcasts, voiceovers, and other narrative formats, it outperforms conventional text-to-speech (TTS) tools, which are unable to produce long-form, coherent, and interactive multi-speaker dialogue. VibePod supports up to four distinct voices in 30-minute segments. With its improved pacing, turn-taking, and speaker consistency, VibePod rated highly in user testing for spontaneity and realism.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod 是一个新框架，旨在根据脚本生成逼真的多说话人音频内容。它非常适合制作播客、画外音和其他叙述性格式，性能优于传统的文本转语音(TTS)工具，这些工具无法生成长篇、连贯且互动的多说话人对话。VibePod 在 30 分钟的片段中支持最多四种不同的嗓音。凭借其改进的节奏、轮流发言和说话人一致性，VibePod 在用户测试中因其自发性和真实感而获得高评价。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unlike systems focused on voice cloning, VibePod emphasizes dialogue quality over personalization. It was trained on publicly available and synthetically generated datasets, with safeguards built in to prevent misuse. It does not allow custom voice uploads, reflecting {ResponsibleAIPrinciples}.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与专注于语音克隆的系统不同，VibePod 强调对话质量而不是个性化。它基于公开可用和合成生成的数据集进行了训练，内置了防止滥用的安全措施。它不允许定制声音，这反映了 {ResponsibleAIPrinciples}。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText2Link" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft’s Responsible AI principles]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft 的负责任 AI 原则]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodText3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To promote research, transparency, and responsible use, VibePod is being released on Hugging Face under the MIT License. Open-sourcing the technology invites collaboration from the broader speech synthesis community. Future enhancements will include multilingual support and controls for emotional tone, expanding its creative potential.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为了促进研究、透明度和负责任的使用，VibePod 将在 MIT 许可证下发布于 Hugging Face。将技术开源需要邀请更广泛的语音合成社区进行协作。未来的增强功能将包括多语言支持和情感语气控制，从而扩展其创意潜力。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Projects.VibePodTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VibePod]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VibePod]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\1\s\extensions\prompt-flow-web\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-CN" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";ActionBarDesigner_ChatTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use the chat window to run your flow in a live chat way.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用聊天窗口以实时聊天方式运行流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ActionBarDesigner_CloseRightPanel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close right panel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[关闭右面板]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ActionBarDesigner_OpenRightPanel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open right panel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开右面板]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ActionBar_HideOverview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hide overview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隐藏概述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ActionBar_ShowOverview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show overview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[显示概述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Activate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Activate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[激活]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ActivateFocusMode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Activate focus mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[激活焦点模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Add" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AddInput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AddMyData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add my data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加我的数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AddOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Advanced" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高级]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Apply" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Apply]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[应用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ApplyAndRestart" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Apply and restart]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[应用并重启]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoMatic_Gen_Tool_Meta" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic generating tool meta]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动生成工具元数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.BasePrompt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Base prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.Connection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.Deployment.Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.Deployment.Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[By default, this is the same model you set to run this node, but you can choose other models here for the purpose of generating new prompts.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[默认情况下，这是设置为运行此节点的同一模型，但你可以在此处选择其他模型以生成新提示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We will auto generate prompt variants with the large language model selected, based on the knowledge of the model and the Base prompt selected. All generated prompts will be limited by Microsofts Content filtering policies.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们将根据所选模型和基本提示的知识，自动生成选中大型语言模型的提示变体。所有生成的提示都将受 Microsofts 内容筛选策略的限制。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[We will auto generate prompt variants with large language models based on the Base prompt you selected.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.ErrorMessage.FieldRequired" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This field is required.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[必填字段。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.ErrorMessage.InvalidNumber" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please enter a valid number.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请输入有效的数字。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.ErrorMessage.MessageBarBody" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[please check your prompt text, connection name or deployment name.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请检查提示文本、连接名称或部署名称。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.ErrorMessage.MessageBarTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get prompt suggestions failed for connection {connectionName}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取连接 {connectionName} 的提示建议失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.ErrorMessage.NumberGreaterThan5" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please enter a number up to 5.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请输入一个不超过 5 的数字。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Please enter a number less than 5.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.ErrorMessage.NumberLessThan1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please enter a number greater than 0.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请输入一个大于 0 的数字。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.Footer.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.Footer.Submit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Submit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提交]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.Header" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate prompt variants in bulk]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批量生成提示变体]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.Loading.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New variants are being generated...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在生成新变体...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.Loading.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generating]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.NumberOfVariants" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max number of new variants to generate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要生成的新变体数上限]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Number of new variants to generate]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";AutoPrompt.GeneratePromptPanel.PromptText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示文本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BackToFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Back to flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[返回流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BaseRun" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Base run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunDatasetHint.AdditionForImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If your flow inputs incorporate image data, refer to additional instructions.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果流输入包含图像数据，请参阅其他说明。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunDatasetHint.DetailForCSV" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a .csv file, each row should represent a separate record, with each field separated by a comma.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于 .csv 文件，每一行应代表一条单独的记录，每个字段用逗号分隔。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunDatasetHint.DetailForJsonLine" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For a .jsonl file, each line should represent a separate record formatted as a JSON object.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对于 .jsonl 文件，每一行应代表一条采用 JSON 对象格式的单独记录。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunDatasetHint.DetailForTSV" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Similarly for a .tsv file, each row should represent a separate record, with each field separated by a tab.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[同样，对于 .tsv 文件，每一行应代表一条单独的记录，每个字段用制表符分隔。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunDatasetHint.LearnMoreAboutImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about image data in batch run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解批处理运行中的图像数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunDatasetHint.Summary" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prepare your data in one of the following formats: .jsonl, .csv, or .tsv. Ensure that your data includes headers that align with the flow inputs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[准备以下一种格式的数据: .jsonl、.csv 或 .tsv。确保数据包含与流输入一致的标头。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunSubmitSuccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your run(s) have been submitted successfully.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功提交运行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Add_New_Data" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add new data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加新数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.BasicSetting_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basic settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.BasicSetting_Variants_Default" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(default)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(默认)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.BasicSetting_Variants_Desc" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a node with variants that you want to run. Note: other nodes will run with default variant.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择包含想要运行的变量的节点。注意: 其他节点将使用默认变量运行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.BasicSetting_Variants_Select_All" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(Select all)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(全选)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.BasicSetting_Variants_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Variants]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[变量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.BatchRunReview_Eval_Data" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data asset for evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要评估的数据资产]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.BatchRunSetting_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch run settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批处理运行设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.BatchRunVariant_NumberTip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{runNumber} run(s) will be generated based on selected variant(s)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将会基于所选变量生成 {runNumber} 个运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.BatchRun_Panel_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch run & Evaluate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批量运行和评估]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Submit batch run]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Batch_Run_Data_Type_Tips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a .jsonl .csv, or .tsv file, or a folder containing these file types.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择 .jsonl .csv、.tsv 文件或包含这些文件类型的文件夹。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Batch_Run_Display_Name_Batch_Run_Name_Macro_Support" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{batchRunNameMacro} will be replaced by the run to evaluate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{batchRunNameMacro} 将替换为要评估的运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Batch_Run_Display_Name_Macro_Support" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Display name supports macro]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[显示名称支持宏]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Batch_Run_Display_Name_Timestamp_Macro_Support" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{timestampMacro} will be replaced by submission timestamp]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{timestampMacro} 将替换为提交时间戳]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Batch_Run_Display_Name_Variant_Id_Macro_Support" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{variantIdMacro} will be replaced by tunning node variant id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{variantIdMacro} 将替换为调整节点变体 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Batch_Run_Name_Pattern_Tip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[It will be generated automatically]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[它将自动生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Button_Review_And_Submit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review + submit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[审阅 + 提交]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.ChooseFile_Tips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please make sure the data includes headers]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请确保数据包含标头]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Choose_A_File" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose a file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Choose_A_Folder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose a folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择文件夹]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.DataName_Tips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data with same name will be saved as a new version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[同名数据将另存为新版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Dataset_Entry_File" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Entry file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[条目文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Dataset_Folder_Type_Missing_Entry" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Selected file must be .jsonl, .csv, .tsv, or a folder containing these types.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所选文件必须是 .jsonl、.csv、.tsv 或包含这些类型的文件夹。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.EvaluationSetting_Diff_Dataset_Warning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Caution: Changing to a different dataset may cause evaluation errors. Please ensure your selected dataset matches the output in sample count and order.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注意: 更改为其他数据集可能会导致评估错误。请确保所选数据集与样本计数和顺序中的输出匹配。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.EvaluationSetting_Info_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation info]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Evaluation_Data_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose data asset for evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择要评估的数据资产]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.File_Select" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Multi_Eval_Configure_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[配置计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Multi_Eval_OtherSettings_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Other settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[其他设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Multi_Eval_Selection_Desc" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can choose to test your prompt flow and evaluate the output performance using automated or customized evaluation method. You can submit batch run without evaluation if you want to evaluate the outputs later.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以选择使用自动化或自定义评估方法测试提示流并评估输出性能。如果以后要评估输出，可以提交批处理运行而不进行评估。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[You can choose to test your prompt flow and evaluate the output performance using built-in or customized evaluation method. You can submit batch run without evaluation if you want to evaluate the outputs later.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Multi_Eval_Selection_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Review_Error_tips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation failed. Required information is missing or not valid.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[验证失败。所需信息缺失或无效。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Select_Data_From_Flow_folder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select data from flow folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从流文件夹中选择数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Submission_Failed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Submission failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提交失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Submission_Successful" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Submission successful]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提交成功]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Submit_New_Evaluation_Partial_Success" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate run submission is partially successful, please check error messages below:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估运行提交部分成功，请查看以下错误消息:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Submit_New_Evaluation_Success" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate run submitted successfully.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功提交评估运行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Upload_From_Local_File" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload from local file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从本地文件上传]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BatchRunWizardV2.Upload_From_Local_Folder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload from local folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从本地上传]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Browse" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunContent_AddTag" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add tag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加标记]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunContent_RunDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunContent_RunName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run display name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行显示名称]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Run name]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunContent_Runtime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunPanel_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Submit bulk test]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提交批量测试]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Submit bulk run]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunWizard_Bulk_Run_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run your prompt flow with multiple lines of input data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用多个输入数据行运行提示流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunWizard_Bulk_Run_Info" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run Info]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunWizard_Bulk_Run_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bulk test settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批量测试设置]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Bulk run settings]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunWizard_Evaluation_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Test your prompt flow and evaluate the output quality]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测试提示流并评估输出质量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunWizard_Evaluation_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunWizard_Review_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review your setting before running the bulk test.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在运行批量测试之前，请查看设置。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunWizard_Review_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评审]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunWizard_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Bulk test & Evaluate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批量测试和评估]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Bulk run & Evaluate]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkRunWizard_Variants_Node_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select Variants]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择变量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetailsOutput.CSV" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[CSV]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[CSV]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetailsOutput.JSONL" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[JSONL]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[JSONL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetailsOutput.exportCSV" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[export CSV]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[导出 CSV]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetailsOutput.exportJSONL" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[export JSONL]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[导出 JSONL]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetails_ExpandAllCells" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expand all cells]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[展开所有单元格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetails_Filter" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Filter]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[筛选器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetails_FoldAllCells" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fold all cells]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[折叠所有单元格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetails_InputData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetails_NoTuningNodeTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Variant_0 is the default run generated for the entire flow when none of the LLM nodes have additional variants.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Variant_0 是在没有任何 LLM 节点具有其他变体时为整个流生成的默认运行。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Variant_0 is the default run generated for the entire flow when none of the LLM nodes have additional variants.  If you wish to create more variants for a specific LLM node, click the 'Tune' button within that node in your flow.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";BulkTestDetails_TuningNodeTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Following variants are based on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下变体基于]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CancelFlow_Failed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow test cannot be cancelled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法取消流测试]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CancelFlow_Success" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow test is cancelled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流测试已取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_MinimapOutViewport_Line1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visible area is out of scope,]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可见区域超出范围，]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_MinimapOutViewport_Line2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[please click minimap to navigate back]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请单击缩略图来返回]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_MinimapUnavailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are {nodesCount} nodes in the canvas, navigator only support the preview of no more than {maxNodesCount} nodes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[画布中有 {nodesCount} 个节点，导航器只支持预览不超过 {maxNodesCount} 个节点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Navigator" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Navigator]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[导航器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_autoLayout" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Auto layout]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动布局]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_autoZoom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Auto zoom]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动缩放]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_chat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_drag" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hand tool]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[手形工具]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_featureSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Feature settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[功能设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_resetZoom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reset zoom]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重置缩放]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_select" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Selection tool]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择工具]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_showFreezeLayout" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Freeze layout]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[冻结布局]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_showSimpleMode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Simple mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[简单模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_showVerticalDirection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Vertical direction]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[垂直方向]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_zoomIn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zoom in]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[放大]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_zoomOut" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zoom out]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[缩小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CanvasEditor_Tooltips_zoomSelector" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zoom selector]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[缩放选择器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Change_Chat_Input_Dialog_Content" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Are you sure to change the chat input type? it will cause the chat history to be cleared.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[确定要更改聊天输入类型吗? 这将导致清除历史聊天记录。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Change_Chat_Input_Dialog_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm changing the chat input type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[确认更改聊天输入类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.Add" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.AddAnImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add an image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.AttachFileButtonDisableTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To upload images, set the chat input type to 'list' first.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要上传图像，请先将聊天输入类型设置为“list”。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.ClearButtonTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to clear all chat histories]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击以清除所有聊天历史记录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.CloseButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to close chat box]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击以关闭聊天框]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.DropImageHere" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drop image here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将映像拖放到此处]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.EnterFullScreenButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to enter full screen mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击以进入全屏模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.EvalButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate the last]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估最后一个]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.EvaluationErrorTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[错误]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.EvaluationFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.EvaluationSettingName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation setting - {name}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估设置 - {name}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.ExitFullScreenButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to exit full screen mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击以退出全屏模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.HeaderTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.InputPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input anything to test...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入要测试的任何内容...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.LoadingMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generating chat output for you]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为你生成聊天输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.MessageStatus_TimeSpent_Desc" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[time spent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停留时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.MessageStatus_TimeSpent_Desc_Capitalized" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Time spent]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所用时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.MessageStatus_TimeSpent_Unit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[sec]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[秒]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.MessageStatus_Tokens_Desc" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Total tokens for generating this]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用于生成此项的令牌总数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.MessageStatus_Tokens_Unit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[令牌]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.NoEvaluationSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are no evaluation settings yet.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[尚无评估设置。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.NoMetricsAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No metrics available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无可用指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.NoOutputAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No output available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无可用输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.PasteImageOrLinkHere" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Paste image or link here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此处粘贴图像或链接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.PickAEvaluationFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Pick a evaluation flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择评估流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.ResendButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resend]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重新发送]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.SendAndEvalButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Send and evaluate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发送和评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.SendAnyway" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Send anyway]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仍要发送]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.SendButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Send]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发送]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.Send_Tooltip.Runtime_Need_Update" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Current runtime may not parse chat history, please try to upgrade runtime to the latest version if there is resource not found error.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[当前运行时可能无法分析聊天历史记录，如果存在找不到资源的错误，请尝试将运行时升级到最新版本。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.StartNewSessionButtonTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to start a new session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击以启动新会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.Tooltip_Bottom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only default variants will be used for chat, if you want to test variants please try evaluation. For chatbot and test app bot, it will only show the chat output.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只有默认变体将用于聊天，如果要测试变体，请尝试产品评测。对于聊天机器人和测试应用机器人，将仅显示聊天输出。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Only default variants will be used for chat, if you want to test variants please try bulk test. For chatbot and test app bot, it will only show the chat output.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.Tooltip_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[chat]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.Tooltip_TotalTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Total tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[令牌总数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.UploadFromThisDevice" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload from this device]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从此设备上传]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatBox.WarningMessageBarDismissButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dismiss warning message]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[消除警告消息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatFlow.ChatHistoryPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat history will be automatically generated]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将自动生成聊天历史记录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatFlow.ChatInputPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please input content in chat box]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请在聊天框中输入内容]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatHistory" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat history]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天历史记录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChatHistoryDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat history automatically reflects the conversation history in the chat box. Chat history value is not editable.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天记录自动反映聊天框中的对话历史记录。聊天记录值不可编辑。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Several flow runs or node runs failed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多个流运行或节点运行失败。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Action_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Send]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发送]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Actions.Left_Panel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Left panel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[左面板]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Actions.Right_Panel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Right panel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[右面板]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Badge.ChatHistory" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[chat history]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天历史记录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Badge.ChatInput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[chat input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Badge.ChatOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[chat output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Chat_Flow_IO" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow inputs and outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流输入和输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Chat_IO_Setting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat setting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Chat_input_placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please select a chat input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请选择聊天输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Chat_output_placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please select a chat output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请选择聊天输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Evaluation_Setting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation setting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Flow_Evaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Flow_Inputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow inputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Flow_Outputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Flow_Test" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow test]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流测试]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Header_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Test]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测试]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Chat]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.More_Actions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More actions]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更多操作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.No_ChatIO_Warning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The input field is currently inactive. To enable it, please select chat input and chat output in chat setting or you can test in right panel directly]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入字段当前处于非活动状态。要启用它，请在聊天设置中选择聊天输入和聊天输出，也可以直接在右侧面板中进行测试]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.No_Output_Found" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No output found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未找到输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Selector.Placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select variants to filter messages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择变体以筛选消息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Chat_Window.Selector.Variant" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Variant]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[变体]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ChooseASupportedFileType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a supported file type: .csv, .tsv, .jsonl]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择受支持的文件类型：.csv、.tsv、.jsonl]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Select a supported file type: .csv, .tsv, .jsonl, .parquet]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Clone" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clone]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[克隆]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CloneFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clone flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[克隆流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Close" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Cloud" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cloud]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[云]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CompareMetrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compare metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[比较指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Confirm" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[确认]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Connection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Connections" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Copied" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copied]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已复制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CopyName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[复制名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CopyToClipboard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy to clipboard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[复制到剪贴板]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Create" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.AoaiResourceLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI Account Names]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI 帐户名称]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service Account Names]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.AoaiResourcePlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search or select an Azure OpenAI account]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜索或选择 Azure OpenAI 帐户]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Search or select an Azure OpenAI Service account]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.AoaiResourceTooltip1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name of your {aoaiService}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{aoaiService} 的名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.AoaiResourceTooltip2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[. You can find it in {azPortal}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[。可以在 {azPortal} 中找到它]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.AoaiResourceTooltip3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ or Azure CLI: az cognitiveservices account list --query "[?kind=='OpenAI']5D;.{name:name, location:location, kind:kind}". Learn more about {promptFlowConnection}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ 或 Azure CLI: az cognitiveservices 帐户列表 --query "[?kind=='OpenAI']5D;。{name:name, location:location, kind:kind}"。详细了解 {promptFlowConnection}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.AoaiService" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure OpenAI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure OpenAI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Azure OpenAI Service]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.AzurePortal" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure portal]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 门户]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.FolderNameTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The flow code files are located in this folder. You can rename the folder, without affecting the flow display name.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流代码文件位于此文件夹中。可以重命名文件夹，而不会影响流显示名称。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.GalleryCreateFileLocateDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The flow code files are stored in a specific folder within your workspace file share storage. This folder name can be customized according to your preferences.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流代码文件存储在工作区文件共享存储中的特定文件夹中。可以根据首选项自定义此文件夹名称。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.GalleryImport_ImportDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Import an existing flow from the workspace-bound file share storage, by selecting the flow folder that contains the unique flow.dag.yaml file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过选择包含唯一 flow.dag.yaml 文件的流文件夹，从工作区绑定的文件共享存储导入现有流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.GalleryImport_ImportTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Import from file share]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从文件共享导入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.GalleryImport_NoDagYamlError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No flow.dag.yaml file detected in current path, please select another path.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[当前路径中未检测到 flow.dag.yaml 文件，请选择其他路径。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.GalleryImport_UpLoadTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload from local]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从本地上传]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.GalleryImport_UploadDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Import an existing flow from local, by uploading the flow folder that contains the unique flow.dag.yml file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过上传包含唯一 flow.dag.yml 文件的流文件夹，从本地导入现有流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.GalleryUpload_NoDagYamlInZipError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No flow.dag.yaml found in the root of zip file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 zip 文件的根目录中找不到 flow.dag.yaml。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.ImportFromFileShare_FilShareSelectorLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Path to flow folder to import]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要导入的流文件夹的路径]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.ImportFromFileShare_FilShareSelectorTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Browse to the storage path where the flow folder is located, your path must point to a single flow folder.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览到流文件夹所在的存储路径，路径必须指向单个流文件夹。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.LocationToStoreFlowTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The flow folder, which holds all the code files for your flow, is located in the promptflow directory under your user path.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包含流的所有代码文件的流文件夹位于用户路径下的 promptflow 目录中。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.PromptFlowConnection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[connection in prompt flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示流中的连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.SubLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[订阅 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.SubPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search or select a subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜索或选择订阅]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClone.UploadFromLocal_SelectFolderTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Choose a single flow folder that has a dag.yaml file in it from your local drive.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从本地驱动器中选择包含 dag.yaml 文件的单个流文件夹。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateAndClonePanel_FolderExistsError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Folder name already exists]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件夹名称已存在]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateFlow_TeachingBubble_Content" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Activate your app's AI by cloning a sample or building a flow from scratch.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过克隆示例或从头开始构建流来激活应用的 AI。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";CreateFlow_TeachingBubble_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a new flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建新流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Data" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DefaultValueTemplate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[default value: {defaultValue}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[默认值: {defaultValue}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Delete" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Deploy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployNoRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need to select a runtime which includes the environment and compute to execute a flow and then deploy.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要选择包含环境和计算的运行时，才能执行流，然后进行部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.AdvancedSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高级设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.BasicSettings.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deploy your flow to a managed online endpoint for real-time inference.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将流部署到托管联机终结点以进行实时推理。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.BasicSettings.Loading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[loading]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在加载]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.BasicSettings.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Basic settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Compute.Review.InstanceCount" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instance count]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实例计数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Compute.Review.VirtualMachine" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Virtual machine]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[虚拟机]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Compute.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Connection.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specify connections for the endpoints when perform inferencing.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[执行推理时指定终结点的连接。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Connection.NoConnection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No connection available for current flow.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有可用于当前流的连接。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[No connection override available for current flow.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Connection.Review.Connection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Connection.Review.DeploymentNameModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment name / Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署名称/模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Connection.Review.NodeName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Node name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[节点名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Connection.Review.Provider" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供程序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Connection.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Create" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.CreatingNotificationContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[After the deployment task is completed, you will receive a message in the notification center.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成部署任务后，你将在通知中心收到一条消息。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.CreatingNotificationTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint {endpointName} is being created]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在创建终结点{endpointName}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.AdvancedSettingsDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced settings - Deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高级设置 - 部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.AllowSharingDataForTestingPurposeOnlyDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The first row of your input data will be used as sample input data for testing the endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入数据的第一行将用作测试终结点的示例输入数据。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.AllowSharingSampleInput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow sharing sample input data for testing purpose only]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仅允许出于测试目的共享示例输入数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.AppInsightsTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Collect system metrics like token counts monitor the cost and performance of your deployment. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[收集令牌计数等系统指标，监视部署的成本和性能。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.ApplicationInsightsDiagnostics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Application Insights diagnostics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Application Insights 诊断]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.Data" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.DataCollection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inferencing data collection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[推理数据收集]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Inferencing data collection (preview)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.DataCollectionTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Collect production data from your deployment to monitor the performance of your model. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从部署中收集生产数据，以监视模型的性能。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.Deployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.DeploymentName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.DeploymentNameExists" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment with this name already exists. Keeping using this name will overwrite the existing deployment with new configurations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[具有此名称的部署已存在。继续使用此名称将用新配置覆盖现有部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.DeploymentTags" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署标记]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.DeploymentTagsDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can tag resources to add values that help you identify their use.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以标记资源以添加有助于识别其用途的值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A deployment is a set of resources and computes required for hosting the model or component that does the actual inferencing.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署是托管执行实际推理的模型或组件所需的一组资源和计算。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.DescriptionV2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Configure more attributes for your deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为部署配置更多属性。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Configure inferencing data collection and other attributes for your deployment.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.Disabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已禁用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.EgressPublicNetworkAccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Egress public network access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流出公用网络访问]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.EgressPublicNetworkAccessTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disable this option if your deployment should be blocked from internet outbound and should only access workspace and its storage account and container registry behind the virtual network.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果应阻止部署进行 Internet 出站，并且部署应仅访问虚拟网络后面的工作区及其存储帐户和容器注册表，请禁用此选项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.Enabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已启用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.EnvironmentVariables" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environment variables]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[环境变量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.EnvironmentVariablesDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Dictionary of environment variable key-value pairs to set in the deployment container.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[要在部署容器中设置的环境变量键值对的字典。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.Metadata" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metadata]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[元数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.NoLinkedApplicationInsightsDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project does not have linked Application Insights. You need to {doSomethingLink} first and deploy with this property enabled. {learnMoreLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你的项目没有链接 Application Insights。首先需要 {doSomethingLink} 并启用此属性进行部署。{learnMoreLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.Security" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Security]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安全性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Deployment.updateYourAIResourceAndLinkAnAppInsights" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[update your AI resource and link an AppInsights]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新 AI 资源并链接 AppInsights]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.AccessToConnectionSecrets" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Access to connection secrets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[访问连接机密]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.AdvancedSettingsEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced settings - Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高级设置 - 终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.AllowSharingDataForTestingPurposeOnly" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Allow sharing sample input data for testing purpose only]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仅允许出于测试目的共享示例输入数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.AllowSharingDataForTestingPurposeOnlyDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The first row of your input data will be used as sample input data for testing the endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入数据的第一行将用作测试终结点的示例输入数据。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[If the checkbox is selected, after you deploy the endpoint successfully, you can test your endpoint using your data.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.AssignedToId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assigned to ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已分配给 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.AuthenticationType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[身份验证类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.AuthenticationTypeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The authentication method for the endpoint. Key-based authentication provides a primary and secondary key that does not expire. Azure ML token-based authentication provides a token that periodically refreshes automatically.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点的身份验证方法。基于密钥的身份验证提供了不会过期的主密钥和辅助密钥。Azure ML 基于令牌的身份验证提供了一个会定期自动刷新的令牌。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.AuthenticationTypeDescriptionV2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The authentication method for the endpoint. Key-based authentication provides a primary and a secondary key. Microsoft Entra ID (Azure AD) and Azure ML token-based authentications provide a token that periodically refreshes automatically.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点的身份验证方法。基于密钥的身份验证提供主密钥和辅助密钥。Microsoft Entra ID (Azure AD)和基于 Azure ML 令牌的身份验证提供定期自动刷新的令牌。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.AzureMLTokenBased" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure ML token-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于 Azure ML 令牌]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An endpoint is used to deploy and score your models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点用于部署模型和为模型评分。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.DisableAccessToConnectionSecrets" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disable access to connection secrets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[禁用对连接机密的访问]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EnableAccessToConnectionSecrets" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable access to connection secrets]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启用对连接机密的访问]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EnableAccessToConnectionSecretsDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable this property will auto assign a connection reader role to the endpoint system-assigned identity if you have the connection reader permission, so that the endpoint can access connections used in the flow when perform inferencing. If you disable it, you'll need to grant Azure Machine Learning workspace connection reader to the endpoint identity manually.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果具有连接读取者权限，则启用此属性会自动向终结点系统分配的标识分配连接读取者角色，以便终结点可在执行推理时访问流中使用的连接。如果禁用，则需要手动地向终结点标识授予 Azure 机器学习工作区连接读取者。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EnablePublicNetworkAccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable public network access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启用公共网络访问]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EnableStreamingResponse" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable streaming response]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启用流式处理响应]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EnableStreamingResponseDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The deployment offers the capability of streaming responses, enabling access to information as it becomes available, rather than waiting for a complete response.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署提供流式处理响应的功能，在信息可用时启用对信息的访问，而不是等待完整的响应。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EndpointDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EndpointNameDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint name must begin with lowercase letter, followed by lowercase letters, hyphen or numbers and end with a lowercase letter or number.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[端点名称必须以小写字母开头，后跟小写字母、连字符或数字，并以小写字母或数字结尾。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EndpointTags" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点标记]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EndpointTagsDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can tag resources to add values that help you identify their use.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以标记资源以添加有助于识别其用途的值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.EnforceAccessToConnectionSecretsPreview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enforce access to connection secrets (preview)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[强制实施对连接机密的访问(预览)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标识类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To ensure that the endpoint can access Azure resources when performing inference, use a system-assigned or user-assigned managed identity for your endpoint and grant correct permissions to the identity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要确保终结点在执行推理时可以访问 Azure 资源，请对终结点使用系统分配的托管标识或用户分配的托管标识，并授予对标识的正确权限。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[You can attach system assigned or user assigned managed identities to grant access to resources.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.StorageBlobDataReaderToTheWorkspaceDefaultStorage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage Blob Data Reader to the workspace default storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将 Blob 数据读取器存储到工作区默认存储]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.SystemAssignedAbstract" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need to give following permissions to the system assigned identity after the endpoint is created:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建终结点后，你需要向系统分配的标识授予以下权限:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.SystemAssignedDetailItem1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AzureML Data Scientist role or a customized role with "Microsoft.MachineLearningServices/workspaces/connections/listsecrets/action" permission to workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对工作区具有“Microsoft.MachineLearningServices/workspaces/connections/listsecrets/action”权限的 AzureML 数据科学家角色或自定义角色]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[AzureML datascientis role or a customized role with "Microsoft.MachineLearningServices/workspaces/connections/listsecrets/action" permission to workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.SystemAssignedV2Abstract" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need to grant following role to the system assigned identity:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要向系统分配的标识授予以下角色:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.SystemAssignedV2DetailItem1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning Workspace Connection Secrets Reader on the scope of Azure Machine Learning workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 机器学习工作区范围上的 Azure 机器学习工作区连接机密读取者。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedAbstract" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need to give following permissions to the user assigned identity:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你需要向用户分配的标识授予以下权限:]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[You need to give following permissions to the user assigned identity You need to give following permissions to the user assigned identityYou need to give following permissions to the user assigned identityYou need to give following permissions to the user assigned identity:]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedDetailItem1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AzureML Data Scientist role or a customized role with "Microsoft.MachineLearningServices/workspaces/connections/listsecrets/action" permission to workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对工作区具有“Microsoft.MachineLearningServices/workspaces/connections/listsecrets/action”权限的 AzureML 数据科学家角色或自定义角色]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[AzureML datascientis role or a customized role with "Microsoft.MachineLearningServices/workspaces/connections/listsecrets/action" permission to workspace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedDetailItem2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage Blob Data Contributor permission, and Storage Table Data Contributor to the default storage of the workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存储 Blob 数据参与者权限以及工作区默认存储的存储表数据参与者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedDetailItem3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AcrPull to the workspace container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AcrPull 到工作区容器注册表]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedDetailItem4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AzureML Metrics Writer to the workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作区的 AzureML 指标编写器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedV2Abstract" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need to grant following roles to the user assigned identity:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要向用户分配的标识授予以下角色:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedV2DetailItem1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AcrPull on the scope of the Azure Machine Learning workspace container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 机器学习工作区容器注册表范围上的 AcrPull]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedV2DetailItem2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage Blob Data Reader on the scope of the Azure Machine Learning workspace storage]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 机器学习工作区存储范围上的存储 Blob 数据读取者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedV2DetailItem3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Azure Machine Learning Workspace Connection Secrets Reader on the scope of Azure Machine Learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Azure 机器学习工作区范围上的 Azure 机器学习工作区连接机密读取者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.IdentityTypeWarningBanner.UserAssignedV2DetailItem4" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(optional) AzureML Metrics Writer on the scope of the Azure Machine Learning workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(可选)Azure 机器学习工作区范围上的 AzureML 指标写入者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.KeyBased" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key-based]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于密钥]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.KeyBasedAuthentication" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key-based authentication]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于密钥的身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.Metadata" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metadata]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[元数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.MicrosoftEntraTokenBased" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Entra token-based(Azure AD)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于 Microsoft Entra 令牌(Azure AD)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.NavigationStepName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.PublicNetworkAccess" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Public network access]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[公用网络访问]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.PublicNetworkAccessDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable this option if you want to allow scoring requests to your endpoint from the internet. Disable this option if you want to allow scoring requests to your endpoint only from your resources in your virtual network.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果要允许从 Internet 对终结点的评分请求，请启用此选项。如果希望仅允许从虚拟网络中的资源对终结点进行评分请求，请禁用此选项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.SystemAssigned" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[System-assigned]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[系统分配]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.TokenBasedAuthentication" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token-based authentication]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基于令牌的身份验证]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Endpoint.UserAssigned" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User-assigned]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用户分配]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.AdvancedSettingsEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced settings - Environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高级设置 - 环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.Environment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.EnvironmentName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environment Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[环境名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.EnvironmentTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The environment to host the model. By default will use the environment based on current image and dependencies in requirements.txt. You can also select custom environment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用于托管模型的环境。默认情况下，将基于 requirements.txt 中的当前映像和依赖项使用环境。还可以选择自定义环境。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.EnvironmentVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environment Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[环境版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.NavigationStepName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.SpecifyEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specify the environment for your deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为部署指定环境。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.UseCurrentFlowEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use environment of current flow definition]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用当前流定义的环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.UseCustomizedEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use customized environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用自定义环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Environment.UseCustomizedEnvironmentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can select other custom environment with different dependencies for your deployment. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你可以为部署选择具有不同依赖项的其他自定义环境。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Outputs.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select which outputs of the flow will be included in the response of the endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择将包含在终结点响应中的流输出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Outputs.IncludedInEndpointResponse" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Included in endpoint response]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包含在终结点响应中]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Outputs.OutputName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Outputs.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Outputs.Type" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.OutputsAndConnections.AdvancedSettingsEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced settings - Outputs & connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高级设置 - 输出和连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.OutputsAndConnections.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Specify what flow outputs to be included in your endpoint response, and what connections to be used in your deployment.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指定要包含在终结点响应中的流输出，以及要在部署中使用的连接。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.OutputsAndConnections.NavigationStepName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outputs & connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出和连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Review.AADConnectionWarning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Following connection(s) are using Microsoft Entra ID based authentication. You need to manually grant the endpoint identity access to the related resource of these connection(s).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[以下连接正在使用基于 Microsoft Entra ID 的身份验证。需要手动授予终结点标识对这些连接的相关资源的访问权限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Review.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review the deployment settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[审阅部署设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.Review.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评审]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeployPanel.ReviewPlusCreate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review + Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[审阅 + 创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.ComputeStep.AutoScale" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable auto scale]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启用自动缩放]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.ComputeStep.InstanceCount" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instance count]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实例计数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.ComputeStep.SelectInstanceType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select instance type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择实例类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.ComputeStep.StepName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.ComputeStep.VirtualMachine" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Virtual machine]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[虚拟机]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.CreatePanelTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add managed online deployment runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加托管联机部署运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.DeploymentSettingStep.SelectDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a deployment to add to your endpoint.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建要添加到终结点的部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.DeploymentSettingStep.StepName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.DeploymentSettingStep.ValidateLoading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating deployment...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在验证部署...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.DeploymentSettingStep.unableConnectDeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are unable to connect to this deployment as runtime. Please make sure this deployment is ready to use. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法作为运行时连接到此部署。请确保此部署已准备好使用。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EndpointStep.SelectEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An endpoint is used to deploy and score your models.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点用于部署模型和为模型评分。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EndpointStep.StepName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.CustomEnvironmentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can customize the packages in runtime environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你可以在运行时环境中自定义包]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[You can customize the packages in runtime environment, ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.CustomEnvironmentDescriptionWithComma" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can customize the packages in runtime environment, ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你可以在运行时环境中自定义包， ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.CustomImageDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please add more dependency in following conda file, please do not existing dependency, otherwise, runtime may break.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请在以下 conda 文件中添加更多依赖项，请不要更改现有依赖项，否则运行时可能会中断。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.CustomizedEnvironmentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add new python packages to default image or bring your own customized image. Please follow the instruction to create environment using your base image. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将新的 python 包添加到默认映像或自定义映像。请按照说明使用基本映像创建环境。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Add new python packages to default image or bring your own customized image. Please follow the instruction to create environment using your base image. {View documentation}]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.CustomizedWayTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select customized way]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择自定义方式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.CustomizedWay_CustomImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Custom image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义映像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.CustomizedWay_ExistingEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select from existing environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从现有环境中选择]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.DefaultEnvironmentDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use the pre-defined runtime environment provide by promptflow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 propmptflow 提供的预定义运行时环境]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use the pre-defined runtime environment provide by promptflow, ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.DefaultEnvironmentDescriptionWithComma" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use the pre-defined runtime environment provide by promptflow, ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 propmptflow 提供的预定义运行时环境， ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.DefaultValue" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[default]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[默认]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.ReviewTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.StepName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.UseCustomizedEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use customized environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用自定义环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.EnvironmentSettingStep.UseDefaultEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use default environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用默认环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.ReviewStep.StepName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评审]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.AutoAssign" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Auto assign Azure Machine Learning data scientist]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动分配 Azure 机器学习数据科学家]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.DeploymentTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.EndpointIdentityType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint identity type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点标识类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.IdentityTypeWarningBanner.UserAssignedAbstract" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ After creating the endpoint and before you consume the endpoint as runtime, you need to give following permissions to the user assigned identity. Otherwise, the endpoint runtime will lack the correct access to run prompt flow.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ 创建终结点后，在将终结点用作运行时之前，需要向用户分配的标识授予以下权限。否则，终结点运行时将缺少运行提示流的正确访问权限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.IdentityTypeWarningBanner.UserAssignedDetailItem1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AzureML data scientist role to workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作区的 AzureML 数据科学家角色]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.IdentityTypeWarningBanner.UserAssignedDetailItem2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Storage Blob Data Contributor permission, and Storage Table Data Contributor to the default storage of the workspace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存储 Blob 数据参与者权限以及工作区默认存储的存储表数据参与者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.IdentityTypeWarningBanner.UserAssignedDetailItem3" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AcrPull to the workspace container registry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[AcrPull 到工作区容器注册表]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.RuntimeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.RuntimeName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.RuntimeSettingStep.StepName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DeploymentRuntime.UpdatePanelTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Update {runtimeName}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新{runtimeName}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Details" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Disabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已禁用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Discard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[放弃]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Download" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownstreamRunsMultiSelector_AppendEvaluatorOutputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Append evaluator run outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[追加计算器运行输出]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Append evaluator outputs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownstreamRunsMultiSelector_Placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select evaluators to append]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择要追加的评估器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";DownstreamRunsSelector_Placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a dependent run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择从属运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Edit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Editor" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Editor]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EditorPanel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open editor panel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开编辑器面板]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EmptyRuns.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can see the status of each flow run, such as whether it is running, completed, or failed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以查看每个流运行的状态，例如它是正在运行、已完成还是失败。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EmptyRuns.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about prompt flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解有关提示流的详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EmptyRuns.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View and manage the execution history of your flows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看和管理流的执行历史记录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EnableAutoGeneratedChatHistory" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable auto-generated chat history]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启用自动生成的聊天历史记录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EnableAutoGeneratedChatHistoryTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat history automatically reflects the conversation history in the chat box. Chat history value is not editable.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天记录自动反映聊天框中的对话历史记录。聊天记录值不可编辑。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Enabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已启用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EndpointName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Endpoints" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Error_RuntimeUpgradeTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please check if the runtime is using the latest version, and try to upgrade it if it's not up to date.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请检查运行时是否正在使用最新版本，如果不是最新版本，请尝试对其进行升级。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EvaluateButton_BuiltInEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automated evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动评估]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Built-in evaluation]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EvaluateButton_BuiltInEvaluation_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate using standard, built-in metrics.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用标准内置指标进行评估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EvaluateButton_CustomEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Custom evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EvaluateButton_CustomEvaluation_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Apply your custom evaluation flow, or simply do a batch run without evaluation.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[应用自定义评估流，或仅执行批量运行但不进行评估。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EvaluateButton_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate the performance of the flow.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估流的性能。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EvaluateSelected" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估所选内容]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EvaluationHistory" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation history]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估历史记录]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";EvaluationRun" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Apply_To_All_Evaluations" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Apply to all evaluations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[应用于所有计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Apply_To_All_Evaluations_Warning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to apply this configuration to all evaluations?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否要将此配置应用于所有计算?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Built_In_Evaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automated evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动评估]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Built-in evaluation]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Bulk_Test_With_Evaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch run with evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[带评估的批处理运行]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Bulk test with evaluation]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Bulk_Test_Without_Evaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch run without evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[不带评估的批处理运行]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Bulk test without evaluation]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Connection_Override_Column_Deployment_Name_Model" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment name / Model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署名称/模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Connection_Override_Column_Input_Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Connection_Override_Hint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This evaluation flow uses LLM. You need to set your connection here to use this evaluation flow.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此评估流使用 LLM。需要在此处设置连接才能使用此评估流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Customized_Evaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customized evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Empty_Eval_List_Tip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There is no evaluation flow.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有计算流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Evaluation_Flow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Evaluation_From_Built_In" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automated evaluation method]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动评估方法]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Built-in evaluation method]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Evaluation_From_Custom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customized evaluation method]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义评估方法]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Evaluation_Run_Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation run display name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估运行显示名称]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Evaluation run name]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Evaluation_Runtime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime for evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算的运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Evaluation_Search" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜索]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Evaluation_Select_Size_Tips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{size} evaluation(s) selected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已选择 {size} 计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.GoToPage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to page number]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[转到页码]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Input_Mapping" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation input mapping]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估输入映射]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.SelectFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select evaluation: {{flowName}}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择评估： {{flowName}}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.Select_Evaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select evaluation method]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择计算方法]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Evaluation_Panel.View_Details" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Existing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Existing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[现有]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Export" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Export]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[导出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Can_Rename_Raw_File_Mode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can rename file under raw file mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以在原始文件模式下重命名文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Create_File" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Create_Folder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建文件夹]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Delete_File" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Discard_Changes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard changes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[放弃更改]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Download" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zip and download all files]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[压缩并下载所有文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Download_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click the download icon to zip and download all files in the flow folder. Unzip and open them in an IDE like VS Code to continue development, testing, and cloud run submissions with the prompt flow SDK installed locally. For more guidance on prompt flow local, you can refer to ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击下载图标以压缩并下载流文件夹中的所有文件。解压缩并在像 VS Code 等 IDE 中打开它们，以继续开发、测试和云运行提交，并在本地安装提示流 SDK。有关本地提示流的更多指南，可以参阅]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Editing_Mode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Raw file mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[原始文件模式]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[File edit]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Editing_Mode_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Switch to Raw File Mode to view the unformatted code of the flow files and edit them in the file editor, including flow yaml files and node source files.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[切换到原始文件模式以查看流文件的未格式化代码，并在文件编辑器中对其进行编辑，包括流 yaml 文件和节点源文件。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.FLow_Yaml_Tips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is a .yaml file that contains the definition of the DAG, detailed setups for flow execution and directs to various files within the folder tree. This file cannot be deleted.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这是一个 .yaml 文件，其中包含 DAG 的定义、流执行的详细设置以及指向文件夹树中的各种文件。无法删除此文件。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.FileOperations" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File Operations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件操作数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.File_Not_Support" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The file is either binary or uses an unsupported text encoding]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[该文件为二进制文件或使用不支持的文本编码]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Files" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Files]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.FolderOperations" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Folder Operations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件夹操作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Invalid_Yaml_Error" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to load the flow, please switch to ‘Raw file mode’ to check if there are any errors in flow.dag.yaml file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法加载流，如果 flow.dag.yaml 文件中存在错误，请切换到“原始文件模式”以检查。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Invalid_Yaml_Save_Error" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to save this .yaml file. Because the .yaml file is in wrong format.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法保存此 .yaml 文件。因为该 .yaml 文件的格式错误。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Location" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[位置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Maximize" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Maximize file explorer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大化文件资源管理器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Minimize" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Minimize file explorer]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大化文件资源管理器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.OpenFileInEditor" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open file in editor]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在编辑器中打开文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.OpenRawFileMode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Turn on raw file mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开原始文件模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Open_In_VSCode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open project in local VSCode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在本地 VSCode 中打开项目]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Open_In_VSCode_Illustration" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to download the project as a ZIP file. This will open VSCode, where you'll select the downloaded ZIP]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击以 ZIP 文件格式下载项目。这将打开 VSCode，你将在其中选择下载的 ZIP]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Preview_Not_Available" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Preview not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[预览不可用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Raw" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Raw]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[原始]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Readonly_File_Warning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To edit the read-only source file, you can go back to the flatten view for inline editing, or switch to raw file mode to edit in this file tab.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要编辑只读源文件，可以返回平展视图进行内联编辑，或切换到原始文件模式以在此文件选项卡中进行编辑。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Readonly_Icon_Tips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This is a source file that can be edited inline on the left side. This file cannot be deleted.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[这是可在左侧内联编辑的源文件。无法删除此文件。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Rename" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rename]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重命名]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Rename_File" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Rename file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重命名文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Select_File" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Select_Folder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择文件夹]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Unsaved_Changes_Content" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are some unsaved change in the flow, do you want to save them?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流中有一些未保存的更改，是否要保存?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Unsaved_Changes_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unsaved changes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更改尚未保存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Upload_File" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上传文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FileStorage.Upload_Folder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上传文件夹]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FillValueFromData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fill value from data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从数据填充值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenInputParameter_NoDeploymentNameTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No deployment found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.ActivateConfig.DisabledReason" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Current selected runtime not support activate config feature, please try to upgrade runtime to the latest version.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[当前选定的运行时不支持激活配置特征，请尝试将运行时升级到最新版本。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.ActivateConfig.SelectTheConditionExpectedResultType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select the condition expected result type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择条件预期结果类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.ActivateConfig.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Activate config]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[激活配置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.ActivateConfig.TypeAConditionHere" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type a condition here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此处键入条件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.ActivateConfig.TypeTheConditionExpectedResultValueHere" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type the condition expected result value here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在此处键入条件预期结果值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.ActivateConfig.When" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[When]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.ActivateConfig.is" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[is]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Activate_Focus_Mode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Activate focus mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[激活焦点模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Add" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Click_To_Add_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to add description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击以添加说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Diff_Mode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Diff mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[差异模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Diff_Mode_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Switch on to compare changes between two versions of a file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[切换到比较某文件两个版本之间的更改。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Discard" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Discard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[放弃]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.EnterNodeName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enter a node name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入节点名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Hide_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hide description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隐藏说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.ImageTypeCannotBeSelectedAsChatInput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image type cannot be selected as chat input. Please set the chat input type to 'list' if you wish to send image(s) in the chatbox.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法选择图像类型作为聊天输入。如果要在聊天框中发送图像，请将聊天输入类型设置为“列表”。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Input_Panel.Data_Item_Select_Hint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a sample to automatically fill values for input data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择示例以自动填充输入数据的值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Input_Panel.Data_Preview_Empty_Hint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cannot preview selected data asset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法预览所选数据资产]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Input_Panel.Data_Preview_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Preview of top {previewTopN} rows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[前 {previewTopN} 行的预览]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Input_Panel.Data_Select_Hint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a data which contains columns in schema]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择架构中包含列的数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Input_Panel.Data_Select_Message" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Selected dataset doesn't contain {notContainedInput} in schema. Please select another dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所选数据集在架构中不包含 {notContainedInput}。请选择其他数据集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Input_Panel.InputMapping" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input mapping]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入映射]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Input_Panel.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Input_Panel.Type" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Input_Panel.Value" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Value]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.No_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无描述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Node_Name_Exist" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Node name is already existed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[节点名称已存在。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Node_Name_Invalid" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The node name should start with a letter or the underscore character, and can only contain alpha-numeric characters and underscores. It should not be a reserved Python keyword such as 'if', 'else', 'and', etc.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[节点名称应以字母或下划线字符开头，并且只能包含字母数字字符和下划线。它不应是保留的 Python 关键字，例如“if”、“else”、“and”等。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.NodesReferTo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[({num} node references)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[({num} 个节点引用)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Api" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Api]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[API]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Code" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Code]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[代码]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Column_Action" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Action]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[操作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Column_ChatInput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Column_ChatOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Column_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Column_Evaluation_Only" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation only]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仅限评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Column_Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Column_Tag" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标记]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Column_Type" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Column_Value" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Value]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Connection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Current_API" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[current API]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[当前 API]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Enum_Multi_Placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please choose options]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请选择选项]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Enum_Single_Placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please choose an option]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请选择一个选项]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Error_Generate_Parameter" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fail to generate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未能生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Error_Generate_Require" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{inputName} is required.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{inputName} 为必填项。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Error_Get_Deployment_List" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fail to get deployment list]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未能获取部署列表]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Fail to get deployment list.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Error_Get_Dynamic_List" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fail to get list options]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未能获取列表选项]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Fail to get list options.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Generate_By" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Prompt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Warning_No_Deployment_Message" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No deployments found: there are no available deployments for {apiName}. Please create a deployment first.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到任何部署: 没有 {apiName} 的可用部署。请先创建部署。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Parameter.Warning_Value_Need_Change" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This field value may need to be changed. Please be aware.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可能需要更改此字段值。请注意。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.ReferTo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Referring to:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[引用:]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Refering to:]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Run_Tooltip.Runtime_Need_Update" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Current runtime cannot parse last run, please try to upgrade runtime to the latest version or execute a new run.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[当前运行时无法分析上次运行，请尝试将运行时升级到最新版本或执行新运行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Save_And_Install" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save and install]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[保存并安装]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Save_Only" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save only]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仅保存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Select_Row" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select row]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Show_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[显示说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Stop_Focus_Mode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop focus mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止焦点模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Tab_Header_Aria_Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow flatten view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流平展视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Wrap_Text" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Wrap text]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文本自动换行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlattenView.Wrap_Text_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Switch on to enable text wrapping to ensure long lines of text do not extend off the screen]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开以启用文本环绕，确保长行文本不会延伸到屏幕外]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Flow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow Id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowInputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowInputsAndOutputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow inputs & outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流输入和输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowInputs_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow inputs are the variables that are processed within the flow. Nodes within the flow can access and use these inputs as their own node-level inputs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流输入是在流中处理的变量。流中的节点可以访问这些输入，并将其用作自己的节点级输入。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Flow outputs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputsData_AppendRelatedResults" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Append related results]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[追加相关结果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputsData_ExportCurrentCSV" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download current page]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载当前页]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputsData_ExportCurrentCSV_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download the output data on the current page as a CSV file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将当前页上的输出数据下载为 CSV 文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputsData_ExportScript" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download data export script]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载数据导出脚本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputsData_ExportScript_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download a script that, once executed, export all output data as CSV or JSONL format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载脚本，执行后将所有输出数据导出为 CSV 或 JSONL 格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputsData_ViewDetail" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputsData_ViewDetail_Details" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputsData_ViewDetail_Trace_View" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trace view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跟踪视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputsData_ViewTrace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View trace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看跟踪]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowOutputs_Tooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow outputs are the results generated after executing the flow. You can specify the outputs of any node within the flow as the flow-level outputs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流输出是执行流后生成的结果。可以将流中任何节点的输出指定为流级输出。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowTest" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow Test]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流测试]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FlowsTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create, iterate, and debug your orchestration flows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建、迭代和调试业务流程流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FolderChoiceText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Folder]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件夹]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FolderName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Folder name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件夹名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ForceCancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Force cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[强制取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Function_Calling" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Function calling]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[函数调用]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Function Calling]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FunctionsCalling_GenSchemaFromTool" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate function schema from tools]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从工具生成函数架构]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";GalleryPanel_ExploreGallery" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Explore gallery]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[浏览库]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";GalleryPanel_ViewLessSamples" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View less samples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更少示例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";GalleryPanel_ViewMoreSamples" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View more samples]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更多示例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";GoToBatchRunDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View run list]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看运行列表]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";GoToBulkRunDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to run details page]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[转到“运行详细信息”页]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";GoToEdit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to edit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[转到编辑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";GotIt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Got it]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[明白了]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Graph.Full_Node_Aria_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Node name is {nodeName}, node type is {nodeType}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[节点名称为 {nodeName}，节点类型为 {nodeType}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Graph.Maximize" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Maximize graph view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大化图形视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Graph.Minimize" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Minimize graph view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最小化图形视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Graph.Node_Aria_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Node name is {nodeName}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[节点名称为 {nodeName}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Graph.Title_Aria_Label" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Graph view of {flowName} – for visualization only. To read and edit the flow, go to the "{flattenViewTabHeader}"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{flowName} 图形视图 - 仅用于可视化效果。若要读取和编辑流，请转到 "{flattenViewTabHeader}"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Graph.title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Graph]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.OverviewSection.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.OverviewSection.SectionDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt flow is a development tool designed to streamline the entire development cycle of AI applications powered by large language models (LLMs).]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示流是一种开发工具，旨在简化由大语言模型(LLM)提供支持的 AI 应用程序的整个开发周期。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.OverviewSection.SectionTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[What is prompt flow?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[什么是提示流?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.ResourcesSection.CreateAndManageComputeSessions" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create and manage prompt flow compute sessions in Azure AI Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure AI Foundry 中创建和管理提示流计算会话]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create and manage prompt flow compute sessions in Azure AI Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.ResourcesSection.DevelopPromptFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Develop a prompt flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开发提示流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.ResourcesSection.ProcessImagesInPromptFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Process images in prompt flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在提示流中处理图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.ResourcesSection.SectionTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Relevant resources]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[相关资源]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.ResourcesSection.TunePromptsUsingVariants" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tune prompts using variants]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用变体优化提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.ResourcesSection.UsePromptflowToolsInAIStudio" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use Prompflow tools in AI Foundry]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 AI Foundry 中使用 Prompflow 工具]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use Prompflow tools in AI Studio]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";HelpPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt flow help]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示流帮助]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Import" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Import]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[导入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";InputDatasetPreview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input dataset preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入数据集(预览)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";InputErrorMessage.DuplicatedName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Duplicated name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重复的名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";InputErrorMessage.InvalidValue" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input value is invalid]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入值无效]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";InputName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";InputStep_InputManually" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input manually]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[手动输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";InputStep_SelectExisting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select existing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择现有]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Input_File_Path_Empty" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Inputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Inputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Inputs_Prefix" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[inputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Instances" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instances]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LangChain" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LangChain]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LangChain]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LeaveConfirm" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You have unsaved changes, go ahead to discard your changes, or cancel to continue editing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你有未保存的更改。如果继续其他操作，则会放弃更改; 如果取消继续其他操作，则可以继续编辑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LineNumber" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Line number]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[行号]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Loading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在加载...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LoadingConnectionInformation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading connection information...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在加载连接信息...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LoadingDataset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading dataset...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在加载数据集...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LoadingDatasetFiles" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading dataset files...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在加载数据集文件...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LoadingEvalFlows" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading evaluation flows...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在加载计算流...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LoadingInputMappingInformation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading input mapping information...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在加载输入映射信息...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Local" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Local]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[本地]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Locate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Locate in graph]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在图形中找到]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LocationToStoreFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Location to store flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存储流的位置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Logs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Logs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[日志]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LogsOf" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Logs of {name}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{name} 的日志]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Lowercase.Connections" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Lowercase.Flows" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[flows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Lowercase.Runs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[runs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Lowercase.Runtimes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[runtimes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MaxTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大令牌数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MediaParameter.Image.DropImageHere" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drop image here]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将图像拖放到此处]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MediaParameter.Image.Placeholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Drag, paste, or enter image URL/relative path]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[拖动、粘贴或输入图像 URL/相对路径]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[ drag, paste, or enter image URL/relative path]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MessageBarDoc_EvaluationMetrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Used automated evaluation methods? Learn more about the evaluation metrics and ways to enhance them. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用了自动评估方法？详细了解评估指标及其增强方法。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Used built-in evaluation methods? Learn more about the evaluation metrics and ways to enhance them. ]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Metrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MetricsCompare" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Metrics compare]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指标比较]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MoreTools" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More tools]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更多工具]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MoreToolsTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select or create a runtime first to view more tools.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[先选择或创建运行时以查看更多工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";More_Details" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更多详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MoveNodeDown" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Move node down]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下移节点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";MoveNodeUp" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Move node up]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上移节点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Move_Down" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Move Down]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下移]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Move_Up" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Move Up]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上移]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NameInvalid" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name is invalid. It can only contain alphanumeric characters, dashes, and underscores, with a limit of 255 characters.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称无效。它只能包含字母数字字符、短划线和下划线，限制为 255 个字符。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Name_Has_Whitespace_Error" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[File/folder name cannot have whitespace at both ends]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[文件/文件夹名称首尾不能有空格]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";New" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NewEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Next" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Next]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下一个]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NoFlowResource_AIStudio_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[A flow is the instruction set that implements the AI logic for your app. You can create a flow by cloning samples, importing local or stored files, or building from scratch.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流是实现应用 AI 逻辑的指令集。可以通过克隆示例、导入本地或存储的文件或从头开始生成来创建流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NoFlowResource_AIStudio_LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more about using flows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解如何使用流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NoFlowResource_AIStudio_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use prompt flow to orchestrate your AI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用提示流协调 AI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NoFlowResource_AIStudio_Title_ViewMyOnly" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You have not created any of your own flows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你尚未创建任何自己的流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NoResultFound" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No result found.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未找到结果。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NodeOutput_Input" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NodeOutput_Output" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NodeOutput_Snapshot" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Snapshot]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[快照]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NodeOutput_Trace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跟踪]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NodeOutput_ViewFull" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View full output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看完整输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NotOwnerTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This flow is owned by {userName}. Clone if you want to edit and experiment with it.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此流归 {userName} 所有。如果要对其进行编辑和试验，请进行克隆。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutputName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Output name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Output_Tokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[令牌]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Outputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutputsSummary_Completed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[instances have completed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实例已完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutputsSummary_Failed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[instances have failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实例失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";OutputsSummary_Processed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[instances have been processed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已处理实例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Overview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[概述]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Overview_TeachingBubble" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View run overview, outputs, logs, metrics and trace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看运行概述、输出、日志、指标和跟踪。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Chat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Common.DefaultTagTypePlural" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标记]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Common.DefaultTagTypeSingular" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[tag]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标记]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Common.FlowDesc" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流说明]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Common.FlowName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Common.RunDisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run display name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行显示名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Common.SmallVMSelectedWarningMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[`Standard_DS1_v2` and `Standard_F2s_v2` may be too small for bigger models and may lead to container termination due to insufficient memory, not enough space on the disk, or probe failure as it takes too long to initialize the container.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[`Standard_DS1_v2` 和 `Standard_F2s_v2` 对于更大的模型来说可能太小，并且可能导致容器终止，原因包括内存不足、磁盘空间不足或探测失败，因为初始化容器所用的时间太长。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Common.VMSize" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VM size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VM 大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.CreateByType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create by type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按类型创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteFunction.ConfirmDeletionContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Are you sure you want to delete this function "{name}"?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否确实要删除此函数“{name}”?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteFunction.ConfirmDeletionTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm Deletion]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[确认删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteFunction.Delete" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteStepButtonTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete this step]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除此步骤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteStepDialog.ConfirmDeletionContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Are you sure you want to delete this step "{name}"?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否确实要删除此步骤 "{name}"?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteStepDialog.ConfirmDeletionTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm Deletion]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[确认删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteStepDialog.DeleteStep" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete step]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除步骤]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteStepVariantButtonTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete this step variant]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除此步骤变量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteStepVariantDialog.ConfirmDeletionContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Are you sure you want to delete this step variant "{name}"?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否确实要删除此步骤变量 "{name}"?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteStepVariantDialog.ConfirmDeletionTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm Deletion]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[确认删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DeleteStepVariantDialog.DeleteStep" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete step variant]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除步骤变量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DismissFunctionChange.Confirm" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[确认]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DismissFunctionChange.ConfirmCancelContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Are you sure you want to discard the changes?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否确定要放弃更改?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.DismissFunctionChange.ConfirmCancelTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm discard changes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[确认放弃更改]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.EditAutomaticRuntimeDetail" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit automatic runtime detail]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑自动运行时详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.EditFlowDetail" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit flow detail]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑流详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.EditFlowSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.GenerateIntegrationCode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate integration code]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成集成代码]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.MaxIdleTimeInvalid" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max idle time is invalid. It can only contain numeric characters.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大空闲时间无效。它只能包含数字字符。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.MaxIdleTimeTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max idle time must be restricted within the range of 1200 seconds to 86340 seconds.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大空闲时间必须限制在 1200 秒到 86340 秒的范围内。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Max idle time must be restricted within the range of 1200 seconds to 86400 seconds.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.PromptsDesignerPageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompts Designer Page]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示设计器页]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.Save" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[保存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.Settings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.TargetEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Target environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[目标环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.ThirdPartyDependencies" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Third-party dependencies]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[第三方依赖项]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.UseData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use data]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用数据]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Designer.ValueRequired" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Value is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[必须提供值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Detail.PromptsDetailPageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompts Detail Page]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示详细信息页]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.EndpointList.LoadingEndpoints" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Loading endpoints...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在加载终结点...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.EndpointList.endpoints" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[endpoints]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Evaluate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Evaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.FlowAuthenticationFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Authentication Failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[身份验证失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.FlowAuthenticationFailedContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This flow was created by {userName}, but access to the workspace and default storage account allows you to edit it. Clone it to avoid accidental overwrites.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此流由 {userName} 创建，但对工作区和默认存储帐户的访问权限允许你对其进行编辑。请克隆它以避免意外覆盖。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Unable to access the dag.yaml file with original storage location at {fileDefinitionPath}. Please check the storage setting.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.FlowIsMissingButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go back to flow list]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[返回到流列表]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.FlowIsMissingContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The flow dag.yaml file is missing from its original storage location at {fileDefinitionPath}. Please check if it has been moved or deleted by mistake.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流 dag.yaml 文件在 {fileDefinitionPath} 的原始存储位置中缺失。请检查是否已误移动或误删除它。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.FlowIsMissingTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow is missing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[缺少流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.ArchiveSelectedFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click 'Archive' to remove a selected flow from the current view. It's not deleted, and you can find it in the 'Archived' view. You can restore it when needed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击“存储”从当前视图中删除所选流。如果未删除，可以在“已存储”视图找到。需要时可以还原。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Archive selected flow]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.Archived" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Archive]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存档]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Archived]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.ChatFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.ChatFlowDesc" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[On top of the standard flow, this option provides the chat history support and a user-friendly chat interface in the authoring/debugging UI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在标准流之上，此选项在创作/调试 UI 中提供聊天历史记录支持和用户友好聊天界面。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[On top of generic flow it provides better chat history support and chat interface in the authoring/debugging UI.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.ChatType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Chat flow]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.ColumnType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.CreateButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.CreateNewFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a new flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建新流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.CreatedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.CreatedOn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.DefaultType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标准]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Generic]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.DeleteFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Description]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[说明]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[description]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.Duration" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Duration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[持续时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.EvaluationFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.EvaluationFlowCreate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create evaluation flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建评估流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.EvaluationFlowDesc" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create an evaluation flow to measure how well the output matches the expected criteria and goals.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建评估流以度量输出与预期条件和目标的匹配程度。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create evaluation flow to measure how well the output matches the expected criteria and goals.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.EvaluationType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Evaluation flow]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.Experiment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Experiment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[试验]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.FlowName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.FlowRunsTab" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow runs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.Flows" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.FlowsConnections" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connections]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Flows connections]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.FlowsRuns" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.FlowsRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.FlowsVectorIndex" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Vector index]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[矢量索引]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.GalleryChat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.GalleryEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.IncludeArchived" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Include archived]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[包括已存档]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.IndexService_EntityNamePlural_Flow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.Owner" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Owner]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所有者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.PromptFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Standard flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标准流]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Generic flow]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.PromptFlowCreate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建流]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Create]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.PromptFlowDesc" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Harness the power of Large Language Models, customized Python code, and more to craft your tailored prompt flow. Test the flow using custom datasets and seamlessly deploy as an endpoint for easy integration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[利用大型语言模型、自定义 Python 代码等的功能来打造定制的提示流。使用自定义数据集测试流，并无缝部署为终结点，以便轻松集成。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Harness the power of Large Language Models, customized Python code, Serp API, and more to craft your tailored prompt flow. Test the flow using custom datasets and seamlessly deploy as an endpoint for easy integration.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.PromptsListPageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示流]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Prompts]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.Restore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restore]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[还原]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.RestoreSelectedFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restore selected flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[还原所选流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.Samples" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Gallery]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[库]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Samples]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.Tags" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标记]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[tags]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.TypeScriptFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeScript flow ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[TypeScript 流 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.TypeScriptFlowDesc" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Harness the power of Large Language Models, customized TypeScript code, and more to craft your tailored prompt flow. Test the flow using custom datasets and seamlessly deploy as an endpoint for easy integration.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[利用大型语言模型、自定义 TypeScript 代码等的功能来打造定制的提示流。使用自定义数据集测试流，并无缝部署为终结点，以便轻松集成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.UpdatedOn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Updated on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.List.ViewMyOnly" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View only my flows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仅查看我的流]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[View my only]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Samples_GoBack" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go Back]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[后退]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Samples_NoReadmeTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Oops, it seems the contributor hasn't provided detailed description for this sample.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[糟糕，参与者似乎没有为此示例提供详细描述。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Samples_TrySample" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clone]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[克隆]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Try sample]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Page.Samples_ViewDetail" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View detail]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Preview" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Preview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[预览]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Previous" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Previous]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上一个]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Prompts_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flows]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Prompts]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Properties" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[属性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RawJson" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Raw JSON]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[原始 JSON]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Refresh" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刷新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RefreshSubscription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刷新订阅]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Remove_All" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Remove all]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[全部删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Run" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunCancel.InProgressText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The cancellation of {runDisplayName} is currently in progress. This may take up to 2 minutes to take effect.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[当前正在取消 {runDisplayName}。这最长可能需要 2 分钟才能生效。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunCancel.InProgressTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Canceling {runDisplayName}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在取消 {runDisplayName}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunCancel.PromptSubText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancelling the job is a permanent action and cannot be undone. Are you sure you want to cancel this job?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消作业是永久性操作，并且无法撤消。是否确实要取消此作业?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunCancel.PromptText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You have requested to cancel job: ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已请求取消作业: ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunCancel.PromptTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel job]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消作业]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunErrors_runtimeTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please ensure that the runtime has all the necessary packages installed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请确保运行时已安装所有必需的包。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunEssentialInfo_FailureRate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failure rate]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[失败率]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunEssentialInfo_ProceedRuns" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Proceed runs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[继续运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunEssentialInfo_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Essential information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基本信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunList.DisplayName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Display name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[显示名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunList.Runs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunList.TokenCount" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Token count]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[令牌计数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunList.ViewOnlyMyRuns" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View only my runs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只查看我的运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunOverviewMainInfo_LatestEvaluation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Latest evaluation]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最新评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunOverviewMainInfo_ViewEvalRun" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View evaluation run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看评估运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunOverviewMainInfo_ViewFull" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View full information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看完整信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunResult.Flow.Fail_To_Fetch_Info" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fail to fetch information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法提取信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunResult.Flow.Fail_To_Fetch_Output" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fail to fetch output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法提取输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunResult.Flow.Fail_To_Fetch_Trace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fail to fetch trace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法提取跟踪]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunResult.Flow.Invalid_Info" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Invalid information]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无效的信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunResult.Flow.Invalid_Output" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Invalid output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出无效]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunResult.Flow.Invalid_Trace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Invalid trace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无效跟踪]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunResult.Node.Fail_To_Fetch_Output_Info" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fail to fetch output info]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法提取输出信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";RunResult.Node.Invalid_Output_Info" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Invalid output info]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输出信息无效]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.Add" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add Runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加运行时]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Add]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.CheckVersion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Check version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[检查版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.Create" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.Delete" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.Edit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.NoUpgradeTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This runtime have the latest image and no updates are required. Congratulations. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此运行时具有最新映像，无需更新。恭喜。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.Refresh" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Refresh]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[刷新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.Update" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Update]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.Upgrade" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upgrade]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[升级]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Command.UpgradeTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your runtime is based on an outdated image that does not have the latest features. To access the full functionality of our service, please update your runtime to the most recent version. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时基于没有最新功能的过时映像。若要访问我们服务的完整功能，请将运行时更新为最新版本。 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Compute.Authenticate_Compute_In_Flow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need to be authenticated to read data asset in promptflow. Please use the authenticate button to get authenticated.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要经过身份验证才能读取提示流中的数据资产。请使用身份验证按钮进行验证。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Compute.Authenticate_Compute_In_Runtime_Create" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need to be authenticated to use this compute instance as runtime. Please use the authenticate button to get authenticated.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要经过身份验证才能将此计算实例用作运行时。请使用身份验证按钮进行验证。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Compute.Authenticate_Compute_Success" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Successful authenticated to runtime. You can use this runtime to read data asset now.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已成功完成身份验证到运行时。现在可以使用此运行时读取数据资产。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Compute.CacheUserToken.CannotCommunicateWithCI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Can't communicate with compute instance, please check the status of the compute instance, also make sure the connectivity (create PE to workspace) for your compute instance if your using private network. {learnMoreLink}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法与计算实例通信，请检查计算实例的状态，同时确保计算实例的连接（使用专用网络时为计算实例创建 PE 到工作区）。{learnMoreLink}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Compute.CacheUserToken.GetComputeUrlFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to get compute url.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法获取计算 url。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.AddComputeInstanceRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add compute instance runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加计算实例运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.AddRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ Add runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.AutoRestartComputeInstance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Auto restart compute instance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动重启计算实例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.Confirm" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Confirm]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[确认]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.Create" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.CustomApplication" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Custom application]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义应用程序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.CustomApplicationName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Custom application name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义应用程序名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.CustomEnvironmentLearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[follow this guidance to learn more details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按照此指南了解更多详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.CustomImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Custom image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义映像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.CustomizeImageIllustrate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The compute instance do not have runtime custom application, we will automatically install one for this compute instance.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算实例没有运行时自定义应用程序，我们将自动为此计算实例安装一个。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.DefaultEnvironmentLearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[learn more about the packages in this default image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细了解此默认映像中的包]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.DefaultImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Default image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[默认图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.DockerImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Docker image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Docker 映像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.EditComputeInstanceRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit compute instance runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑计算实例运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.Existing" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Existing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[现有]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.New" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[New]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[新建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.NoCustomApplication" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No custom application]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无自定义应用程序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.PleaseSelectCI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please select compute instance first]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请先选择计算实例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.PleaseSelectCustomApplication" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Please select custom application]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[请选择自定义应用程序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.RestartComputeInstance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Restart compute instance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重启计算实例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.RestartInformText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To create/update a runtime on a compute instance, it needs to be restarted automatically. This may affect your tasks and jobs that are running on this compute instance. Click confirm if you want to proceed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要在计算实例上创建/更新运行时，需要自动重新启动它。这可能会影响正在此计算实例上运行的任务和作业。如要继续操作，请单击“确认”。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.RuntimeEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.RuntimeName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.UnableConnectApplication" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We are unable to connect to this application as runtime. Please make sure this custom application is ready to use. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法作为运行时连接到此应用程序。请确保此自定义应用程序已准备好使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Create.Update" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Update]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.AssignTo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assign to]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[指派给]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.ComputeInstance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute instance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算实例]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.ComputeInstanceCount" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute instance count]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算实例计数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.ComputeInstanceType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute instance type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算实例类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.ComputeType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.CreatedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.CustomApplication" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Custom application]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义应用程序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.Deleting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ (Deleting...)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(正在删除...)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.Deployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.Deprecated" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ (deprecated)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(已弃用)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.Endpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[托管终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.InstanceCount" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instance count]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实例计数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.InstanceType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Instance type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[实例类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.LinkedCompute" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Linked compute]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[链接的计算]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.ManagedEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[托管终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.Runtime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.RuntimeEnvironment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime environment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时环境]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.Status" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[状态]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Data.UpdatedOn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Updated on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Delete.AutomaticRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[automatic runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Delete.DeleteButtonName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Delete.DeleteDialogItemType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[connection]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Delete.DeleteDialogText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting runtime is a permanent action that cannot be undone. Are you sure you wish to proceed?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除运行时是一个永久性操作，无法撤销。确实要继续吗?]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Deleting connections is a permanent action that cannot be undone. Are you sure you wish to proceed?]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Delete.Stop" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Detail.Edit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Detail.InvalidRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Invalid runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时无效]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Detail.ResourceNotFound" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime with name "{runtimeName}" was not found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到名为“{runtimeName}”的运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Error.Error" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[错误]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Error.Unknown" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unknown]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未知]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Install" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.InstanceDetail.DataAcquisitionFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime acquisition failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时获取失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.InstanceDetail.DataWithNameAcquisitionFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime with name "{runtimeName}" acquisition failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名为“{runtimeName}”的运行时获取失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.InstanceDetail.Details" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.NoRuntimes.CreateRuntimeDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You need a compute session to run prompt flows.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要计算会话才能运行提示流。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.NoRuntimes.CreateRuntimeMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create a prompt flow runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建提示流运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.NoRuntimes.NoRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Reset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Runtime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.RuntimeEntityName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.RuntimeErrors.Message" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{title}: {details}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{title}: {details}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.RuntimeErrors.ViewRunErrorsLink" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See more details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更多详情]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.ChangeBaseImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Change base image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更改基础映像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.ChangeComputeSessionSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Change compute session settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更改计算会话设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.ConnectSession" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connect session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.ConnectWithAdvancedSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connect with advanced settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用高级设置进行连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.InstallPackagesFromRequirements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install packages from requirements.txt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装来自 requirements.txt 的包]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.ResetComputeSession" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reset compute session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重置计算会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.StartComputeSession" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start compute session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启动计算会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.StartWithAdvancedSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start with advanced settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从高级设置开始]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.StopComputeSession" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop compute session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止计算会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Action.ViewInstalledPackages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View installed packages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看已安装的包]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Description.GetFlowIdOrExperimentIdFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to get flowId or experimentId]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未能获取 flowId 或 experimentId]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Description.SessionOperationFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Session operation failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[会话操作失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Description.SessionPanelInfo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Change VM and identity will take 1 - 3 minutes to restart the session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更改 VM 和标识需要 1 - 3 分钟才能重启绘画]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Description.SessionPanelInfoContainComputeSelectCI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you wish to switch to a different compute type or compute instance, ensure to stop the session initially.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果要切换到其他计算类型或计算实例，请确保首先停止会话。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Description.SessionPanelInfoContainComputeSelectServerless" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you wish to switch to a different compute type, ensure to stop the session initially. Change VM and identity will take 1 - 3 minutes to restart the session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果要切换到其他计算类型，请确保首先停止会话。更改 VM 和标识需要 1 - 3 分钟才能重启绘画]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Description.SessionStartingTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[It will take 1 - 3 minutes to start the session.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启动会话需要 1-3 分钟。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Description.StopSessionReminderText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stopping the compute session will impact any ongoing runs associated with compute session. Are you sure you want to proceed?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止计算会话将影响与计算会话关联的任何正在进行的运行。是否确实要继续?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Identity.AiStudioUseIdentityTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project user assigned managed identity (UAI) can be used by session for storage access for promptflow run. Admin would need to be assigned Managed Identity Operator Role for the scope of UAI to be able to assign UAI to project. MI operator role includes the action Microsoft.ManagedIdentity/userAssignedIdentities/assign/action which is needed to assign UAI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[项目用户分配的托管标识 (UAI) 可由会话用于提示流的存储访问。需要向管理员分配 UAI 范围的托管标识操作员角色才能向项目分配 UAI。MI 操作员角色包括分配 UAI 所需的 Microsoft.ManagedIdentity/userAssignedIdentities/assign/action 操作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Identity.useIdentityTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace user assigned managed identity (UAI) can be used by session for storage access for promptflow run. Admin would need to be assigned Managed Identity Operator Role for the scope of UAI to be able to assign UAI to workspace. MI operator role includes the action Microsoft.ManagedIdentity/userAssignedIdentities/assign/action which is needed to assign UAI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作区用户分配的托管标识 (UAI) 可由会话用于提示流的存储访问。需要向管理员分配 UAI 范围的托管标识操作员角色才能向工作区分配 UAI。MI 操作员角色包括分配 UAI 所需的 Microsoft.ManagedIdentity/userAssignedIdentities/assign/action 操作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Migration.Authoring" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime is renamed to session which provide improved experience to help you easily manage the lifecycle of compute and customize packages. To continue editing this flow, please upgrade to session experience.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时已重命名为会话，可提供改进的体验，帮助你轻松管理计算的生命周期以及自定义包。若要继续编辑此流，请升级到会话体验。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Migration.Clone" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This evaluation flow is build on top of old runtime experience, please upgrade to session experience.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此计算流基于旧的运行时体验构建而成，请升级到会话体验。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Migration.LearnMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[ Learn more:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[ 了解详细信息:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Migration.RuntimeList" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runtime is renamed to session which provide improved experience to help you easily manage lifecycle of compute and customize packages.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时已重命名为会话，可提供改进的体验，帮助你轻松管理计算的生命周期以及自定义包。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.ApplyAndRestartComputeSession" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Apply and restart compute session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[应用并重启计算会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.ApplyAndStartComputeSession" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Apply and start compute session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[应用并启动计算会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.AssignedIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assigned identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分配的标识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.BaseImageSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Base image settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基础映像设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.BaseImageSettingsDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The base image from your flow endpoint will also be used to build the container environment, or you can change the image in flow.dag.yaml manually.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流终结点中的基础映像也将用于生成容器环境，或者可以手动更改 flow.dag.yaml 中的映像。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.BaseImageSettingsEditButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Base image settings edit button]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[基础图像设置编辑按钮]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.ComputeName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.ComputeSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.ComputeSettingsEditButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute settings edit button]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算设置编辑按钮]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.ComputeType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.CustomizedImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customized image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.CustomizedImageDescription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only image in public docker registry and azure container registry are supported. If you specify image in azure container registry please make sure you (or the user assigned manage identity if you specify it when start automatic runtime) have ACR pull permission.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仅支持公共 docker 注册表和 azure 容器注册表中的映像。如果在 azure 容器注册表中指定映像，请确保你或用户分配的托管标识（如果你在启动自动运行时的时候指定了它）拥有 ACR 拉取权限。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.CustomizedImagePlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[<url-of-container-registry>/<image_path>:<image_label>]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[<url-of-container-registry>/<image_path>:<image_label>]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.DataFormat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.DefaultImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Default image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[默认图像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.Disabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已禁用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.Enabled" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enabled]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已启用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.Identity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.IdleShutdown" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Idle shutdown]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[空闲关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.ImageFormatError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image URL didn't correctly, please specify full image URL in container registry.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[映像 URL 不正确，请在容器注册表中指定完整的映像 URL。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.ImageType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[映像类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.Review" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[审阅]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.ReviewAndApply" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Review + apply]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[审阅 + 应用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.UseLatestImage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use latest image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用最新映像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.VMCount" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Virtual machine count]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[虚拟机计数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.VMSize" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VM size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VM 大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Settings.WorkspaceUserAssignedManagedIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace's user-assigned managed identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作区的用户分配的托管标识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.State.ComputeSessionFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute session failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算会话失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.State.ComputeSessionNotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute session not available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算会话不可用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.State.ComputeSessionRunning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute session running]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算会话正在运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.State.ComputeSessionStarting" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute session starting]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算会话正在启动]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.State.ComputeSessionStopping" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute session stopping]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算会话正在停止]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.State.ConnectedTo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connected to]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已连接到]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.State.SessionDisconnected" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Session disconnected]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已断开会话连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.State.StartComputeSession" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start compute session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启动计算会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.State.Unknown" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unknown]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未知]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.HowToAddProperty" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[[how to add python_requirements_txt property]5D;]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[[如何添加 python_requirements_txt 属性]5D;]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.NotFindRequirementsProperty" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Didn't find python_requirements_txt property in flow.dag.yaml.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 flow.dag.yaml 中找不到 python_requirements_txt 属性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.OpenChangeBaseImageModal" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open 'Change base image' modal]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开“更改基础映像”模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.OpenChangeComputeSessionSettingModal" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open 'Change compute session setting' modal]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开“更改计算会话设置”模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.OpenInstalledPackagesModal" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open 'Installed packages' modal]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开“已安装包”模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.OpenRequirementFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open requirements file failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开要求文件失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.OpenRequirements" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Open requirements.txt (it can also be other file if customer change its name in flow.dag.yaml)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[打开requirements.txt (如果客户在 flow.dag.yaml 中更改其名称，它也可以是其他文件)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.RequirementsFileNotFound" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The {requirements} file was not found in the flow directory. Please verify the accuracy of the file name.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在流目录中找不到 {requirements} 文件。请验证文件名的准确性。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.ResetSession" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Initialize a new container and install package from requirements.txt, which may take a few minutes. This action will impact ongoing runs on this session.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[初始化新容器，并从 requirements.txt 安装包，这可能需要几分钟时间。此操作将影响此会话上正在进行的运行。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.SessionTurnToStopping" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Session turn to stopping]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[会话转为停止]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.StartComputeSessionToEnableAction" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start compute session to enable the action.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启动计算会话以启用操作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.StartWithAdvancedSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start compute session with custom configurations, such as different compute type like serverless compute or compute instance, idle shutdown behavior etc.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用自定义配置启动计算会话，例如无服务器计算或计算实例等计算类型、空闲关闭行为等。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Tooltip.StartingSession" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start compute session with default setting (default VM SKU and one hour idle shutdown) on serverless compute.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在无服务器计算上使用默认设置(默认 VM SKU 和一小时空闲关闭)启动计算会话。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Wording.Image" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Image]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[映像]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Wording.Session" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Wording.SessionAdvancedSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Session advanced settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[会话高级设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Wording.SessionStatusMessages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Session status messages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[会话状态消息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Wording.StartComputeSessionWithAdvancedSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start compute session with advanced settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用高级设置启动计算会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Wording.StopComputeSessionFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop compute session failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止计算会话失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Session.Wording.VMSize" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[VM size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[VM 大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.StatusPanel.AccordionAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expand or collapse details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[展开或折叠详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.StatusPanel.Close" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Close]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.StatusPanel.Error" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[错误]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.StatusPanel.Running" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Running]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.StatusPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status messages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[状态消息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.StatusPanel.WarningsHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Warnings ({count})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[警告({count} 个)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.ACRecommendTooltip1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic runtime is currently in public preview and provides an easy way to customize packages while automatically managing compute resources for you. To learn]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时目前为公共预览版，它提供了一种自定义包的简便方法，同时为你自动管理计算资源。要了解]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.ACRecommendTooltip2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[how to switch from compute instance runtime to automatic runtime.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如何从计算实例运行时切换到自动运行时。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AdvancedSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高级设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AiStudioIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project user assigned managed identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[项目用户分配的托管标识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AiStudioStartWithAdvancedSettingsTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start an automatic runtime using a custom configuration, specifying the VM size, idle shutdown behavior, and whether to use UAI in the project.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用自定义配置启动自动运行时，指定 VM 大小、空闲关闭行为以及是否在项目中使用 UAI。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AiStudioUseIdentityDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There is no user assigned managed identity to this project.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此项目没有用户分配的托管标识。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[These is no user assigned managed identity to this project.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AiStudioUseIdentityTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Project user assigned managed identity (UAI) can be used by automatic runtime for storage access for promptflow run. Admin would need to be assigned Managed Identity Operator Role for the scope of UAI to be able to assign UAI to project. MI operator role includes the action Microsoft.ManagedIdentity/userAssignedIdentities/assign/action which is needed to assign UAI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时可对提示流运行的存储访问使用项目用户分配的托管标识(UAI)。需要向管理员分配托管标识操作员角色来获取 UAI 的范围，这样他们才能向项目分配 UAI。MI 操作员角色包括分配 UAI 所需的 Microsoft.ManagedIdentity/userAssignedIdentities/assign/action 操作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AiStudioUserAssignedIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use project user assigned managed identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用项目用户分配的托管标识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AutomaticRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AutomaticRuntimeCannotChoice" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic runtime is failed, it can not be chosen.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时失败，无法选择。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AutomaticRuntimeOperationFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic runtime operation failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时操作失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.AutomaticRuntimeStatusMessages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic runtime status messages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时状态消息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.ComputeInstanceRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute instance runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算实例运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Copy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[复制]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DescriptionTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Descriptive title]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[描述性标题]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.CopyFileURL" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy URI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[复制 URI]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.CopyFilename" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Copy filename]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[复制文件名]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.CreateModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Register model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[注册模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.CreateModelNoPermission" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have the proper role based access to register a model. Missing permission: {permission}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你没有注册模型的基于角色的合适权限。缺少权限: {permission}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.DiagnosticsLog" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Diagnostics log]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[诊断日志]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.Download" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Download]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[下载]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.FlowDiagnosticsNotAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The flowDiagnostics information is not currently available.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[flowDiagnostics 信息当前不可用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.LinkToFile" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Link to this file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[链接到此文件]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.ViewDiagnosticsLog" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View diagnostics log]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看诊断日志]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.DiagnosticsLog.ViewInAzurePortal" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View in Azure Portal]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在 Azure 门户中查看]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Edit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.EditAutomaticRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit automatic runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑自动运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.FlowIdOrExperimentIdError" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[flowId or experimentId error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[flowId 或 experimentId 错误]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Identity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace user assigned managed identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作区用户分配的托管标识]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Identity]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTime.DisableIdleShutdown" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Disable idle shutdown]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[禁用空闲关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTime.EnableIdleShutdown" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable idle shutdown]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启用空闲关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTime.EnableIdleShutdownTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can set a automatic runtime to automatically shut down after it has been idle for a set amount of time]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可将自动运行时设置为在空闲一段时间后自动关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTime.HoursSelector" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hours selector]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[小时选择器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTime.IdleShutdown" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Idle shutdown]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[空闲关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTime.IdleShutdownTooltipButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Idle shutdown tooltip button]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[空闲关闭工具提示按钮]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTime.MinutesSelector" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Minutes selector]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分钟选择器]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTime.ShowIdleShutdown" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Enable idle shutdown: {hour} hour(s) {minute} minute(s)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启用空闲关闭: {hour} 小时 {minute} 分钟]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTime.ShutdownTime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Shutdown after {hour} hour(s) {minute} minute(s) of inactivity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[处于非活动状态 {hour} 小时 {minute} 分钟后关闭]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.IdleTimeAtLeast" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Idle time must be at least 20 minutes ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[空闲时间必须至少为 20 分钟 ]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.InstallPackage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install package]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[安装包]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.MoreDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[More details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更多详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.PackagesNameHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.PackagesVersionHeader" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.PanelInfo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Change VM and identity will take 1 - 3 minutes to restart the runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更改 VM 和标识需要 1 - 3 分钟才能重启运行时]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Change VM and identity will cost 5 - 10 minutes to restart the runtime]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.PanelInfoContainComputeSelectCI" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you wish to switch to a different compute type or compute instance, ensure to stop the runtime initially.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果要切换到其他计算类型或计算实例，请确保起初停止运行时。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.PanelInfoContainComputeSelectServerless" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[If you wish to switch to a different compute type, ensure to stop the runtime initially. Change VM and identity will take 1 - 3 minutes to restart the runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[如果要切换到其他计算类型，请确保起初停止运行时。更改 VM 和标识需要 1 - 3 分钟来重启运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Reset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reset]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.ResetAutomaticRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Reset automatic runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[重置自动运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.RuntimeNotReady" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your runtime is not ready to use.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时未准备就绪，无法使用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.RuntimeStartingTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[It will take 1 - 3 minutes to start the runtime.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启动运行时将需要 1-3 分钟。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[It will cost 1 - 3 minutes to start the runtime.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.RuntimeStateNotReady" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your runtime in "{state}" state is not ready to use. {startingStateInfo}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[处于“{state}”状态的运行时还未就绪，无法使用。{startingStateInfo}]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Your runtime in "{state}" state is not ready to use.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Start" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[开始]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.StartAc_TeachingBubble_Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic runtime can dynamically install dependent packages based on the requirements of your flow. The startup may take a few minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时可以根据流的要求动态安装依赖包。启动可能需要几分钟时间。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.StartAc_TeachingBubble_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start automatic runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启动自动运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.StartAutomaticRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start automatic runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[启动自动运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.StartWithAdvancedSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start with advanced settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从高级设置开始]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.StartWithAdvancedSettingsTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Start an automatic runtime using a custom configuration, specifying the VM size, idle shutdown behavior, and whether to use UAI in the workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用自定义配置启动自动运行时，指定 VM 大小、空闲关闭行为以及是否在工作区中使用 UAI。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Stop" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.StopAutomaticRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop automatic runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止自动运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.StopAutomaticRuntimeFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop automatic runtime failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未能停止自动运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.StopAutomaticRuntimeReminderText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stopping the runtime will impact any ongoing runs associated with this runtime. Are you sure you want to proceed?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止运行时将影响任何与此运行时关联且正在进行的运行。确定要继续吗?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Tooltip.Edit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit the runtime configurations.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑运行时配置。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Tooltip.InstallPackage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Install the packages from requirements.txt.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[从 requirements.txt 安装包。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Install packages from requirement.txt file.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Tooltip.Reset" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select 'Reset' to initialize a new container and install packages from requirements.txt, which may take a few minutes. This action will impact ongoing runs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择“重置”以初始化新容器，并从 requirements.txt 安装包，这可能需要几分钟时间。此操作将影响正在进行的运行。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Click 'Reset' to initialize a new container and install packages from requirements.txt, which may take a few minutes. This action will impact ongoing runs.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Tooltip.SaveAndInstall" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save the file and install the required packages in the runtime.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[保存文件并在运行时中安装所需的包。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Tooltip.Start" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Automatic runtime can dynamically install dependent packages based on the requirements of your flow. The startup may take a few minutes.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时可以根据流的要求动态安装依赖包。启动可能需要几分钟时间。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.Tooltip.Stop" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select 'Stop' to halt the runtime. This action will impact ongoing runs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择“停止”以停止运行时。此操作将影响正在进行的运行。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Click 'Stop' to halt the runtime. This action will impact ongoing runs.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.UserAssignedIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use workspace user assigned managed identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用工作区用户分配的托管标识]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[User assigned managed identity]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.ViewInstalledPackages" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View installed packages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看已安装的包]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.ViewPackagesPanelTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View packages]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看包]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.ViewPackagesPanelTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Packages include the one baked in base image and installed via requirements.txt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[软件包包含在基础映像中预装的并通过 requirements.txt 安装的包]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.useIdentityDisabledTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There is no user assigned managed identity to this workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此工作区没有用户分配的托管标识。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[These is no user assigned managed identity to this workspace.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.TrainSession.useIdentityTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace user assigned managed identity (UAI) can be used by automatic runtime for storage access for promptflow run. Admin would need to be assigned Managed Identity Operator Role for the scope of UAI to be able to assign UAI to workspace. MI operator role includes the action Microsoft.ManagedIdentity/userAssignedIdentities/assign/action which is needed to assign UAI.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自动运行时可对提示流运行的存储访问使用工作区用户分配的托管标识(UAI)。需要向管理员分配托管标识操作员角色来获取 UAI 的范围，他们才能向工作区分配 UAI。MI 操作员角色包括分配 UAI 所需的 Microsoft.ManagedIdentity/userAssignedIdentities/assign/action 操作。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Type.ComputeInstance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute instance runtime (recommended)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算实例运行时(推荐)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Compute instance runtime (for personal use)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Type.ComputeInstanceRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Compute instance runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[计算实例运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Type.ManagedOnlineEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed online deployment runtime (for shared use)]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[托管的联机部署运行时(适合共享使用)]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Managed online deployment runtime (for team use)]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Unknown" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unknown]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未知]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Warning.MIRDeprecatedWarning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Managed online endpoint / deployment runtime is deprecated. Please migrated this runtime as soon as possible. ]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[托管的联机终结点/部署运行时已弃用。请尽快迁移此运行时。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.Warning.Warning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Warning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[警告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.selector.NoRuntimeAvailable" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No runtime available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有可用的运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Runtime.selector.SelectRuntime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select runtime]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择运行时]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Save" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[保存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Schema" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Schema]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[架构]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Search" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search more tools]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜索更多工具]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Search]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SearchByName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search by name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[按名称搜索]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.AddConnection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Columns.AssignedTo" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assigned to]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[分配对象]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Columns.ConnectionName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Columns.ConnectionType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Columns.CreatedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Columns.Provider" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供程序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Columns.SharingScope" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sharing scope]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共享范围]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Columns.Size" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Size]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[大小]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Columns.UpdatedOn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Updated on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Command.Add" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Command.Create" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Create]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Command.Custom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Custom]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Command.Delete" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Connection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.ConnectionEntityName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.ConnectionPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.AddKeyValuePairs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add key-value pairs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加键值对]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.Cancel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Cancel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[取消]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.ConnectionNameExists" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection with this name already exists. Keeping using this name will overwrite the existing connection.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已存在具有此名称的连接。继续使用此名称将覆盖现有连接。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Value is required]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[必须提供值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.ExpiryTime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expiry time]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[到期时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.ForExample" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For example]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[例如]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.IsSecret" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[is secret]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[为机密]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[密钥]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.KeyValuePairs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key-value pairs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[键值对]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.Provider" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供程序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.Save" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Save]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[保存]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.SecretStar" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[······]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[······]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.SetAsWorkspace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Set as workspace level key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[设置为工作区级别密钥]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Create.Value" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Value]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Delete.DeleteButtonName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Delete.DeleteButtonNoPermission" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You do not have the proper role based access to delete a connection. Missing permission: {permission}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你没有删除连接的基于角色的合适权限。缺少权限: {permission}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Delete.DeleteDialogItemType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Delete.DeleteDialogText" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Deleting connections is a permanent action that cannot be undone. Are you sure you wish to proceed?]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除连接是永久性操作，无法撤消。确定要继续吗?]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Delete.OperationsColumnAriaLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Operations]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[操作]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Description.Dot" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[.]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Description.NewConnectionBanner" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This prompt flow connection UI has been integrated into the new unified connection UI. To explore this updated experience, please navigate to]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此提示流连接 UI 已集成到新的统一连接 UI 中。要浏览此更新的体验，请导航到]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Description.WorkspaceConnection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[workspace connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作区连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Detail.Edit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Detail.ResourceNotFound" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection with name "{connectionName}" was not found]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到具有名称 "{connectionName}" 的连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Error.DeleteFailure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Delete failure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[删除失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Error.Error" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Error]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[错误]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Error.SaveFailure" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{operate} connection "{connectionName}" failure]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{operate} 连接 "{connectionName}" 失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.CreatedBy" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Created by]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[创建者]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.DataAcquisitionFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection acquisition failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接获取失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.DataWithNameAcquisitionFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection with name "{connectionName}" acquisition failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取名为 "{connectionName}" 的连接失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.Details" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.ExpiryTime" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expiry time]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[到期时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.HideSecrets" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hide]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隐藏]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.Key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[密钥]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.KeyValuePair" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Key-value pair]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[键值对]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Name]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[名称]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.Provider" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Provider]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提供程序]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.SecretKeys" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Secret keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[密钥]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.SecretsPermission" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Only users who have permissions can view secret keys]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[只有拥有权限的用户才能查看密钥]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.SharingScope" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Sharing scope]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共享范围]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.ShowSecrets" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[显示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.UpdatedOn" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Updated on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.api_base" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[API base]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[API 基础]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.api_key" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[API key]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[API 密钥]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.api_type" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[API type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[API 类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.api_version" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[API version]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[API 版本]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.endpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.organization" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Organization]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[组织]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.InstanceDetails.resource_id" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Resource ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[资源 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.NoConnectionSelectorPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No connection available]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[没有可用的连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.PageResources.PageStatusFormat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Showing {first}-{last} of {total} {entityNamePlural}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[显示 {first}-{last} 个 {entityNamePlural} (共 {total} 个)]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.PageResources.PageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Panel.Add" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add {connectionType} connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加 {connectionType} 连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SecretsManagement.Panel.Edit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Edit {connectionType} connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[编辑 {connectionType} 连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SeeAllProperties" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[See all properties]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看所有属性]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectAConnection" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a connection]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择连接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectADeployment" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a deployment]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择部署]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectAModel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a model]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择模型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectChatFlowInput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Selected input will be used as chat input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所选输入将用作聊天输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectChatFlowOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Selected output will be used as chat output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[所选输出将用作聊天输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectEvaluationRun" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select evaluation run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择评估运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SelectFlowType" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select flow type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择流类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SemanticKernel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Semantic Kernel]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[语义内核]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.AdvancedSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Advanced settings]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[高级设置]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.BatchRun" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Batch run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[批处理运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.Concurrency" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Concurrency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[并发]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.ConcurrencyTip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Number of records running in parallel.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[并行运行的记录数。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.Customize" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Customize]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[自定义]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.DataFormat" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Data format]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[数据格式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.EarlyStop" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Early stop]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提前停止]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.EvalRun" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluation run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.IntegerPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input an integer number]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入整数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.MaxActiveVms" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Max active VMs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[最大活动 VM 数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.MultipleVmTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ideal for handling larger data and jobs taking more than 10 hours and under 10 days to process.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[非常适合处理用时超过 10 小时且不到 10 天的较大数据和作业。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.MultipleVms" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Multiple VMs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[多台 VM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.RecordsFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[records failed.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[个记录失败。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.RunningTimeMsgContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Maximum running time for 'Single VM' is 10 hours. For jobs more than 10 hours, please change to 'Multiple VMs'. Maximum running time for 'Multiple VMs' is 10 days.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[“单个 VM”的最大运行时间为 10 小时。对于超过 10 小时的作业，请更改为“多个 VM”。“多个 VM”的最大运行时间为 10 天。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.RunningTimeMsgTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Timeout notification]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[超时通知]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.SameAsBatch" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Same as batch run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[与批处理运行相同]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.SelectIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择标识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.SelectWSIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select workspace's user-assigned manged identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择工作区用户分配的托管标识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.SingleVm" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Single VM]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单一 VM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.SingleVmTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Ideal for handling small data and jobs taking under 10 hours to process.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[非常适合处理用时不超过 10 小时的小型数据和作业。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.StopAfter" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop after]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止执行时间]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.UserIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[User identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[用户标识]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.UserIdentityTip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This identity helps access data, retrieve connections, pull images, and install packages to compute.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此标识可帮助访问数据、检索连接、拉取映像以及安装包以进行计算。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.VmCount" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select virtual machine count]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择虚拟机计数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.WorkspaceIdentity" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Workspace's user-assigned manged identity]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工作区用户分配的托管标识]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Workspace's user-assigned manged identity: Default identity]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SessionConfig.WorkspaceIdentityTip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The primary user-assigned identity to workspace will be used, Learn more about how to create and update user-assigned identities for a workspace.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[将会使用由用户分配到工作区的主标识，详细了解如何为工作区创建和更新用户分配的标识。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Share" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Share]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共享]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Skip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Skip]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跳过]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Snapshot" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Snapshot]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[快照]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Status" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[状态]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Stop" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";StopFocusMode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Stop focus mode]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[停止焦点模式]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Submit" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Submit]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提交]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SubmitChatBox.ChatInput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SubmitChatBox.ChatOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Chat output]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[聊天输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SubmitFlow_Parse_Input_Loading" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validating and parsing input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在验证和分析输入]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Validate and parsing input]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SubmitFlow_RunCompleted" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run completed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SubmitFlow_RunFailed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行失败]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SubmitFlow_Running" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Running]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SubmitFlow_RunningNodeName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Running {name}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在运行 {name}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Subscription" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Subscription]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[订阅]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";SystemMetrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[System metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[系统指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tags" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tags]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[标记]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Temperature" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Temperature]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[温度]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Test" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Test]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[测试]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tokens_Completion" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Completion]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[完成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tokens_Prompt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tokens_Total" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Total]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[总计]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tool.LLM" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[LLM]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[LLM]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolCodeEditor_ViewFull" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View full]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看完整]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolCodeValidation.InputEmptyReasonTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Input section is empty. This may be due to one of the following reasons:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[输入部分为空。这可能是由于以下原因之一导致的:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolCodeValidation.InputEmptyReason_NeedValidate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[1. You haven't clicked "Validate and parse input" button to parse the input.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[1. 尚未单击“验证并分析输入”按钮来分析输入。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[You haven't clicked "Validate and parse input" button to parse the input.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolCodeValidation.InputEmptyReason_NotInCode" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[2. Your Python function or prompt doesn't require any input.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[2. Python 函数或提示不需要任何输入。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Your Python function or prompt doesn't require any input.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolCodeValidation.ValidateButton" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validate and parse input]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[验证和分析输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolCodeValidation.ValidateButtonTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click this button to parse the input parameters required by the Python function or the input from the prompt's Jinja template enclosed in {{ }}. Once parsed, you can set the actual input value.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击此按钮可分析 Python 函数要求的输入参数或提示符中括在 {{ }} 中的 Promptja 模板的输入。分析后，可以设置实际输入值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolCodeValidation.ValidateSuccessMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation and parsing input completed successfully.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[验证和分析输入已成功完成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.Code" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Write {language} function. The function input definition will be exposed in the Input section, where you can enter values for each input field.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[写入 {language} 函数。函数输入定义将在“输入”部分中公开，你可以在其中输入每个输入字段的值。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Write python function. The function input definition will be exposed in the Input section, where you can enter values for each input field.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.EvaluationSettings" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can choose to test your prompt flow and evaluate the output performance using automated or customized evaluation method. You can submit batch run without evaluation if you want to evaluate the outputs later.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以选择使用自动化或自定义评估方法测试提示流并评估输出性能。如果以后要评估输出，可以提交批处理运行而不进行评估。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[You can choose to test your prompt flow and evaluate the output performance using built-in or customized evaluation method. You can submit batch run without evaluation if you want to evaluate the outputs later.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.FillValueFromData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Fill input value from a dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[填充数据集中的输入值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.FinishTune" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Collapse variants, displaying only the default variant.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[折叠变体，仅显示默认变体。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.FlowInput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow input is the data passed into the flow as a whole. Define the input schema by specifying the name and type. Set the input value of each input to test the flow.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流输入是作为一个整体传递到流中的数据。通过指定名称和类型来定义输入架构。设置每个输入的输入值以测试流。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Flow input is the data passed into the flow as a whole. Defikkne the input schema by specifying the name and type. Set the input value of each input to test the flow.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.FlowOutput" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Flow output is the data produced by the flow as a whole, which summarizes the results of the flow execution. You can view and export the output table after the flow run or batch run is completed. Define flow output value by referencing the flow single step output.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流输出是流作为一个整体生成的数据，它总结了流执行的结果。可以在流运行或批处理运行完成后查看和导出输出表。通过引用流单步输出来定义流输出值。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Flow output is the data produced by the flow as a whole, which summarizes the results of the flow execution. You can view and export the output table after the flow run or bulk test is completed. Define flow output value by referencing the flow single step output.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.GenerateVariants" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[(Preview) Automatically generate more prompt variations with large language models based on the prompt you gave.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[(预览)根据你给出的提示，使用大型语言模型自动生成更多提示变体。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Automatically generate more prompt variations with large language models based on the prompt you gave.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.InputMapping" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To calculate metrics, evaluation requires some inputs. Please select the sources of these data. The source can be from this flow output, or from your dataset.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要计算指标，计算需要一些输入。请选择这些数据的源。源可以来自此流输出或数据集。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.LLM" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use OpenAI's Large Language Model for text completion or chat.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 OpenAI 的大型语言模型进行文本完成或聊天。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Use Open AI's Large Language Model for text completion or chat.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.LLM_New" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upgrade to latest version to consume new LLM tools. Use OpenAI's Large Language Model for text completion or chat.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[升级到最新版本以使用新的 LLM 工具。使用 OpenAI 的大型语言模型进行文本完成或聊天。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Upgrade to latest version to consume new LLM tools. Use Open AI's Large Language Model for text completion or chat.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.Prompt" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Craft prompt using Jinja as the templating language.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Jinja 作为模板化语言的制作提示。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.Prompt1" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[We offer a templating strategy that can help you create parametric prompts that accept different input values. Instead of fixed text, simply enclose your input name in {{}}, so it can be replaced on the fly. We use {Jinja} as our templating language.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[我们提供了一种模板化策略，可帮助你创建接受不同输入值的参数提示。只需将输入名称括在 {{}} 中，以便可以动态替换它，而不是固定文本。我们使用 {Jinja} 作为模板化语言。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.Prompt2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Do you want to improve your prompts? Check out our "{promptEngineeringGuidance}" document for tips and best practices.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[是否要改进提示? 有关提示和最佳做法，请查看我们的 "{promptEngineeringGuidance}" 文档。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.PromptEngineeringGuidance" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Prompt Engineering Guidance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提示工程指南]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.Python" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run Python code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行 Python 代码。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.RegenerateTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The tool interface is not up-to-date, please click 'Validate and parse input' button to regenerate.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[工具接口不是最新的，请单击“验证并分析输入”按钮重新生成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.SelectVariants" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You have set variants for following nodes. The flow will generate a separate run for each variant. You can select a node with variants to run. Or select "Use default variant for all nodes" to avoid generating separate runs, so the default variant of each node is used.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已为以下节点设置变体。流将为每个变体生成单独的运行。可以选择具有要运行的变体的节点。或者选择“对所有节点使用默认变体”以避免生成单独的运行，因此将使用每个节点的默认变体。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[You have set variants for following nodes. The flow will generate a separate run for each variant. You can select an node with variants to run. Or select "Use default variant for all nodes" to avoid generating separate runs, so the default variant of each node is used.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.SetAsAggregation" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[An aggregation node will take effect when the batch run is submitted. The aggregation node will receive and collect the output from each row of input data into a list, and process them together as a list at once.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提交批处理运行时，聚合节点将生效。聚合节点将接收每个输入数据行的输出并将其收集到列表中，并一次性将其作为列表一起处理。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[An aggregation node will take effect when the bulk test is submitted. The aggregation node will receive and collect the output from each row of input data into a list, and process them together as a list at once.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.ToolMoreDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[For more details, refer to [this document]5D;({doc-link})]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[有关更多详细信息，请参阅 [此文档]5D;({doc-link})]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[For more details, refer to]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.ToolNotFoundReason" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The code file could be moved or renamed.]A;Try regenerating the tool interface with advanced options.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可以移动或重命名代码文件。]A;请尝试使用高级选项重新生成工具接口。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.ToolNotFoundTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Can't find tool {toolId}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到工具 {toolId}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.Tune" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tune LLM with variants.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用变体优化 LLM。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Tooltips.TypeScript" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run TypeScript code.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行 TypeScript 代码。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TotalTokens" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Total tokens]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[令牌总数]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.Database_Creating" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Database is creating, please wait...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[正在创建数据库，请稍候...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.Database_Refresh_Status" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to refresh status]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击以刷新状态]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.Error.LostFlowId" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Lost flow id]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流 ID 丢失]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.ListPage.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trace list]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跟踪列表]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Tracing]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跟踪]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Trace]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.Search" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Search]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[搜索]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.SessionPage.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trace session]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跟踪会话]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.Share" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Share Link]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[共享链接]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.Streaming" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Streaming]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[流式处理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.Trace_Details" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Trace Details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[跟踪详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.View_Only_My_Traces" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View only my traces]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仅查看我的跟踪]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace.View_Trace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View trace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看跟踪]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace_Collapse" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Collapse]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[折叠]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Trace_Expand" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Expand]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[展开]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Type" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Type]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[类型]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Update" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Update]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[更新]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Upgrade" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upgrade]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[升级]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";UpgradeFlow" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upgrade flow]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[升级流]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";UpgradeFlowTips" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[To continue editing this flow, please click the 'Upgrade' button to store the flow in the file share storage as our recent update. This new feature provides a YAML definition for your flow, promoting a code-first experience to flow development.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[若要继续编辑此流，请单击“升级”按钮，将此流作为最近的更新存储在文件共享存储中。此新功能为你的流提供 YAML 定义，从而提升流程开发的代码优先体验。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Please upgrade your flow to edit.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Upload" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上传]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";UploadFrom" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Upload from]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[上传自]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";UploadFromPlaceholder" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select folder...]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择文件夹...]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";UploadZipFile" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Zip file]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Zip 文件]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Upload zip file]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validate_And_Parse_Failed" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validate and parse input failed]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法验证和分析输入]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.AutoParseInputError_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Parse input failed at step "{nodeName}{variantName}": {msg}]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在步骤“{nodeName}{variantName}”中分析输入失败: {msg}]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.CircularDependency_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There is a circular dependency]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[存在循环依赖]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.Duplicate_Third_Party_Dependencies_Name" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Duplicate third party dependency name found.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[发现重复的第三方依赖项名称。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.EmptyCode_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empty "{inputOrPrompt}" at step: "{nodeName}{variantName}"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[步骤 "{nodeName}{variantName}" 处的 "{inputOrPrompt}" 为空]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Empty "{inputOrPrompt}" at step: "{nodeName}"]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.EmptyConfig_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empty config: "{config}" at step: "{nodeName}{variantName}"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[步骤 "{nodeName}{variantName}" 处的配置 "{config}" 为空]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Empty config: "{config}" at step: "{nodeName}"]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.EmptyInput_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empty input: "{parameterName}" at step: "{nodeName}{variantName}"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[步骤 "{nodeName}{variantName}" 处的输入 "{parameterName}" 为空]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Empty input: "{parameterName}" at step: "{nodeName}"]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.EmptyRuntimeName" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Empty runtime, please create a runtime and use it.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行时为空，请创建运行时并使用它。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.FlowInputTypeError_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Unable to parse input "{inputName}" with value "{value}" as type "{inputType}".]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法将值为“{value}”的输入“{inputName}”分析为类型“{inputType}”。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.InputDependencyNotFound_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Not found dependency node: "{referenceNodeName}" in input: "{parameterName}" at step: "{nodeName}"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[在步骤: "{nodeName}" 的输入: "{parameterName}" 中找不到依赖项节点: "{referenceNodeName}"]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.InputInvalidType_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Invalid value type input: "{parameterName}" at step: "{nodeName}{variantName}"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[步骤 "{nodeName}{variantName}" 处的值类型输入 "{parameterName}" 无效]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Invalid value type input: "{parameterName}" at step: "{nodeName}"]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.MeidTokenUnsupported_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{connectionName} is token-based connection which is not supported in the selected runtime. Please consider updating runtime to the latest version or opting for key-based connection.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{connectionName} 是基于令牌的连接，在所选运行时中不受支持。请考虑将运行时更新到最新版本或选择基于密钥的连接。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.MeidTokenUnsupported_ErrorMessage_2" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[{connectionName} are token-based connections which is not supported in the selected runtime. Please consider updating runtime to the latest version or opting for key-based connections.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[{connectionName} 是基于令牌的连接，在所选运行时中不受支持。请考虑将运行时更新到最新版本或选择基于密钥的连接。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.Missing_Code_File_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The file you referenced cannot be found! To resolve this issue, you can either add the file content inline directly to create a new file in the File list, or upload the missing file there.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到你引用的文件！要解决此问题，可以直接内联添加该文件内容以在文件列表中创建新文件，也可以上传缺少的文件。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.OldRuntimeWithNewLLM_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Current runtime is not supported for node "{newLLMNodeName}". Please upgrade to latest version to consume new LLM tools.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[节点“{newLLMNodeName}”不支持当前运行时。请升级到最新版本以使用新的 LLM 工具。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.Run_Anyway" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Run anyway]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[仍要运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.UnparsedCode_ErrorMessage" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[There are new code changes need to be parsed at step: "{nodeName}{variantName}"]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[需要在步骤 "{nodeName}{variantName}" 处分析新的代码更改]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[There are new code changes need to be parsed at step: "{nodeName}"]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Validation.Validation_Warning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Validation Warning]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[验证警告]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Value" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Value]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.Action.Add" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Add new variant]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[添加新变量]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.Action.Duplicate" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Clone]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[克隆]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Duplicate]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.Action.FinishTuning" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Hide variants]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[隐藏变体]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Finish tuning]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.Action.GenerateVariants" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Generate variants]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成变体]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.Action.SetAsDefault" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Set as default]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[设置为默认值]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.Action.Tune" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Show variants]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[显示变体]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Tune]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.Default" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Default]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[默认]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.RunPanel.GuideTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select the LLM node with variants that you want to run.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择包含要运行的变量的 LLM 节点。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.RunPanel.RunModeLabel_DefaultNodes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Use default variant for all nodes]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对所有节点使用默认变体]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.RunPanel.RunModeLabel_TuningNodes" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Select a node to run variants]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[选择要运行变量的节点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.RunPanel.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Submit flow run]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[提交流运行]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Variant.Tooltip.Tune" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[You can only tune one LLM step at one time. Please click finish tune on the unfinished step before tuning another LLM step.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[一次只能优化一个 LLM 步骤。在优化另一个 LLM 步骤之前，请在未完成的步骤上单击“完成优化”。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";VariantID" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Variant ID]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[变体 ID]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";VariantsSummary" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Variants summary]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[变量摘要]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewConnectionDetails" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View connection details]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看连接详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewDAG" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View DAG]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看 DAG]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewDAG_Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[DAG view]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[DAG 视图]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewEndpoint" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View endpoint]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看终结点]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewFullContent" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View full content]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看完整内容]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewLatestBatchRun" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View latest batch run outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看最新的批处理运行输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewLogs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View logs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看日志]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewMore" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看更多]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewOutputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewRunHistory" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View batch runs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看批运行]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[View bulk runs]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewTestResults" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View test results]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看测试结果]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ViewTrace" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View trace]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看跟踪]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";VisualizeOutputs" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Visualize outputs]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[可视化输出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";VisualizeOutputs_EvaluateOnData" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate on]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";VisualizeOutputs_EvaluateOnRun" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Evaluate on the output of]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[对以下项的输出进行评估]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";VisualizeOutputs_RunsMetrics" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Runs & metrics]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[运行和指标]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";VisualizeOutputs_ShareTooltip" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Click to instantly copy the current page's URL for easy sharing.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[单击以立即复制当前页面的 URL 以便轻松共享。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>
﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="D:\a\_work\1\s\extensions\governance\src\locstrings\en-us.json" PsrId="306" FileType="1" SrcCul="en-US" TgtCul="zh-CN" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="Rccx" />
  </OwnedComments>
  <Settings Name="@SettingsPath@\default.lss" Type="Lss" />
  <Item ItemId=";String Table" ItemType="0" PsrId="306" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
    <Item ItemId=";Strings" ItemType="0" PsrId="306" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";Governance.BreadCrumb" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Governance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[治理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card1.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Streamline AI governance with Azure AI Foundry by integrating evaluation tools with top governance platforms. These integrations allow you to define, execute, and monitor AI risk and compliance workflows seamlessly, without affecting developer productivity.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[通过将评估工具与顶级治理平台集成，使用 Azure AI Foundry 简化 AI 治理。通过这些集成，可以无缝定义、执行和监视 AI 风险和合规性工作流，而不会影响开发人员的工作效率。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card1.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Governance]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[治理]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card2.ButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to Purview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[转到 Purview]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card2.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Assess and manage compliance for your AI apps with Microsoft Purview Compliance Manager. It translates AI regulations, such as the EU AI Act, into actionable suggestions that you can implement in Azure AI Foundry to run your AI evaluations. Upload the results back to Microsoft Purview Compliance Manager assessments for a comprehensive view of your regulatory posture, ensuring you stay current with regulations and certifications, and can report effectively to auditors.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Microsoft Purview 合规性管理器评估和管理 AI 应用的合规性。它将 AI 法规(如欧盟 AI 法案)转换为可在 Azure AI Foundry 中实施的可操作建议，以运行 AI 评估。将结果上传回 Microsoft Purview 合规性管理器评估，以全面了解你的合规状况，从而确保你掌握法规和认证的最新信息，并可以有效地向审核员报告。]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Assess and manage compliance for your AI apps with Microsoft Purview Compliance Manager. It translates AI regulations, such as the EU AI Act, into actionable suggestions that you can implement in Azure AI Foundry to run your AI evaluations. Upload the results back to Compliance Manager assessments for a comprehensive view of your regulatory posture, ensuring you stay current with regulations and certifications, and can report effectively to auditors.]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card2.LinkLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card2.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Purview]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Purview]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Purview Compliance Manager]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card3.ButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Go to Credo AI]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[转到 Credo AI]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Go to Credo]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card3.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Define evaluation requirements for AI projects with the Credo AI integration for Azure AI Foundry. Governance teams can set project-specific and compliance-based requirements, which are then converted into executable code for developers to run Evaluators directly in Foundry. This closed-loop approach removes traditional bottlenecks and ensures AI systems meet compliance standards from the start.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用适用于 Azure AI Foundry 的 Credo AI 集成定义 AI 项目的评估要求。治理团队可以设置项目特定和基于合规性的要求，然后可将其转换为可执行代码，以便开发人员直接在 Foundry 中运行评估程序。这种闭环方法消除了传统的瓶颈，并确保 AI 系统从一开始就满足合规性标准。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card3.LinkLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card3.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Credo AI integration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Credo AI 集成]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Credo integration]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card4.ButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Get Saidot]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[获取 Saidot]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card4.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Connect your Azure model registry to govern AI models and agents in Saidot with the Microsoft Azure AI Foundry integration. Generate evaluation plans based on risk profiles, including red teaming and simulated datasets. Run evaluations in Azure, review results in Saidot, and report to risk and compliance—all in one streamlined workflow.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用 Microsoft Azure AI Foundry 集成连接 Azure 模型注册表以在 Saidot 中治理 AI 模型和智能体。基于风险配置文件生成评估计划，包括红队测试和模拟数据集。在 Azure 中运行评估，在 Saidot 中查看结果，并报告风险与合规性 - 所有工作均在一个简化的工作流中完成。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card4.LinkLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card4.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Saidot integration]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Saidot 集成]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card5.ButtonLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[View Apps]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[查看应用]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card5.Description" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Foundry system-assigned managed identities are now automatically labeled as Microsoft Entra Agent IDs.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[现在，Foundry 系统分配的托管标识自动标记为 Microsoft Entra 代理 ID。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card5.LinkLabel" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Learn more]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[了解详细信息]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.Card5.Title" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Microsoft Entra]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[Microsoft Entra]]></Val>
          </Tgt>
          <Prev Cat="Text">
            <Val><![CDATA[Microsoft Entra Agent ID]]></Val>
          </Prev>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.ComingSoon" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Coming Soon]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[即将推出]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";Governance.PageTitle" ItemType="0" PsrId="306" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[AI development with a governance-first approach]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用治理优先的方法进行 AI 开发]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
</LCX>